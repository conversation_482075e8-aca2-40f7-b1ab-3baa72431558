<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1.1" author="anton.podoprygolov">
        <sql><![CDATA[
            create table if not exists chart_desktop
            (
                id          bigserial
                constraint chart_desktop_pk
                primary key,
                owner       varchar not null,
                name        varchar not null,
                create_date date    not null,
                update_date date,
                constraint owner_name_unique
                unique (owner, name)
                );
        ]]></sql>
    </changeSet>
    <changeSet id="2.2" author="anton.podoprygolov">
        <sql><![CDATA[
            create table if not exists chart_widget
            (
                id          bigserial
                constraint chart_widet_pk
                primary key,
                desk_id     bigint                                                 not null
                constraint chart_widet_chart_desktop_null_fk
                references chart_desktop,
                date_from   date,
                date_to     date,
                date_span   varchar,
                mvz         varchar,
                settings    varchar                                                not null,
                create_date date                                                   not null,
                update_date date,
                location    varchar                                                not null,
                name        varchar                                                not null,
                type        varchar                                                not null
                );
            ]]></sql>
    </changeSet>
</databaseChangeLog>
