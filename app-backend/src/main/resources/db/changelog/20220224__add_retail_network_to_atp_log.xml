<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="igor.belogolovsky">
        <sql>
            <![CDATA[
            alter table if exists atp_log
            drop column if exists retail_network;
            alter table if exists atp_log
                add column if not exists retail_network varchar default '';

            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Аренда ЦФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Великий Новгород';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Великий Новгород 20тн СТ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Воронеж';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Воронеж 20 тн';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Воронеж БФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Восток ЦФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Екатеринбург 20тн СТ';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Екатеринбург БФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Екатеринбург УФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Елабуга';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Казань';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Киров';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Краснодар';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Краснодар 20тн';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Краснодар БФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Кузнецк';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Курск';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Аренда';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП ВВФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Вел.Новгород';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Екатеринбург';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП ЕКБ (Эстафета)';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Казань';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Казань МД';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Краснодар';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП (найм)';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП НН МД';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Пермь';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Ростов';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Самара';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Самара МД';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Саратов';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Уфа';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП ЦФ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП ЦФ МД';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП МФП Челябинск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Невинномысск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Нижний Новгород';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Нижний Новгород 20тн НТ';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Нижний Новгород БФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Новосиб.20тн СТ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Новосибирск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Орел';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Оренбург';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Пермь УФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Петрозаводск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Петрозаводск 20тн СТ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Резерв.кол ЦФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Ростов';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Рязань';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Самара';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Самара БФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Санкт-Петербург';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Санкт-Петербург БФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Саратов';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Север';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Север Перекрест';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП СЗФ 20тн НТ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Сибирь найм';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Софьино';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП СПб 20тн СТ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Тюмень';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Тюмень 20тн СТ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП УФ 20тн НТ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Уфа';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Уфа 20тн СТ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП ЦФ Ком.кол';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Челябинск УФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Члб 20тн СТ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП ЮГ ЦФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Астрахань';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Волгоград';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Вологда';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Воркута';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Ижевск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Казань';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Калининград';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Красноярск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Лайт Вел. Луки';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Лайт Выш. Волочек';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Лайт Оленегорск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Лайт Тихвин';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Мурманск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Нижневартовск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Омск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Печора';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Пятигорск';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Ростов ТСХ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Саратов';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Сосногорск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Сочи';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК СПб МФП(Пулково)';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Сургут';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Сыктывкар';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Усинск';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Ухта';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Югорск';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Лизинг  (ТС покупка)';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорегион Волга';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорегион Москва';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорегион Сибирь';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорегион Урал';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорегион Юг';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорег.СевероЗапад';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Макрорег.Центр (ЦЧФ)';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'ПСГ Краснодар';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв АТП Восток ЦФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв АТП Рязань';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв АТП Север';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв АТП ЮГ ЦФ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв ТС Волга';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв ТС Сев-Запад';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв ТС Урал';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв ТС Центр';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв ТС Юг';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'УТ МФП МД (найм)';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'УТ МФП СЗФ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'УТ МФП ЦО СТ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'УТ РемЗона ЦФ';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'ЦО';
            update atp_log set retail_network = 'нет' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Экспедирование МФП';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'Резерв ТС Сибирь';
            update atp_log set retail_network = 'Перекресток' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Мурманск ТСХ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Ярославль';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'АТП Ярославль 20тн СТ';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Коряжма';
            update atp_log set retail_network = 'Пятерочка' from (select id, name from atp) as atp where atp_log.atp_id = atp.id and atp.name = 'КДК Архангельск';
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>