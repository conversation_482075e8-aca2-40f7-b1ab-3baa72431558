<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="anton.podoprygolov">
        <sql>
            <![CDATA[
            create table if not exists nh_plan
            (
                id         bigserial
                primary key,
                vehicle_id bigint                not null
                constraint vehicle_fk
                references nh_vehicles,
                start_date date                  not null,
                plan_nh    double precision      not null,
                created_by varchar               not null,
                created_at date                  not null,
                updated_by varchar,
                updated_at date,
                deleted    boolean default false not null
            );
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>