<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="189243.1" author="denis.berestinsky">
        <sql><![CDATA[
            create or replace view hr_place_tplace_log_view(
                p_id,
                tp_id,
                start_date,
                end_date,
                created_at,
                created_by,
                updated_at,
                updated_by
            ) as select
                p_id,
                tp_id,
                start_date,
                lead(start_date - 1) over (partition by p_id order by start_date),
                created_at,
                created_by,
                updated_at,
                updated_by
            from hr_places_tplaces_log
            where not deleted;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
