--liquibase formatted sql

--changeset igor.belog<PERSON><PERSON>@x5.ru:2167332_2

drop table if exists atp_test;
drop table if exists constructor_meta_aggregating_fields;
drop table if exists hr_org_unit_name;
drop table if exists test_include_all;
drop table if exists test_table;
drop table if exists test_table_child;
drop table if exists ts_org_unit;
drop table if exists ts_org_unit_name;
drop view if exists chart_filter_repair_view;
drop view if exists chart_repair_stats_view;
drop view if exists chart_ts_number_joined_view;
drop view if exists chart_ts_number_view;
drop view if exists hr_mechanic_timeline;
drop view if exists kip_dictionary_view;
drop view if exists nh_plan_dictionary_view_new;
drop view if exists nh_timeline;
drop view if exists nh_timeline_org_units_view;
drop view if exists repair_base_properties_view;
drop view if exists repair_chart_view;
drop view if exists repshop_performance_plan_timeline_view;
drop materialized view if exists organizational_units_timeline_test;
drop materialized view if exists vehicle_region_hr_timeline;

--rollback create table atp_test (     id             bigserial,     name           varchar,     repair_name    varchar,     type           varchar,     retail_network varchar,     created_at     timestamp,     created_by     varchar,     updated_at     timestamp,     updated_by     varchar,     deleted        boolean default false);
--rollback create table constructor_meta_aggregating_fields (     api_name     varchar not null     primary key,     display_name varchar not null,     expression   varchar not null );
--rollback create table dict_groupings (     id       serial     primary key,     name     varchar not null,     sys_name varchar not null,     ordering integer,     constraint names_groupings     unique (name, sys_name) ); 
--rollback create table hr_org_unit_name (     org_unit_id        integer not null,     org_unit_name      varchar,     org_unit_full_path varchar );  
--rollback create table test_include_all ( ); 
--rollback create table test_table (     id         serial     constraint test_table_pk     primary key,     name       varchar,     start_date date,     fk         integer default 1 not null     constraint test_table_test_table_child_id_fk     references test_table_child ); 
--rollback create table test_table_child (     id serial     constraint test_table_child_pk     primary key ); 
--rollback create table ts_org_unit (     id            integer not null,     org_unit_id   integer,     cost_point_id varchar ); 
--rollback create table ts_org_unit_name (     org_unit_id   integer not null,     org_unit_name varchar ); 
--rollback create view chart_filter_repair_view (order_id, start_date, repair_atp, repair_kind, repair_place, repair_year, month, week, month_year,     week_year, report_week_year, quarter_year, day_month_year, vrt, vrt_name, event_id, event_text,     repair_type, repair_type_name, repair_subtype, repair_subtype_name, equnr, vehicle_licence,     vehicle_tonnage, vehicle_brand, vehicle_model, vehicle_type, vehicle_group, vehicle_create_year,     vehicle_gbo, repair_expenses, part_amount, part_expenses, services_amount, services_expenses) as SELECT r.order_id,        r.repair_start_date                                                                                         AS start_date,        COALESCE(r.final_repshop_name, 'N/ A'::character varying)                                                   AS repair_atp,        COALESCE(r.repair_kind, 'N/ A'::character varying)                                                          AS repair_kind,        COALESCE(                CASE                    WHEN r.repair_kind::text = ANY (ARRAY ['X540'::text, 'X542'::text, 'X543'::text])                        THEN 'Ремзона'::character varying                    WHEN r.repair_kind::text = ANY (ARRAY ['X541'::text, 'X544'::text]) THEN 'СТО'::character varying                    ELSE r.repair_kind                    END,                'N/ A'::character varying)                                                                          AS repair_place,        COALESCE(date_part('year'::text, r.repair_start_date)::text,                 'N/ A'::text)                                                                                      AS repair_year,        COALESCE(date_part('month'::text, r.repair_start_date)::text,                 'N/ A'::text)                                                                                      AS month,        COALESCE(date_part('week'::text, r.repair_start_date)::text,                 'N/ A'::text)                                                                                      AS week,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, 'MM. YYYY'::text),                 'N/ A'::text)                                                                                      AS month_year,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, '"Н "IW. IYYY'::text),                 'N/ A'::text)                                                                                      AS week_year,        COALESCE(to_char((r.repair_start_date + 3)::timestamp with time zone, '"ОН "IW. IYYY'::text),                 'N/ A'::text)                                                                                      AS report_week_year,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, '"Кв "Q. YYYY'::text),                 'N/ A'::text)                                                                                      AS quarter_year,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, 'DD. MM. YYYY'::text),                 'N/ A'::text)                                                                                      AS day_month_year,        COALESCE(r.vrt, 'N/ A'::character varying)                                                                  AS vrt,        COALESCE(COALESCE(r.vrt_name, ('Код ВРТ - '::text || r.vrt::text)::character varying),                 'N/ A'::character varying)                                                                         AS vrt_name,        COALESCE(r.event_id, 'N/ A'::character varying)                                                             AS event_id,        COALESCE(r.event_text, 'N/ A'::character varying)                                                           AS event_text,        COALESCE(r.repair_type_id::text, 'N/ A'::text)                                                              AS repair_type,        COALESCE(twt.name, 'N/ A'::character varying)                                                               AS repair_type_name,        COALESCE(r.repair_subtype_id::text, 'N/ A'::text)                                                           AS repair_subtype,        COALESCE(tws.name, 'N/ A'::character varying)                                                               AS repair_subtype_name,        r.equnr,        COALESCE(r.ts_license_num, 'N/ A'::character varying)                                                       AS vehicle_licence,        COALESCE(to_char(r.ts_load_wgt, 'FM90D0'::text), 'N/ A'::text)                                              AS vehicle_tonnage,        COALESCE(r.ts_marka, 'N/ A'::character varying)                                                             AS vehicle_brand,        COALESCE(r.ts_model, 'N/ A'::character varying)                                                             AS vehicle_model,        COALESCE(r.ts_type, 'N/ A'::character varying)                                                              AS vehicle_type,        COALESCE(r.ts_group, 'N/ A'::character varying)                                                             AS vehicle_group,        COALESCE(date_part('year'::text, r.ts_create_date)::text,                 'N/ A'::text)                                                                                      AS vehicle_create_year,        CASE            WHEN r.ts_gbo = true THEN 'С ГБО'::text            ELSE 'Без ГБО'::text            END                                                                                                     AS vehicle_gbo,        r.repair_expenses,        r.part_amount,        r.part_expenses,        r.services_amount,        r.services_expenses FROM repair r          LEFT JOIN toro_works tw ON r.vrt::text = tw.id::text          LEFT JOIN toro_works_types twt ON tw.subtype_id = twt.id          LEFT JOIN toro_works_subtypes tws ON tw.subtype_id = tws.id;
--rollback create view chart_repair_stats_view (mr_id, atp_id, mvz_id, vehicle_date, mvz_name, atp_name, mr_name, ts_license_num, ts_marka, ts_model,     ts_type, ts_group, ts_gbo, ts_load_wgt, ts_create_year, mileage, retail_network, atp_type, mvz_type,     vehicle_date_year, vehicle_date_month, vehicle_date_week, month_year, week_year) as SELECT repair_stats.mr_id,        repair_stats.atp_id,        repair_stats.mvz_id,        repair_stats.vehicle_date,        repair_stats.mvz_name,        repair_stats.atp_name,        repair_stats.mr_name,        repair_stats.ts_license_num,        repair_stats.ts_marka,        repair_stats.ts_model,        repair_stats.ts_type,        repair_stats.ts_group,        repair_stats.ts_gbo,        repair_stats.ts_load_wgt,        date_part('year'::text, repair_stats.ts_create_date) AS ts_create_year,        repair_stats.mileage,        repair_stats.retail_network,        repair_stats.atp_type,        repair_stats.mvz_type,        date_part('year'::text, repair_stats.vehicle_date)   AS vehicle_date_year,        date_part('month'::text, repair_stats.vehicle_date)  AS vehicle_date_month,        date_part('week'::text, repair_stats.vehicle_date)   AS vehicle_date_week,        (date_part('month'::text, repair_stats.vehicle_date) || '.'::text) ||        date_part('year'::text, repair_stats.vehicle_date)   AS month_year,        (('Н '::text || date_part('week'::text, repair_stats.vehicle_date)) || '.'::text) ||        CASE            WHEN date_part('month'::text, repair_stats.vehicle_date) = 1::double precision AND                 (date_part('week'::text, repair_stats.vehicle_date) = ANY                  (ARRAY [52::double precision, 53::double precision])) THEN                date_part('year'::text, repair_stats.vehicle_date) - 1::double precision            WHEN date_part('month'::text, repair_stats.vehicle_date) = 12::double precision AND                 date_part('week'::text, repair_stats.vehicle_date) = 1::double precision THEN                date_part('year'::text, repair_stats.vehicle_date) + 1::double precision            ELSE date_part('year'::text, repair_stats.vehicle_date)            END                                              AS week_year FROM repair_stats; 
--rollback create view chart_ts_number_joined_view (id, equnr, vrt_start_date, vrt_end_date, vrt_inactive_date, territory_id, territory_name, vrt_mr_id,     mr_name, vrt_atp_id, vrt_atp_type, atp_name, vrt_mvz_id, vrt_retail_network, vrt_mvz_name, vrt_mvz_type,     repshop_id, repshop_name, td_create_date_year, td_inactive_day, td_deleted_day, td_truck_type, td_ts_type,     td_marka, td_model, td_load_wgt, td_gbo, td_license_num, td_create_date, vrt_start_date_year,     vrt_start_date_month, vrt_start_date_week, quarter_year, month_year, week_year, report_week_year,     day_month_year) as SELECT vrt.ctid                                                                                             AS id,        vrt.equnr,        vrt.start_date                                                                                       AS vrt_start_date,        vrt.end_date                                                                                         AS vrt_end_date,        vrt.inactive_date                                                                                    AS vrt_inactive_date,        o.territory_id,        COALESCE(o.territory_name, 'N/A'::character varying)                                                 AS territory_name,        o.mr_id                                                                                              AS vrt_mr_id,        COALESCE(o.mr_name, 'N/A'::character varying)                                                        AS mr_name,        o.atp_id                                                                                             AS vrt_atp_id,        COALESCE(o.atp_type, 'N/A'::character varying)                                                       AS vrt_atp_type,        COALESCE(o.atp_name, 'N/A'::character varying)                                                       AS atp_name,        COALESCE(o.mvz_id, 'N/A'::character varying)                                                         AS vrt_mvz_id,        COALESCE(o.retail_network, 'N/A'::character varying)                                                 AS vrt_retail_network,        COALESCE(o.mvz_name, 'N/A'::character varying)                                                       AS vrt_mvz_name,        COALESCE(o.mvz_type, 'N/A'::character varying)                                                       AS vrt_mvz_type,        o.repshop_id,        COALESCE(o.repshop_name, 'N/A'::character varying)                                                   AS repshop_name,        COALESCE(date_part('year'::text, td.create_date)::text,                 'N/A'::text)                                                                                AS td_create_date_year,        td.inactive_day                                                                                      AS td_inactive_day,        td.deleted_day                                                                                       AS td_deleted_day,        td.truck_type                                                                                        AS td_truck_type,        COALESCE(td.ts_type, 'N/A'::character varying)                                                       AS td_ts_type,        COALESCE(td.marka, 'N/A'::character varying)                                                         AS td_marka,        COALESCE(td.model, 'N/A'::character varying)                                                         AS td_model,        COALESCE(td.load_wgt, 0.0::real)                                                                     AS td_load_wgt,        CASE            WHEN td.gbo THEN 'С ГБО'::text            ELSE 'Без ГБО'::text            END                                                                                              AS td_gbo,        COALESCE(td.license_num, 'N/A'::character varying)                                                   AS td_license_num,        td.create_date                                                                                       AS td_create_date,        COALESCE(date_part('year'::text, vrt.start_date)::text,                 'N/A'::text)                                                                                AS vrt_start_date_year,        COALESCE(date_part('month'::text, vrt.start_date)::text,                 'N/A'::text)                                                                                AS vrt_start_date_month,        COALESCE(date_part('week'::text, vrt.start_date)::text,                 'N/A'::text)                                                                                AS vrt_start_date_week,        COALESCE(to_char(vrt.start_date::timestamp with time zone, '"Кв "Q.YYYY'::text),                 'N/A'::text)                                                                                AS quarter_year,        COALESCE(to_char(vrt.start_date::timestamp with time zone, 'MM.YYYY'::text),                 'N/A'::text)                                                                                AS month_year,        COALESCE(to_char(vrt.start_date::timestamp with time zone, '"Н "IW.IYYY'::text),                 'N/A'::text)                                                                                AS week_year,        COALESCE(to_char((vrt.start_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text),                 'N/A'::text)                                                                                AS report_week_year,        COALESCE(to_char(vrt.start_date::timestamp with time zone, 'DD.MM.YYYY'::text),                 'N/A'::text)                                                                                AS day_month_year FROM vehicle_region_timeline vrt          JOIN ts_data td ON vrt.equnr = td.equnr          LEFT JOIN organizational_units_timeline o                    ON vrt.start_date >= o.start_date AND vrt.start_date < o.end_date AND vrt.mvz_id::text = o.mvz_id::text WHERE td.ts_type IS NOT NULL; 
--rollback create view chart_ts_number_view (id, equnr, vrt_start_date, vrt_end_date, vrt_inactive_date, vrt_mr_id, mr_name, vrt_atp_id, vrt_atp_type,     atp_name, vrt_mvz_id, vrt_retail_network, vrt_mvz_name, vrt_mvz_type, td_create_date_year, td_inactive_day,     td_deleted_day, td_truck_type, td_ts_type, td_marka, td_model, td_load_wgt, td_gbo, td_license_num,     td_create_date, vrt_start_date_year, vrt_start_date_month, vrt_start_date_week, month_year, week_year) as SELECT vrt.ctid                                                                                           AS id,        vrt.equnr,        vrt.start_date                                                                                     AS vrt_start_date,        vrt.end_date                                                                                       AS vrt_end_date,        vrt.inactive_date                                                                                  AS vrt_inactive_date,        vrt.mr_id                                                                                          AS vrt_mr_id,        mr.name                                                                                            AS mr_name,        vrt.atp_id                                                                                         AS vrt_atp_id,        vrt.atp_type                                                                                       AS vrt_atp_type,        atp.name                                                                                           AS atp_name,        vrt.mvz_id                                                                                         AS vrt_mvz_id,        vrt.retail_network                                                                                 AS vrt_retail_network,        vrt.mvz_name                                                                                       AS vrt_mvz_name,        vrt.mvz_type                                                                                       AS vrt_mvz_type,        date_part('year'::text, td.create_date)                                                            AS td_create_date_year,        td.inactive_day                                                                                    AS td_inactive_day,        td.deleted_day                                                                                     AS td_deleted_day,        td.truck_type                                                                                      AS td_truck_type,        td.ts_type                                                                                         AS td_ts_type,        td.marka                                                                                           AS td_marka,        td.model                                                                                           AS td_model,        td.load_wgt                                                                                        AS td_load_wgt,        CASE            WHEN td.gbo THEN 'С ГБО'::text            ELSE 'Без ГБО'::text            END                                                                                            AS td_gbo,        td.license_num                                                                                     AS td_license_num,        td.create_date                                                                                     AS td_create_date,        date_part('year'::text, vrt.start_date)                                                            AS vrt_start_date_year,        date_part('month'::text, vrt.start_date)                                                           AS vrt_start_date_month,        date_part('week'::text, vrt.start_date)                                                            AS vrt_start_date_week,        (date_part('month'::text, vrt.start_date) || '.'::text) || date_part('year'::text, vrt.start_date) AS month_year,        (('Н '::text || date_part('week'::text, vrt.start_date)) || '.'::text) ||        CASE            WHEN date_part('month'::text, vrt.start_date) = 1::double precision AND                 (date_part('week'::text, vrt.start_date) = ANY (ARRAY [52::double precision, 53::double precision]))                THEN date_part('year'::text, vrt.start_date) - 1::double precision            WHEN date_part('month'::text, vrt.start_date) = 12::double precision AND                 date_part('week'::text, vrt.start_date) = 1::double precision                THEN date_part('year'::text, vrt.start_date) + 1::double precision            ELSE date_part('year'::text, vrt.start_date)            END                                                                                            AS week_year FROM vehicle_region_timeline vrt          JOIN ts_data td ON vrt.equnr = td.equnr          LEFT JOIN macro_region mr ON mr.id = vrt.mr_id          LEFT JOIN atp ON atp.id = vrt.atp_id WHERE td.ts_type IS NOT NULL; 
--rollback create view hr_mechanic_timeline(day, rate, personnel_no, repshop_id) as WITH period AS (SELECT date_part('YEAR'::text, p_1.day)  AS year,                        date_part('MONTH'::text, p_1.day) AS month,                        p_1.day                 FROM (SELECT generate_series(((SELECT min(hr_mechanic_full.from_dttm) AS min                                                FROM hr_mechanic_full))::timestamp with time zone,                                              ((SELECT max(hr_mechanic_full.to_dttm) AS max                                                FROM hr_mechanic_full))::timestamp with time zone,                                              '1 day'::interval)::date AS day) p_1) SELECT p.day,        COALESCE(h.rate, 0::double precision) AS rate,        h.personnel_no,        ou.repshop_id FROM period p          LEFT JOIN hr_mechanic_full h ON p.day >= h.from_dttm AND p.day < h.to_dttm          JOIN organizational_units_timeline ou               ON h.mvz_id::text = ou.mvz_id::text AND ou.start_date <= p.day AND p.day < ou.end_date; 
--rollback create view kip_dictionary_view (mvz_id, mvz_name, atp_id, macro_region_id, tonnage, start_date, end_date, percentage, author) as SELECT kdm.mvz_id,        ml.name                                                                                      AS mvz_name,        ml.atp_id,        al.mr_id                                                                                     AS macro_region_id,        kdm.tonnage,        kdm.start_date,        lead(kdm.start_date - 1) OVER (PARTITION BY kdm.mvz_id, kdm.tonnage ORDER BY kdm.start_date) AS end_date,        kdm.percentage,        kdm.author FROM kip_dictionary_by_mvz kdm          JOIN mvz_log_view ml ON ml.uid::text = kdm.mvz_id::text AND ml.start_date <= kdm.start_date AND                                  (ml.end_date IS NULL OR ml.end_date >= kdm.start_date)          JOIN atp_log_view al ON al.atp_id = ml.atp_id AND al.start_date <= kdm.start_date AND                                  (al.end_date IS NULL OR al.end_date >= kdm.start_date) UNION ALL SELECT ''::character varying                                                                        AS mvz_id,        NULL::character varying                                                                      AS mvz_name,        kda.atp_id,        al.mr_id                                                                                     AS macro_region_id,        kda.tonnage,        kda.start_date,        lead(kda.start_date - 1) OVER (PARTITION BY kda.atp_id, kda.tonnage ORDER BY kda.start_date) AS end_date,        kda.percentage,        kda.author FROM kip_dictionary_by_atp kda          JOIN atp_log_view al ON al.atp_id = kda.atp_id AND al.start_date <= kda.start_date AND                                  (al.end_date IS NULL OR al.end_date >= kda.start_date); 
--rollback create view nh_plan_dictionary_view_new (id, vehicle_id, type, brand, tonnage, create_year, plan_nh, start_date, end_date, author, deleted) as WITH dict AS (SELECT np.id,                      np.vehicle_id,                      np.start_date,                      np.plan_nh,                      np.created_by,                      np.created_at,                      np.updated_by,                      np.updated_at,                      np.deleted               FROM nh_plan np               WHERE np.deleted = false                  OR np.deleted IS NULL) SELECT dict.id,        nv.id                                                                   AS vehicle_id,        nv.type,        nv.brand,        nv.vehicle_tonnage                                                      AS tonnage,        nv.vehicle_create_year                                                  AS create_year,        dict.plan_nh,        dict.start_date,        NULL::text                                                              AS end_date,        COALESCE(dict.updated_by, dict.created_by, 'system'::character varying) AS author,        dict.deleted FROM dict          RIGHT JOIN nh_vehicles nv ON nv.id = dict.vehicle_id; 
--rollback create view nh_timeline(one_day, equnr, repshop_id, nh_daily_value) as WITH dict AS (SELECT nh_plan.vehicle_id,                      nh_plan.plan_nh,                      nh_plan.start_date,                      lead(nh_plan.start_date, 1, '9999-12-31'::date)                      OVER (PARTITION BY nh_plan.vehicle_id ORDER BY nh_plan.start_date) AS end_date               FROM nh_plan) SELECT m.one_day,        m.equnr,        m.repshop_id,        COALESCE(d.plan_nh, 0::double precision) /        date_part('days'::text, m.one_day + '1 mon'::interval - m.one_day::timestamp without time zone) AS nh_daily_value FROM monitoring m          JOIN nh_vehicles v ON v.type::text = m.ts_type::text AND v.brand::text = m.marka::text AND                                v.vehicle_create_year::double precision = date_part('YEAR'::text, m.create_date) AND                                v.vehicle_tonnage = m.load_wgt          LEFT JOIN dict d ON v.id = d.vehicle_id AND m.one_day >= d.start_date AND m.one_day < d.end_date WHERE (m.mvz_type::text = ANY (ARRAY ['Основной'::character varying::text, 'КДК'::character varying::text]))   AND (m.ts_group::text = ANY        (ARRAY ['Основное'::character varying::text, 'Полуприцеп'::character varying::text, 'Прицеп'::character varying::text])); 
--rollback create view nh_timeline_org_units_view(one_day, equnr, repshop_id, nh_daily_value) as WITH dict AS (SELECT nh_plan.vehicle_id,                      nh_plan.plan_nh,                      nh_plan.start_date,                      lead(nh_plan.start_date, 1, '9999-12-31'::date)                      OVER (PARTITION BY nh_plan.vehicle_id ORDER BY nh_plan.start_date) AS end_date               FROM nh_plan) SELECT m.one_day,        m.equnr,        ou.repshop_id,        COALESCE(d.plan_nh, 0::double precision) /        date_part('days'::text, m.one_day + '1 mon'::interval - m.one_day::timestamp without time zone) AS nh_daily_value FROM monitoring m          JOIN organizational_units_timeline ou               ON m.mvz_id::text = ou.mvz_id::text AND m.one_day >= ou.start_date AND m.one_day < ou.end_date          JOIN ts_data td ON td.equnr = m.equnr          JOIN nh_vehicles v ON v.type::text = td.ts_type::text AND v.brand::text = td.marka::text AND                                v.vehicle_create_year::double precision = date_part('YEAR'::text, td.create_date) AND                                v.vehicle_tonnage = td.load_wgt          LEFT JOIN dict d ON v.id = d.vehicle_id AND m.one_day >= d.start_date AND m.one_day < d.end_date; 
--rollback create view pl_fuel_stats_view (ctid, mr_name, atp_name, model, probeg, probeg_gorny, moto_hours_head, moto_hours_trail, k1_p,     norma_hou_head, norma_hou_trail, norma_gas, coef_zimny, coef_gorny, coef_reduce_fuel, fuel_price,     gas_price, rashod_fuel_plan, rashod_fuel_plan_head_tot, rashod_fuel_plan_head_hou, rashod_fuel_plan_head,     rashod_fuel_plan_trail_hou, rashod_fuel_fact, rashod_fuel_fact_head_tot, rashod_fuel_fact_head_hou,     rashod_fuel_fact_head, rashod_fuel_fact_trail_hou, econ_fuel, econ_fuel_head, econ_fuel_trail_hou,     rashod_gas_plan, rashod_gas_fact, econ_gas, rashod_fuel_plan_rub, rashod_fuel_plan_head_tot_rub,     rashod_fuel_plan_head_hou_rub, rashod_fuel_plan_head_rub, rashod_fuel_plan_trail_hou_rub,     rashod_fuel_fact_rub, rashod_fuel_fact_head_tot_rub, rashod_fuel_fact_head_hou_rub,     rashod_fuel_fact_head_rub, rashod_fuel_fact_trail_hou_rub, econ_fuel_rub, econ_fuel_head_rub,     econ_fuel_trail_hou_rub, rashod_gas_plan_rub, rashod_gas_fact_rub, econ_gas_rub, rashod_tot_plan,     rashod_tot_fact, econ_tot, rashod_tot_plan_rub, rashod_tot_fact_rub, econ_tot_rub,     rashod_fuel_without_gbo_assumed, effect_fuel_of_gbo_plan, effect_fuel_of_gbo_fact,     effect_tot_of_gbo_plan_rub, effect_tot_of_gbo_fact_rub, econ_tot_of_gbo_rub, qmnum, pl_start_date,     pl_end_date, mvz_id, mvz_name, ts_type, ts_group, marka, load_wgt, model_year, commissioning_year,     driver_tabnum, license_num, fleet_num, equnr, license_num_pri, gbo, date, mr_id, atp_id, retail_network,     atp_type, mvz_type) as SELECT pl_fuel_stats.ctid,        pl_fuel_stats.mr_name,        pl_fuel_stats.atp_name,        pl_fuel_stats.model,        pl_fuel_stats.probeg,        pl_fuel_stats.probeg_gorny,        pl_fuel_stats.moto_hours_head,        pl_fuel_stats.moto_hours_trail,        pl_fuel_stats.k1_p,        pl_fuel_stats.norma_hou_head,        pl_fuel_stats.norma_hou_trail,        pl_fuel_stats.norma_gas,        pl_fuel_stats.coef_zimny,        pl_fuel_stats.coef_gorny,        pl_fuel_stats.coef_reduce_fuel,        pl_fuel_stats.fuel_price,        pl_fuel_stats.gas_price,        pl_fuel_stats.rashod_fuel_plan,        pl_fuel_stats.rashod_fuel_plan_head_tot,        pl_fuel_stats.rashod_fuel_plan_head_hou,        pl_fuel_stats.rashod_fuel_plan_head,        pl_fuel_stats.rashod_fuel_plan_trail_hou,        pl_fuel_stats.rashod_fuel_fact,        pl_fuel_stats.rashod_fuel_fact_head_tot,        pl_fuel_stats.rashod_fuel_fact_head_hou,        pl_fuel_stats.rashod_fuel_fact_head,        pl_fuel_stats.rashod_fuel_fact_trail_hou,        pl_fuel_stats.econ_fuel,        pl_fuel_stats.econ_fuel_head,        pl_fuel_stats.econ_fuel_trail_hou,        pl_fuel_stats.rashod_gas_plan,        pl_fuel_stats.rashod_gas_fact,        pl_fuel_stats.econ_gas,        pl_fuel_stats.rashod_fuel_plan_rub,        pl_fuel_stats.rashod_fuel_plan_head_tot_rub,        pl_fuel_stats.rashod_fuel_plan_head_hou_rub,        pl_fuel_stats.rashod_fuel_plan_head_rub,        pl_fuel_stats.rashod_fuel_plan_trail_hou_rub,        pl_fuel_stats.rashod_fuel_fact_rub,        pl_fuel_stats.rashod_fuel_fact_head_tot_rub,        pl_fuel_stats.rashod_fuel_fact_head_hou_rub,        pl_fuel_stats.rashod_fuel_fact_head_rub,        pl_fuel_stats.rashod_fuel_fact_trail_hou_rub,        pl_fuel_stats.econ_fuel_rub,        pl_fuel_stats.econ_fuel_head_rub,        pl_fuel_stats.econ_fuel_trail_hou_rub,        pl_fuel_stats.rashod_gas_plan_rub,        pl_fuel_stats.rashod_gas_fact_rub,        pl_fuel_stats.econ_gas_rub,        pl_fuel_stats.rashod_tot_plan,        pl_fuel_stats.rashod_tot_fact,        pl_fuel_stats.econ_tot,        pl_fuel_stats.rashod_tot_plan_rub,        pl_fuel_stats.rashod_tot_fact_rub,        pl_fuel_stats.econ_tot_rub,        pl_fuel_stats.rashod_fuel_without_gbo_assumed,        pl_fuel_stats.effect_fuel_of_gbo_plan,        pl_fuel_stats.effect_fuel_of_gbo_fact,        pl_fuel_stats.effect_tot_of_gbo_plan_rub,        pl_fuel_stats.effect_tot_of_gbo_fact_rub,        pl_fuel_stats.econ_tot_of_gbo_rub,        pl_fuel_stats.qmnum,        pl_fuel_stats.pl_start_date,        pl_fuel_stats.pl_end_date,        pl_fuel_stats.mvz_id,        pl_fuel_stats.mvz_name,        pl_fuel_stats.ts_type,        pl_fuel_stats.ts_group,        pl_fuel_stats.marka,        pl_fuel_stats.load_wgt,        pl_fuel_stats.model_year,        pl_fuel_stats.create_year AS commissioning_year,        pl_fuel_stats.driver_tabnum,        pl_fuel_stats.license_num,        pl_fuel_stats.fleet_num,        pl_fuel_stats.equnr,        pl_fuel_stats.license_num_pri,        pl_fuel_stats.gbo,        pl_fuel_stats.date,        pl_fuel_stats.mr_id,        pl_fuel_stats.atp_id,        pl_fuel_stats.retail_network,        pl_fuel_stats.atp_type,        pl_fuel_stats.mvz_type FROM pl_fuel_stats; 
--rollback create view repair_base_properties_view(atp_id, properties, year) as SELECT rbpa.atp_id,        json_agg(json_build_object('month', rbpa.month, 'name', rbpa.property, 'value',                                   rbpa.value))::character varying AS properties,        rbpa.year FROM repair_base_properties_atp rbpa GROUP BY rbpa.atp_id, rbpa.year; 
--rollback create view repair_chart_joined_view (order_id, start_date, territory_id, territory_name, mr_id, mr_name, atp_id, atp_name, mvz_id, mvz_name,     repair_atp, retail_network, atp_type, mvz_type, repshop_id, repshop_name, repair_kind, repair_place,     repair_year, month, week, month_year, week_year, report_week_year, quarter_year, day_month_year, vrt,     vrt_name, event_id, event_text, repair_type, repair_type_name, repair_subtype, repair_subtype_name, equnr,     vehicle_licence, vehicle_tonnage, vehicle_brand, vehicle_model, vehicle_type, vehicle_group,     vehicle_create_year, vehicle_gbo, repair_expenses, part_amount, part_expenses, services_amount,     services_expenses) as SELECT r.order_id,        r.repair_start_date                                                                                       AS start_date,        o.territory_id,        COALESCE(o.territory_name, 'N/A'::character varying)                                                      AS territory_name,        o.mr_id,        COALESCE(o.mr_name, 'N/A'::character varying)                                                             AS mr_name,        o.atp_id,        COALESCE(o.atp_name, 'N/A'::character varying)                                                            AS atp_name,        COALESCE(o.mvz_id, 'N/A'::character varying)                                                              AS mvz_id,        COALESCE(o.mvz_name, 'N/A'::character varying)                                                            AS mvz_name,        COALESCE(r.final_repshop_name, 'N/A'::character varying)                                                  AS repair_atp,        COALESCE(o.retail_network, 'N/A'::character varying)                                                      AS retail_network,        COALESCE(o.atp_type, 'N/A'::character varying)                                                            AS atp_type,        COALESCE(o.mvz_type, 'N/A'::character varying)                                                            AS mvz_type,        ro.repshop_id,        CASE            WHEN r.our_workshop = true AND r.final_service_mvz_id::text = 'МВЗ не определено'::text                THEN 'РЗ не определена'::character varying            ELSE ro.repshop_name            END                                                                                                   AS repshop_name,        COALESCE(r.repair_kind, 'N/A'::character varying)                                                         AS repair_kind,        COALESCE(                CASE                    WHEN r.repair_kind::text = ANY (ARRAY ['X540'::text, 'X542'::text, 'X543'::text])                        THEN 'Ремзона'::character varying                    WHEN r.repair_kind::text = ANY (ARRAY ['X541'::text, 'X544'::text]) THEN 'СТО'::character varying                    ELSE r.repair_kind                    END,                'N/A'::character varying)                                                                         AS repair_place,        COALESCE(date_part('year'::text, r.repair_start_date)::text,                 'N/A'::text)                                                                                     AS repair_year,        COALESCE(date_part('month'::text, r.repair_start_date)::text,                 'N/A'::text)                                                                                     AS month,        COALESCE(date_part('week'::text, r.repair_start_date)::text,                 'N/A'::text)                                                                                     AS week,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, 'MM.YYYY'::text),                 'N/A'::text)                                                                                     AS month_year,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, '"Н "IW.IYYY'::text),                 'N/A'::text)                                                                                     AS week_year,        COALESCE(to_char((r.repair_start_date + 3)::timestamp with time zone, '"ОН "IW.IYYY'::text),                 'N/A'::text)                                                                                     AS report_week_year,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, '"Кв "Q.YYYY'::text),                 'N/A'::text)                                                                                     AS quarter_year,        COALESCE(to_char(r.repair_start_date::timestamp with time zone, 'DD.MM.YYYY'::text),                 'N/A'::text)                                                                                     AS day_month_year,        COALESCE(r.vrt, 'N/A'::character varying)                                                                 AS vrt,        COALESCE(COALESCE(r.vrt_name, ('Код ВРТ - '::text || r.vrt::text)::character varying),                 'N/A'::character varying)                                                                        AS vrt_name,        COALESCE(r.event_id, 'N/A'::character varying)                                                            AS event_id,        COALESCE(r.event_text, 'N/A'::character varying)                                                          AS event_text,        COALESCE(r.repair_type_id::text, 'N/A'::text)                                                             AS repair_type,        COALESCE(twt.name, 'N/A'::character varying)                                                              AS repair_type_name,        COALESCE(r.repair_subtype_id::text, 'N/A'::text)                                                          AS repair_subtype,        COALESCE(tws.name, 'N/A'::character varying)                                                              AS repair_subtype_name,        r.equnr,        COALESCE(r.ts_license_num, 'N/A'::character varying)                                                      AS vehicle_licence,        COALESCE(to_char(r.ts_load_wgt, 'FM90D0'::text), 'N/A'::text)                                             AS vehicle_tonnage,        COALESCE(r.ts_marka, 'N/A'::character varying)                                                            AS vehicle_brand,        COALESCE(r.ts_model, 'N/A'::character varying)                                                            AS vehicle_model,        COALESCE(r.ts_type, 'N/A'::character varying)                                                             AS vehicle_type,        COALESCE(r.ts_group, 'N/A'::character varying)                                                            AS vehicle_group,        COALESCE(date_part('year'::text, r.ts_create_date)::text,                 'N/A'::text)                                                                                     AS vehicle_create_year,        CASE            WHEN r.ts_gbo = true THEN 'С ГБО'::text            ELSE 'Без ГБО'::text            END                                                                                                   AS vehicle_gbo,        r.repair_expenses,        r.part_amount,        r.part_expenses,        r.services_amount,        r.services_expenses FROM repair r          LEFT JOIN organizational_units_timeline o                    ON r.vehicle_mvz_id::text = o.mvz_id::text AND r.repair_start_date >= o.start_date AND                       r.repair_start_date < o.end_date          LEFT JOIN organizational_units_timeline ro                    ON r.repair_start_date >= ro.start_date AND r.repair_start_date < ro.end_date AND                       r.final_service_mvz_id::text = ro.mvz_id::text          LEFT JOIN toro_works tw ON r.vrt::text = tw.id::text          LEFT JOIN toro_works_types twt ON tw.subtype_id = twt.id          LEFT JOIN toro_works_subtypes tws ON tw.subtype_id = tws.id; 
--rollback create view repair_chart_view (order_id, start_date, mr_id, mr_name, atp_id, atp_name, mvz_id, mvz_name, repair_atp, retail_network,     atp_type, mvz_type, repair_kind, repair_place, year, month, week, month_year, week_year, vrt, vrt_name,     event_id, event_text, repair_type, repair_type_name, repair_subtype, repair_subtype_name, equnr,     vehicle_licence, vehicle_tonnage, vehicle_brand, vehicle_model, vehicle_type, vehicle_group,     vehicle_create_year, vehicle_gbo, repair_expenses, part_amount, part_expenses, services_amount,     services_expenses) as SELECT repair.order_id,        repair.repair_start_date                                                               AS start_date,        repair.vehicle_mr_id                                                                   AS mr_id,        repair.vehicle_mr                                                                      AS mr_name,        repair.vehicle_atp_id                                                                  AS atp_id,        repair.vehicle_atp                                                                     AS atp_name,        repair.vehicle_mvz_id                                                                  AS mvz_id,        repair.vehicle_mvz_name                                                                AS mvz_name,        repair.final_repshop_name                                                              AS repair_atp,        repair.vehicle_retail_network                                                          AS retail_network,        repair.vehicle_atp_type                                                                AS atp_type,        repair.vehicle_mvz_type                                                                AS mvz_type,        repair.repair_kind,        CASE            WHEN repair.repair_kind::text = ANY                 (ARRAY ['X540'::character varying::text, 'X542'::character varying::text, 'X543'::character varying::text])                THEN 'Ремзона'::character varying            WHEN repair.repair_kind::text = ANY                 (ARRAY ['X541'::character varying::text, 'X544'::character varying::text]) THEN 'СТО'::character varying            ELSE repair.repair_kind            END                                                                                AS repair_place,        date_part('year'::text, repair.repair_start_date)                                      AS year,        date_part('month'::text, repair.repair_start_date)                                     AS month,        date_part('week'::text, repair.repair_start_date)                                      AS week,        (date_part('month'::text, repair.repair_start_date) || '.'::text) ||        date_part('year'::text, repair.repair_start_date)                                      AS month_year,        (('Н '::text || date_part('week'::text, repair.repair_start_date)) || '.'::text) ||        CASE            WHEN date_part('month'::text, repair.repair_start_date) = 1::double precision AND                 (date_part('week'::text, repair.repair_start_date) = ANY                  (ARRAY [52::double precision, 53::double precision])) THEN                date_part('year'::text, repair.repair_start_date) - 1::double precision            WHEN date_part('month'::text, repair.repair_start_date) = 12::double precision AND                 date_part('week'::text, repair.repair_start_date) = 1::double precision THEN                date_part('year'::text, repair.repair_start_date) + 1::double precision            ELSE date_part('year'::text, repair.repair_start_date)            END                                                                                AS week_year,        repair.vrt,        COALESCE(repair.vrt_name, ('Код ВРТ - '::text || repair.vrt::text)::character varying) AS vrt_name,        repair.event_id,        repair.event_text,        repair.repair_type_id                                                                  AS repair_type,        repair.repair_type_name,        repair.repair_subtype_id                                                               AS repair_subtype,        repair.repair_subtype_name,        repair.equnr,        repair.ts_license_num                                                                  AS vehicle_licence,        repair.ts_load_wgt                                                                     AS vehicle_tonnage,        repair.ts_marka                                                                        AS vehicle_brand,        repair.ts_model                                                                        AS vehicle_model,        repair.ts_type                                                                         AS vehicle_type,        repair.ts_group                                                                        AS vehicle_group,        date_part('year'::text, repair.ts_create_date)                                         AS vehicle_create_year,        CASE            WHEN repair.ts_gbo = true THEN 'С ГБО'::text            ELSE 'Без ГБО'::text            END                                                                                AS vehicle_gbo,        repair.repair_expenses,        repair.part_amount,        repair.part_expenses,        repair.services_amount,        repair.services_expenses FROM repair; 
--rollback create view repair_services_with_rephops_view (ctid, order_id, service_id, service_name, service_amount, service_expenses, creditor_number, creditor_name,     purchase_order_id, mechanic_id, service_row_num, service_expenses_kind, vrt, use_nh, our_workshop,     repair_start_date, vehicle_mr_id, vehicle_atp_id, vehicle_mvz_id, vehicle_retail_network, vehicle_atp_type,     vehicle_mvz_type, vehicle_rephop_id, mechanic_rephop_id) as SELECT row_number() OVER () AS ctid,        rs.order_id,        rs.service_id,        rs.service_name,        rs.service_amount,        rs.service_expenses,        rs.creditor_number,        rs.creditor_name,        rs.purchase_order_id,        rs.mechanic_id,        rs.service_row_num,        rs.service_expenses_kind,        rs.vrt,        tw.use_nh,        rs.our_workshop,        rs.repair_start_date,        rs.vehicle_mr_id,        rs.vehicle_atp_id,        rs.vehicle_mvz_id,        rs.vehicle_retail_network,        rs.vehicle_atp_type,        rs.vehicle_mvz_type,        vtl.repshop_id       AS vehicle_rephop_id,        mtl.repshop_id       AS mechanic_rephop_id FROM repair_services rs          LEFT JOIN (SELECT organizational_units_timeline.start_date,                            organizational_units_timeline.end_date,                            organizational_units_timeline.mvz_id,                            organizational_units_timeline.repshop_id,                            organizational_units_timeline.repshop_name                     FROM organizational_units_timeline) vtl                    ON rs.vehicle_mvz_id::text = vtl.mvz_id::text AND vtl.start_date <= rs.repair_start_date AND                       rs.repair_start_date < vtl.end_date          LEFT JOIN (SELECT organizational_units_timeline.start_date,                            organizational_units_timeline.end_date,                            organizational_units_timeline.mvz_id,                            organizational_units_timeline.repshop_id,                            organizational_units_timeline.repshop_name                     FROM organizational_units_timeline) mtl                    ON rs.mechanic_mvz_id::text = mtl.mvz_id::text AND mtl.start_date <= rs.repair_start_date AND                       rs.repair_start_date < mtl.end_date          LEFT JOIN toro_works tw ON tw.id::text = rs.vrt::text; 
--rollback create view repshop_performance_plan_timeline_view(day, month, year, rs_id, daily_value) as WITH months AS (SELECT months.text_month,                        row_number() OVER ()::integer AS month_number                 FROM (SELECT unnest(enum_range(NULL::month_enum))::text AS text_month) months),      dict AS (SELECT repshops_properties.rs_id,                      repshops_properties.year,                      months.month_number,                      repshops_properties.property,                      repshops_properties.value,                      make_date(repshops_properties.year, months.month_number, 1) AS date               FROM repshops_properties                        JOIN months ON repshops_properties.month::text = months.text_month),      period AS (SELECT date_part('YEAR'::text, p_1.day)  AS year,                        date_part('MONTH'::text, p_1.day) AS month,                        p_1.day                 FROM (SELECT generate_series(                                      '2016-01-01 00:00:00'::timestamp without time zone::timestamp with time zone,                                      now() + '3 years'::interval, '1 day'::interval)::date AS day) p_1),      daily_performance AS (SELECT d.rs_id,                                   d.year,                                   d.month_number,                                   d.value::double precision / date_part('days'::text, d.date + '1 mon'::interval -                                                                                       d.date::timestamp without time zone) AS daily_rate                            FROM dict d                            WHERE d.property::text = 'post_performance'::text) SELECT p.day,        p.month,        p.year,        dp.rs_id,        (d_day.value + d_night.value)::double precision * dp.daily_rate AS daily_value FROM period p          LEFT JOIN daily_performance dp                    ON p.year = dp.year::double precision AND p.month = dp.month_number::double precision          LEFT JOIN dict d_day ON dp.rs_id = d_day.rs_id AND p.year = d_day.year::double precision AND                                  p.month = d_day.month_number::double precision AND d_day.property::text = 'day_posts'::text          LEFT JOIN dict d_night ON dp.rs_id = d_night.rs_id AND p.year = d_night.year::double precision AND                                    p.month = d_night.month_number::double precision AND                                    d_night.property::text = 'night_posts'::text; 
--rollback create view ts_number_view (id, equnr, vrt_start_date, vrt_end_date, vrt_inactive_date, vrt_mr_id, mr_name, vrt_atp_id, vrt_atp_type,     atp_name, vrt_mvz_id, vrt_retail_network, vrt_mvz_name, vrt_mvz_type, td_create_date_year, td_inactive_day,     td_deleted_day, td_truck_type, td_ts_type, td_marka, td_model, td_load_wgt, td_gbo, td_license_num,     td_create_date, vrt_start_date_year, vrt_start_date_month, vrt_start_date_week) as SELECT vrt.ctid                                 AS id,        vrt.equnr,        vrt.start_date                           AS vrt_start_date,        vrt.end_date                             AS vrt_end_date,        vrt.inactive_date                        AS vrt_inactive_date,        vrt.mr_id                                AS vrt_mr_id,        mr.name                                  AS mr_name,        vrt.atp_id                               AS vrt_atp_id,        vrt.atp_type                             AS vrt_atp_type,        atp.name                                 AS atp_name,        vrt.mvz_id                               AS vrt_mvz_id,        vrt.retail_network                       AS vrt_retail_network,        vrt.mvz_name                             AS vrt_mvz_name,        vrt.mvz_type                             AS vrt_mvz_type,        date_part('year'::text, td.create_date)  AS td_create_date_year,        td.inactive_day                          AS td_inactive_day,        td.deleted_day                           AS td_deleted_day,        td.truck_type                            AS td_truck_type,        td.ts_type                               AS td_ts_type,        td.marka                                 AS td_marka,        td.model                                 AS td_model,        td.load_wgt                              AS td_load_wgt,        CASE            WHEN td.gbo THEN 'С ГБО'::text            ELSE 'Без ГБО'::text            END                                  AS td_gbo,        td.license_num                           AS td_license_num,        td.create_date                           AS td_create_date,        date_part('year'::text, vrt.start_date)  AS vrt_start_date_year,        date_part('month'::text, vrt.start_date) AS vrt_start_date_month,        date_part('week'::text, vrt.start_date)  AS vrt_start_date_week FROM vehicle_region_timeline vrt          JOIN ts_data td ON vrt.equnr = td.equnr          LEFT JOIN macro_region mr ON mr.id = vrt.mr_id          LEFT JOIN atp ON atp.id = vrt.atp_id WHERE td.ts_type IS NOT NULL; 
--rollback create materialized view organizational_units_timeline_test as WITH all_dates AS (SELECT DISTINCT _.start_date                    FROM (SELECT mvz_log.start_date                          FROM ts.mvz_log                          WHERE NOT mvz_log.deleted                          UNION                          SELECT atp_log.start_date                          FROM ts.atp_log                          WHERE NOT atp_log.deleted                          UNION                          SELECT mr_log.start_date                          FROM ts.mr_log                          WHERE NOT mr_log.deleted                          UNION                          SELECT atp_repshops_log.start_date                          FROM atp_repshops_log                          WHERE NOT atp_repshops_log.deleted                          UNION                          SELECT hr_atp_places_log.start_date                          FROM hr_atp_places_log                          WHERE NOT hr_atp_places_log.deleted                          UNION                          SELECT hr_places_tplaces_log.start_date                          FROM hr_places_tplaces_log                          WHERE NOT hr_places_tplaces_log.deleted) _                    ORDER BY _.start_date),      mvz_log AS (SELECT mvz_log.uid                                                 AS mvz_id,                         mvz_codes.name                                              AS mvz_name,                         mvz_codes.type                                              AS mvz_type,                         mvz_codes.ut,                         mvz_log.start_date,                         lead(mvz_log.start_date, 1, '9999-12-31'::date)                         OVER (PARTITION BY mvz_log.uid ORDER BY mvz_log.start_date) AS end_date,                         mvz_log.atp_id,                         atp.name                                                    AS atp_name,                         atp.type                                                    AS atp_type,                         atp.retail_network                  FROM ts.mvz_log                           JOIN mvz_codes ON mvz_codes.uid::text = mvz_log.uid::text                           JOIN atp ON mvz_log.atp_id = atp.id                  WHERE NOT mvz_log.deleted                    AND NOT atp.deleted),      atp_log AS (SELECT atp_log.atp_id,                         atp_log.start_date,                         lead(atp_log.start_date, 1, '9999-12-31'::date)                         OVER (PARTITION BY atp_log.atp_id ORDER BY atp_log.start_date) AS end_date,                         atp_log.mr_id,                         region.name                                                    AS mr_name                  FROM ts.atp_log atp_log                           LEFT JOIN macro_region region ON atp_log.mr_id = region.id                  WHERE NOT atp_log.deleted                    AND NOT region.deleted),      mr_log AS (SELECT mr_log.mr_id,                        mr_log.start_date,                        lead(mr_log.start_date, 1, '9999-12-31'::date)                        OVER (PARTITION BY mr_log.mr_id ORDER BY mr_log.start_date) AS end_date,                        mr_log.ter_id                                               AS territory_id,                        t.name                                                      AS territory_name                 FROM ts.mr_log                          LEFT JOIN territory t ON t.id = mr_log.ter_id                 WHERE NOT mr_log.deleted                   AND NOT t.deleted),      repshops_log AS (SELECT rslog.atp_id,                              rslog.rs_id                                                AS repshop_id,                              repshops.name                                              AS repshop_name,                              rslog.start_date,                              lead(rslog.start_date, 1, '9999-12-31'::date)                              OVER (PARTITION BY rslog.atp_id ORDER BY rslog.start_date) AS end_date                       FROM atp_repshops_log rslog                                JOIN repshops ON repshops.id = rslog.rs_id                       WHERE NOT rslog.deleted                         AND NOT repshops.deleted),      hr_places_log AS (SELECT hr_atp_places_log.atp_id,                               hr_atp_places_log.hrp_id                                                           AS hr_place_id,                               hr_places.name                                                                     AS hr_place_name,                               hr_atp_places_log.start_date,                               lead(hr_atp_places_log.start_date, 1, '9999-12-31'::date)                               OVER (PARTITION BY hr_atp_places_log.atp_id ORDER BY hr_atp_places_log.start_date) AS end_date                        FROM hr_atp_places_log                                 JOIN hr_places ON hr_places.id = hr_atp_places_log.hrp_id                        WHERE NOT hr_atp_places_log.deleted                          AND NOT hr_places.deleted),      places_tplaces_log AS (SELECT hr_places_tplaces_log.p_id                                                               AS hr_place_id,                                    hr_places_tplaces_log.tp_id                                                              AS hr_tplace_id,                                    hr_total_places.name                                                                     AS hr_tplace_name,                                    hr_places_tplaces_log.start_date,                                    lead(hr_places_tplaces_log.start_date, 1, '9999-12-31'::date)                                    OVER (PARTITION BY hr_places_tplaces_log.p_id ORDER BY hr_places_tplaces_log.start_date) AS end_date                             FROM hr_places_tplaces_log                                      JOIN hr_places ON hr_places.id = hr_places_tplaces_log.p_id                                      JOIN hr_total_places ON hr_total_places.id = hr_places_tplaces_log.tp_id                             WHERE NOT hr_places_tplaces_log.deleted                               AND NOT hr_places.deleted                               AND NOT hr_total_places.deleted),      main_data AS (SELECT min(_.start_date) AS start_date,                           _.mvz_id,                           _.mvz_name,                           _.mvz_type,                           _.atp_id,                           _.atp_name,                           _.retail_network,                           _.atp_type,                           _.mr_id,                           _.mr_name,                           _.repshop_id,                           _.repshop_name,                           _.hr_place_id,                           _.hr_place_name,                           _.hr_tplace_id,                           _.hr_tplace_name,                           _.territory_id,                           _.territory_name                    FROM (SELECT ad.start_date,                                 ml.mvz_id,                                 ml.mvz_name,                                 ml.mvz_type,                                 ml.atp_id,                                 ml.atp_name,                                 ml.retail_network,                                 ml.atp_type,                                 al.mr_id,                                 al.mr_name,                                 rl.repshop_id,                                 rl.repshop_name,                                 hrl.hr_place_id,                                 hrl.hr_place_name,                                 hrtpl.hr_tplace_id,                                 hrtpl.hr_tplace_name,                                 mrl.territory_id,                                 mrl.territory_name                          FROM all_dates ad                                   LEFT JOIN mvz_log ml ON ml.start_date <= ad.start_date AND ad.start_date < ml.end_date                                   LEFT JOIN atp_log al ON ml.atp_id = al.atp_id AND al.start_date <= ad.start_date AND                                                           ad.start_date < al.end_date                                   LEFT JOIN mr_log mrl ON mrl.mr_id = al.mr_id AND mrl.start_date <= ad.start_date AND                                                           ad.start_date < mrl.end_date                                   LEFT JOIN repshops_log rl                                             ON rl.atp_id = ml.atp_id AND rl.start_date <= ad.start_date AND                                                ad.start_date < rl.end_date                                   LEFT JOIN hr_places_log hrl                                             ON hrl.atp_id = ml.atp_id AND hrl.start_date <= ad.start_date AND                                                ad.start_date < hrl.end_date                                   LEFT JOIN places_tplaces_log hrtpl ON hrl.hr_place_id = hrtpl.hr_place_id AND                                                                         hrtpl.start_date <= ad.start_date AND                                                                         ad.start_date < hrtpl.end_date) _                    GROUP BY _.mvz_id, _.mvz_name, _.mvz_type, _.atp_id, _.atp_name, _.retail_network, _.atp_type,                             _.mr_id, _.mr_name, _.repshop_id, _.repshop_name, _.hr_place_id, _.hr_place_name,                             _.hr_tplace_id, _.hr_tplace_name, _.territory_id, _.territory_name) SELECT main_data.start_date,        lead(main_data.start_date, 1, '9999-12-31'::date)        OVER (PARTITION BY main_data.mvz_id ORDER BY main_data.start_date) AS end_date,        main_data.mvz_id,        main_data.mvz_name,        main_data.mvz_type,        main_data.atp_id,        main_data.atp_name,        main_data.retail_network,        main_data.atp_type,        main_data.mr_id,        main_data.mr_name,        main_data.repshop_id,        main_data.repshop_name,        main_data.hr_place_id,        main_data.hr_place_name,        main_data.hr_tplace_id,        main_data.hr_tplace_name,        main_data.territory_id,        main_data.territory_name FROM main_data; 
--rollback create materialized view vehicle_region_hr_timeline as WITH mvz_log AS (SELECT mvz_log.uid,                         mvz_codes.name,                         mvz_codes.type,                         mvz_log.start_date,                         lead(mvz_log.start_date, 1, '9999-12-31'::date)                         OVER (PARTITION BY mvz_log.uid ORDER BY mvz_log.start_date) AS end_date,                         mvz_log.atp_id                  FROM ts.mvz_log                           JOIN mvz_codes ON mvz_codes.uid::text = mvz_log.uid::text                  WHERE NOT mvz_log.deleted),      repshops_log AS (SELECT rslog.atp_id,                              rslog.rs_id                                                AS repshop_id,                              repshops.name                                              AS repshop_name,                              rslog.start_date,                              lead(rslog.start_date, 1, '9999-12-31'::date)                              OVER (PARTITION BY rslog.atp_id ORDER BY rslog.start_date) AS end_date                       FROM atp_repshops_log rslog                                JOIN repshops ON repshops.id = rslog.rs_id                       WHERE NOT rslog.deleted                         AND NOT repshops.deleted),      vehicle_history AS (SELECT mvz_history.equnr,                                 GREATEST(mvz_history.start_date, mvz_log.start_date,                                          repshops_log.start_date)                                    AS start_date,                                 LEAST(mvz_history.end_date, mvz_log.end_date, repshops_log.end_date) AS end_date,                                 mvz_history.mvz                                                      AS mvz_id,                                 mvz_log.name                                                         AS mvz_name,                                 mvz_log.type                                                         AS mvz_type,                                 mvz_log.atp_id,                                 repshops_log.repshop_id,                                 repshops_log.repshop_name                          FROM ts_mvz_hist mvz_history                                   LEFT JOIN mvz_log ON mvz_history.mvz::text = mvz_log.uid::text                                   LEFT JOIN repshops_log ON mvz_log.atp_id = repshops_log.atp_id                          WHERE mvz_history.start_date < mvz_history.end_date                            AND GREATEST(mvz_history.start_date, mvz_log.start_date, repshops_log.start_date) <                                LEAST(mvz_history.end_date, mvz_log.end_date, repshops_log.end_date)),      atp_history AS (SELECT atp_log.atp_id,                             atp.name                                                       AS atp_name,                             atp_log.start_date,                             lead(atp_log.start_date, 1, '9999-12-31'::date)                             OVER (PARTITION BY atp_log.atp_id ORDER BY atp_log.start_date) AS end_date,                             atp.type                                                       AS transport_type,                             atp.retail_network,                             atp_log.mr_id,                             region.name                                                    AS mr_name                      FROM atp_log atp_log                               LEFT JOIN atp ON atp_log.atp_id = atp.id                               LEFT JOIN macro_region region ON atp_log.mr_id = region.id                      WHERE NOT atp_log.deleted                        AND NOT atp.deleted),      atp_places_log AS (SELECT hr_atp_places_log.atp_id,                                hr_atp_places_log.hrp_id,                                hr_atp_places_log.start_date,                                lead(hr_atp_places_log.start_date, 1, '9999-12-31'::date)                                OVER (PARTITION BY hr_atp_places_log.atp_id ORDER BY hr_atp_places_log.start_date) AS end_date                         FROM hr_atp_places_log                                  JOIN hr_places ON hr_places.id = hr_atp_places_log.hrp_id                         WHERE NOT hr_atp_places_log.deleted                           AND NOT hr_places.deleted),      places_tplaces_log AS (SELECT hr_places_tplaces_log.p_id,                                    hr_places_tplaces_log.tp_id,                                    hr_places_tplaces_log.start_date,                                    lead(hr_places_tplaces_log.start_date, 1, '9999-12-31'::date)                                    OVER (PARTITION BY hr_places_tplaces_log.p_id ORDER BY hr_places_tplaces_log.start_date) AS end_date                             FROM hr_places_tplaces_log                                      JOIN hr_places ON hr_places.id = hr_places_tplaces_log.p_id                                      JOIN hr_total_places ON hr_total_places.id = hr_places_tplaces_log.tp_id                             WHERE NOT hr_places_tplaces_log.deleted                               AND NOT hr_places.deleted                               AND NOT hr_total_places.deleted),      vehicle_region_timeline AS (SELECT vehicle_history.equnr,                                         GREATEST(vehicle_history.start_date, atp_history.start_date,                                                  atp_places_log.start_date,                                                  places_tplaces_log.start_date) AS start_date,                                         LEAST(vehicle_history.end_date, atp_history.end_date, atp_places_log.end_date,                                               places_tplaces_log.end_date)      AS end_date,                                         vehicle_history.mvz_id,                                         vehicle_history.mvz_name,                                         vehicle_history.mvz_type,                                         vehicle_history.atp_id,                                         atp_history.atp_name,                                         atp_history.transport_type,                                         atp_history.retail_network,                                         vehicle_history.repshop_id,                                         vehicle_history.repshop_name,                                         atp_history.mr_id,                                         atp_history.mr_name,                                         hr_places.id                            AS hr_place_id,                                         hr_places.name                          AS hr_place_nm,                                         hr_total_places.id                      AS hr_total_place_id,                                         hr_total_places.name                    AS hr_total_place_nm,                                         vehicle.create_date,                                         vehicle_type.ts_group                   AS vehicle_group                                  FROM vehicle_history                                           LEFT JOIN atp_history ON vehicle_history.atp_id = atp_history.atp_id                                           LEFT JOIN atp_places_log ON atp_places_log.atp_id = atp_history.atp_id                                           LEFT JOIN hr_places ON hr_places.id = atp_places_log.hrp_id                                           LEFT JOIN places_tplaces_log ON places_tplaces_log.p_id = hr_places.id                                           LEFT JOIN hr_total_places ON hr_total_places.id = places_tplaces_log.tp_id                                           JOIN ts_data vehicle ON vehicle_history.equnr = vehicle.equnr                                           JOIN types vehicle_type ON vehicle.ts_type::text = vehicle_type.ts_type::text                                  WHERE GREATEST(vehicle_history.start_date, atp_history.start_date,                                                 atp_places_log.start_date, places_tplaces_log.start_date) <                                        LEAST(vehicle_history.end_date, atp_history.end_date, atp_places_log.end_date,                                              places_tplaces_log.end_date)),      vehicle_activity_timeline AS (SELECT ts_data.equnr,                                           NULL::date                                                   AS start_date,                                           LEAST(ts_data.inactive_day, ts_data.deleted_day)             AS end_date,                                           NULL::date                                                   AS inactive_date,                                           false                                                        AS is_inactive,                                           (ts_data.ts_deleted::text = 'true'::text OR                                            ts_data.ts_inactive::text = 'true'::text) AND                                           ts_data.inactive_day IS NULL AND ts_data.deleted_day IS NULL AS is_outlier                                    FROM ts_data                                    UNION                                    SELECT ts_data.equnr,                                           LEAST(ts_data.inactive_day, ts_data.deleted_day)             AS start_date,                                           NULL::date                                                   AS end_date,                                           LEAST(ts_data.inactive_day, ts_data.deleted_day)             AS inactive_date,                                           true                                                         AS is_inactive,                                           (ts_data.ts_deleted::text = 'true'::text OR                                            ts_data.ts_inactive::text = 'true'::text) AND                                           ts_data.inactive_day IS NULL AND ts_data.deleted_day IS NULL AS is_outlier                                    FROM ts_data                                    WHERE LEAST(ts_data.inactive_day, ts_data.deleted_day) IS NOT NULL) SELECT vehicle_region_timeline.equnr,        GREATEST(vehicle_region_timeline.start_date, vehicle_activity_timeline.start_date) AS start_date,        LEAST(vehicle_region_timeline.end_date, vehicle_activity_timeline.end_date)        AS end_date,        vehicle_region_timeline.mvz_id,        vehicle_region_timeline.mvz_name,        vehicle_region_timeline.mvz_type,        vehicle_region_timeline.atp_id,        vehicle_region_timeline.atp_name,        vehicle_region_timeline.transport_type,        vehicle_region_timeline.retail_network,        vehicle_region_timeline.mr_id,        vehicle_region_timeline.mr_name,        vehicle_region_timeline.hr_place_id,        vehicle_region_timeline.hr_place_nm,        vehicle_region_timeline.hr_total_place_id,        vehicle_region_timeline.hr_total_place_nm,        vehicle_region_timeline.vehicle_group,        vehicle_region_timeline.create_date,        vehicle_activity_timeline.is_inactive,        vehicle_activity_timeline.inactive_date FROM vehicle_region_timeline          LEFT JOIN vehicle_activity_timeline ON vehicle_region_timeline.equnr = vehicle_activity_timeline.equnr AND                                                 GREATEST(vehicle_region_timeline.start_date,                                                          vehicle_activity_timeline.start_date) <                                                 LEAST(vehicle_region_timeline.end_date,                                                       vehicle_activity_timeline.end_date) WHERE NOT vehicle_activity_timeline.is_outlier; 





