<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="24007.1" author="denis.berestinsky">
        <sql><![CDATA[
            alter table toro_works
                add if not exists updated_by varchar,
                add if not exists updated_at timestamptz;

            alter table toro_works_types
                add if not exists created_by varchar default 'unknown' not null,
                add if not exists created_at timestamptz default current_timestamp not null,
                add if not exists updated_by varchar,
                add if not exists updated_at timestamptz,
                add if not exists deleted boolean default false not null;

            alter table toro_works_subtypes
                add if not exists created_by varchar default 'unknown' not null,
                add if not exists created_at timestamptz default current_timestamp not null,
                add if not exists updated_by varchar,
                add if not exists updated_at timestamptz,
                add if not exists deleted boolean default false not null;
        ]]></sql>
    </changeSet>
</databaseChangeLog>