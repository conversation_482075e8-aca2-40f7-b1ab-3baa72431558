<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
          http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1870647.1" author="ayzhan.zheksembek">
        <sql><![CDATA[

            delete from report_templates where report = 'WAYBILLS' and name = 'По умолчанию' and type = 'default';

            insert into report_templates as tab(report, "user", name, type, create_time, update_time, deleted, settings)
            values ('WAYBILLS', 'system', 'По умолчанию', 'default', current_timestamp, current_timestamp, false, '{"sort": [], "columns": [{"name": "labels", "label": "Метка ПЛ", "isPinned": true, "isVisible": true}, {"name": "number", "label": "Номер ПЛ", "isPinned": true, "isVisible": true}, {"name": "labeledWB", "label": "ПЛ с метками", "isPinned": false, "isVisible": false}, {"name": "waybillQty", "label": "Кол-во ПЛ", "isPinned": false, "isVisible": false}, {"name": "status", "label": "Статус ПЛ", "isPinned": false, "isVisible": true}, {"name": "terName", "label": "Территория", "isPinned": false, "isVisible": false}, {"name": "mrName", "label": "Макрорегион", "isPinned": false, "isVisible": false}, {"name": "atp", "label": "АТП", "isPinned": false, "isVisible": true}, {"name": "mvz", "label": "МВЗ ТС", "isPinned": false, "isVisible": true}, {"name": "mvzName", "label": "Название МВЗ ТС", "isPinned": false, "isVisible": false}, {"name": "licenseNum", "label": "Гос. номер ТС", "isPinned": false, "isVisible": true}, {"name": "trailerLicenseNum", "label": "Гос. номер прицепа", "isPinned": false, "isVisible": true}, {"name": "gbo", "label": "ГБО", "isPinned": false, "isVisible": false}, {"name": "dateOpen", "label": "Дата открытия", "isPinned": false, "isVisible": true}, {"name": "timeOpen", "label": "Время открытия", "isPinned": false, "isVisible": true}, {"name": "dateClose", "label": "План. дата закрытия", "isPinned": false, "isVisible": true}, {"name": "timeClose", "label": "План. время закрытия", "isPinned": false, "isVisible": true}, {"name": "dateCloseReference", "label": "Дата ок. посл. рейса", "isPinned": false, "isVisible": true}, {"name": "timeCloseReference", "label": "Время ок. посл. рейса", "isPinned": false, "isVisible": true}, {"name": "driverNumber", "label": "Табельный номер водителя", "isPinned": false, "isVisible": true}, {"name": "driver", "label": "Водитель", "isPinned": false, "isVisible": true}, {"name": "brand", "label": "Марка ТС", "isPinned": false, "isVisible": true}, {"name": "model", "label": "Модель ТС", "isPinned": false, "isVisible": false}, {"name": "commissioningDate", "label": "Год ввода в эксплуатацию", "isPinned": false, "isVisible": false}, {"name": "year", "label": "Год выпуска ТС", "isPinned": false, "isVisible": false}, {"name": "tonnage", "label": "Тоннаж", "isPinned": false, "isVisible": true}, {"name": "eqUnit", "label": "Единица оборудования", "isPinned": false, "isVisible": false}, {"name": "vin", "label": "VIN номер", "isPinned": false, "isVisible": false}, {"name": "mileage", "label": "Пробег по ПЛ", "isPinned": false, "isVisible": true}, {"name": "hours", "label": "Часы по ПЛ", "isPinned": false, "isVisible": true}, {"name": "motoHours", "label": "Моточасы ХОУ", "isPinned": false, "isVisible": true}, {"name": "primaryFuel", "label": "Первичное топливо, л", "isPinned": false, "isVisible": true}, {"name": "secondaryFuel", "label": "Вторичное топливо, л", "isPinned": false, "isVisible": true}, {"name": "maintenance", "label": "Ремонтный ПЛ", "isPinned": false, "isVisible": false}, {"name": "vehicleGroup", "label": "Вид ТС", "isPinned": false, "isVisible": false}, {"name": "vehicleType", "label": "Тип ТС", "isPinned": false, "isVisible": false}, {"name": "mvzNameInWB", "label": "Название МВЗ в ПЛ", "isPinned": false, "isVisible": false}, {"name": "mvzInWB", "label": "МВЗ в ПЛ", "isPinned": false, "isVisible": false}, {"name": "typeWB", "label": "Тип ПЛ", "isPinned": false, "isVisible": false}, {"name": "commerce", "label": "Признак коммерция", "isPinned": false, "isVisible": false}, {"name": "transit", "label": "Признак перегон", "isPinned": false, "isVisible": false}, {"name": "tod", "label": "Признак командировка", "isPinned": false, "isVisible": false}], "filters": [{"name": "vehicleGroup", "value": ["Основное"], "readonly": false, "condition": "contain"}]}'::jsonb);

        ]]></sql>
    </changeSet>
</databaseChangeLog>
