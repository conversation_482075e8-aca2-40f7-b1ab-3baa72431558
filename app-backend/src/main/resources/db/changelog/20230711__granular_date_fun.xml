<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="504502.1" author="anton.podoprygolov">
        <sql><![CDATA[
            create or replace function granular_date(gran text, date_to_trim date) returns date as $$
            select
                case
                    when lower(gran) = 'reporting_week' then
                        case
                            when extract(isodow from date_to_trim - 5) >= 0 then date_to_trim - extract(isodow from date_to_trim - 5)::int
                else date_to_trim - (5 - extract(isodow from date_to_trim)::int)
            end
            else cast(date_trunc(gran, date_to_trim) as date)
            end
    $$ language sql;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
