<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1724353_1.2" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists repair_services_view;

            create or replace view repair_services_view as
            select oum.mr_id      as mechanic_mr_id,
                   oum.mr_name      as mechanic_mr_name,
                   oum.repshop_id   as mechanic_repshop_id,
                   oum.repshop_name as mechanic_repshop_name,
                   oum.atp_id as mechanic_atp_repair_id,
                   oum.atp_name as mechanic_atp_repair_name,
                   ouv.mr_id as vehicle_mr_id,
                   ouv.mr_name as vehicle_mr,
                   ouv.atp_id as vehicle_atp_id,
                   ouv.atp_name as vehicle_atp,
                   ouv.mvz_id as vehicle_mvz_id,
                   ouv.mvz_name as vehicle_mvz_name,
                   ouv.repshop_id as vehicle_repshop_id,
                   ouv.repshop_name as vehicle_repshop_name,
                   rs.equnr,
                   rs.repair_start_date,
                   rs.service_amount,
                   rs.repair_kind,
                   rs.mechanic_id,
                   rs.our_workshop
            from repair_services rs
                     left join organizational_units_timeline oum
                          on oum.start_date <= rs.repair_start_date and rs.repair_start_date < oum.end_date and
                             rs.mechanic_mvz_id = oum.mvz_id
                     left join organizational_units_timeline ouv
                          on ouv.start_date <= rs.repair_start_date and rs.repair_start_date < ouv.end_date and
                             rs.vehicle_mvz_id = ouv.mvz_id;
            ]]></sql>
    </changeSet>
</databaseChangeLog>