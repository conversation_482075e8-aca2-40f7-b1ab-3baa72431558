<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1755812.1" author="ayzhan.zheksembek">
        <sql><![CDATA[
            alter table chart_widget
                add if not exists global_filters_source jsonb default '{}';

            update chart_widget
                set global_filters_source = '{"geoFilter": "widget","date":"widget"}';
        ]]></sql>
    </changeSet>
</databaseChangeLog>
