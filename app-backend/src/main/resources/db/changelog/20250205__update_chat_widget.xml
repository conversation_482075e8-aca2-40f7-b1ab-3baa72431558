<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="2082327.1" author="denis.berestinsky">
        <sql><![CDATA[
            update chart_widget
            set type = 'SimpleBarChart'
            where type = 'BarChart'
            and jsonb_array_length(settings::jsonb->'rows') = 0;

            update chart_widget
            set type = 'StackedBarChart'
            where type = 'BarChart'
            and jsonb_array_length(settings::jsonb->'rows') > 0;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
