<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="577771.1" author="igor.belogolovsky">
        <sql><![CDATA[
            create
            or replace view mvz_log_view(
                uid,
                name,
                type,
                id_rep,
                name_rep,
                ut,
                atp_id,
                start_date,
                end_date,
                author,
                update_at
            ) as
            select ml.uid,
                   mc.name,
                   mc.type,
                   id_rep,
                   name_rep,
                   ut,
                   atp_id,
                   start_date,
                   lead(start_date - 1) over (partition by ml.uid order by start_date), author,
                   updateat
            from mvz_log ml
                     join mvz_codes mc on ml.uid = mc.uid
            where is_deleted = false;
            ]]></sql>
    </changeSet>
</databaseChangeLog>
