<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1932967.1" author="ayzhan.zheksembek">
        <sql><![CDATA[

            update report_templates
            set settings = '{
              "sort": [
                {
                  "asc": 1,
                  "name": "kipShare",
                  "label": "КИП, %"
                }
              ],
              "columns": [
                {
                   "label": "Макрорегион",
                  "name": "mr",
                  "isPinned": true,
                  "isVisible": true
                },
                {
                   "label": "АТП",
                  "name": "atp",
                  "isPinned": true,
                  "isVisible": true
                },
                {
                   "label": "Гос. номер ТС",
                  "name": "vehicleLicense",
                  "isPinned": true,
                  "isVisible": true
                },
                {
                   "label": "МВЗ",
                  "name": "mvz",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Название МВЗ",
                  "name": "mvzName",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Торговая сеть",
                  "name": "retailNetwork",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Вид деятельности АТП",
                  "name": "atpType",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Тип МВЗ",
                  "name": "mvzType",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Единица оборудования",
                  "name": "vehicleId",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "VIN номер",
                  "name": "vehicleVin",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Вид ТС",
                  "name": "vehicleGroup",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Тип ТС",
                  "name": "vehicleType",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Марка",
                  "name": "vehicleBrand",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Модель",
                  "name": "vehicleModel",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Тоннаж",
                  "name": "vehicleTonnage",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Год ввода в эксплуатацию",
                  "name": "vehicleCreateYear",
                  "isPinned": false,
                  "isVisible": false
                },
                {
                   "label": "Ср. кол-во ТС, шт.",
                  "name": "vehicleCount",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "КТГ, %",
                  "name": "ktgShare",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "РГ, %",
                  "name": "rgShare",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "КИП, %",
                  "name": "kipShare",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "КИП план, мч",
                  "name": "kipPlan",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "КТГ, мч",
                  "name": "ktgHours",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "РГ, мч",
                  "name": "rgHours",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "КИП факт, мч",
                  "name": "kipHours",
                  "isPinned": false,
                  "isVisible": true
                },
                {
                   "label": "Факторы",
                  "name": "factor",
                  "isPinned": false,
                  "isVisible": true
                }
              ],
              "filters": [
                {
                  "name": "vehicleGroup",
                  "value": [
                    "Основное"
                  ],
                  "readonly": true,
                  "condition": "contain"
                }
              ],
              "granularity": "Day"
            }
            '::jsonb
            where type = 'private'
              and report = 'KIPFACTOR';

            ]]></sql>
    </changeSet>
</databaseChangeLog>