<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1724351_1.1" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists nh_timeline_org_units_view;
            create view nh_timeline_org_units_view(one_day, equnr, repshop_id, nh_daily_value) as
            WITH dict AS (SELECT nh_plan.vehicle_id,
                                 nh_plan.plan_nh,
                                 nh_plan.start_date,
                                 lead(nh_plan.start_date, 1, '9999-12-31'::date)
                                     OVER (PARTITION BY nh_plan.vehicle_id ORDER BY nh_plan.start_date) AS end_date
                          FROM nh_plan)
            SELECT m.one_day,
                   m.equnr,
                   ou.repshop_id,
                   COALESCE(d.plan_nh, 0::double precision) /
                   date_part('days'::text, m.one_day + '1 mon'::interval - m.one_day::timestamp without time zone) AS nh_daily_value
            FROM monitoring m
                     JOIN organizational_units_timeline ou
                          ON m.mvz_id = ou.mvz_id AND m.one_day >= ou.start_date AND m.one_day < ou.end_date
                     JOIN ts_data td ON td.equnr = m.equnr
                     JOIN nh_vehicles v ON v.type::text = td.ts_type::text AND v.brand::text = td.marka::text AND
                               v.vehicle_create_year::double precision = date_part('YEAR'::text, td.create_date) AND
                               v.vehicle_tonnage = td.load_wgt
                     LEFT JOIN dict d ON v.id = d.vehicle_id AND m.one_day >= d.start_date AND m.one_day < d.end_date;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
