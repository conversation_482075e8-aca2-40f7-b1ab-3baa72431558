<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1735045.2" author="ayzhan.zheksembek">
        <sql><![CDATA[

            insert into dict_aggregates(name, sys_name, ordering, functions, shares)
            values
                ('Кол-во ТС, шт.', 'ts_total_amount', 130, '["count"]', false),
                ('КТГ, %', 'ktg_share', 140, '["count"]', false),
                ('РГ, %', 'rg_share', 150, '["count"]', false),
                ('ТС в ремонте (без ДТП), %', 'ktg_repair_share', 160, '["count"]', false),
                ('ТС с ДТП, %', 'ktg_dtp_share', 170, '["count"]', false)
            on conflict do nothing;

            ]]></sql>
    </changeSet>
</databaseChangeLog>