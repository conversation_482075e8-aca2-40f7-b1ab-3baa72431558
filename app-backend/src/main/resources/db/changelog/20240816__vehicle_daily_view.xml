<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1788941.1" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists vehicle_daily_timeline;

            create or replace view vehicle_daily_timeline as
            WITH days AS (SELECT generate_series('2018-01-01'::date, now() + '3 years'::interval,
                                     '1 day'::interval)::date AS day)
            SELECT vrt.equnr,
                   td.create_date,
                   days.day,
                   org.mvz_id,
                   org.atp_id,
                   org.mr_id,
                   org.mvz_type,
                   org.atp_type,
                   org.retail_network,
                   vrt.is_inactive,
                   t.ts_group
            FROM days
                     JOIN vehicle_region_timeline vrt ON vrt.start_date <= days.day AND days.day < vrt.end_date
                     JOIN organizational_units_timeline org
                          ON vrt.mvz_id = org.mvz_id AND org.start_date <= days.day AND days.day < org.end_date
                     JOIN ts_data td ON vrt.equnr = td.equnr
                     JOIN types t ON td.ts_type = t.ts_type;
            ]]></sql>
    </changeSet>
</databaseChangeLog>