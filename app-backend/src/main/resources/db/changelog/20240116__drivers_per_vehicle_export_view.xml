<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1447347.2" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists drivers_per_vehicle_export_view;
            create
            or replace view drivers_per_vehicle_export_view as
            with mon as (
                select unnest(enum_range(NULL::month_enum)) as month
            ), y as (
                select distinct year from hr_drivers_per_vehicle
            ), atp_with_month as (
                select atp.id, atp.name as atp_name, mon.month, y.year
                from mon
                cross join atp
                cross join y
                order by atp_name
            ), hapl as (
                SELECT hapl_1.id,
                       hapl_1.atp_id,
                       hapl_1.hrp_id,
                       hapl_1.start_date,
                       hapl_1.deleted,
                       lead(hapl_1.start_date, 1, '9999-12-31'::date)
                       OVER (PARTITION BY hapl_1.atp_id ORDER BY hapl_1.start_date) AS end_date
                FROM hr_atp_places_log hapl_1
            )
            select a.id as atp_id, a.atp_name,
                   hp.name as place_name,
                   mr.name as mr_name,
                   al.retail_network,
                   al.type as atp_type,
                   a.year,
                   a.month,
                   hdpv.value
            from atp_with_month a
                     LEFT JOIN hr_drivers_per_vehicle hdpv ON hdpv.atp_id = a.id and hdpv.month = a.month and hdpv.year = a.year
                     LEFT JOIN hapl ON a.id = hapl.atp_id AND hapl.start_date <= CURRENT_DATE AND
                                       hapl.end_date >= CURRENT_DATE
                     LEFT JOIN hr_places hp ON hapl.hrp_id = hp.id
                     LEFT JOIN atp_log al ON a.id = al.atp_id AND al.start_date <= CURRENT_DATE AND
                                             (al.end_date IS NULL OR al.end_date >= CURRENT_DATE)
                     LEFT JOIN macro_region mr ON al.mr_id = mr.id;
            ]]></sql>
    </changeSet>
</databaseChangeLog>
