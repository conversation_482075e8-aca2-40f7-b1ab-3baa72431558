<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1683893.1" author="igor.belogolovskiy">
        <sql><![CDATA[
            update report_templates
            set settings = replace(settings::text, '"label": "Марка"', '"label": "Марка ТС"')::jsonb
            where type = 'private'
              and report = 'KTG';
            update report_templates
            set settings = replace(settings::text, '"label": "Модель"', '"label": "Модель ТС"')::jsonb
            where type = 'private'
              and report = 'KTG';
            update report_templates
            set settings = replace(settings::text, '"label": "Тоннаж"', '"label": "Тоннаж ТС"')::jsonb
            where type = 'private'
              and report = 'KTG';
            update report_templates
            set settings = replace(settings::text, '"label": "Пробег на конец периода"', '"label": "Пробег ТС на конец периода"')::jsonb
            where type = 'private'
              and report = 'KTG';
            update report_templates
            set settings = replace(settings::text, '"label": "Единица оборудования"', '"label": "Единица оборудования ТС"')::jsonb
            where type = 'private'
              and report = 'KTG';
            update report_templates
            set settings = replace(settings::text, '"label": "VIN номер"', '"label": "VIN номер ТС"')::jsonb
            where type = 'private'
              and report = 'KTG';
            update report_templates
            set settings = replace(settings::text, '"label": "Год ввода в эксплуатацию"', '"label": "Год ввода в эксплуатацию ТС"')::jsonb
            where type = 'private'
              and report = 'KTG';

            update report_templates
            set settings = '{
  "columns": [
    {
      "label": "Кол-во строк",
      "name": "vehicleDays",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Торговая сеть",
      "name": "retailNetwork",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Вид деятельности",
      "name": "atpType",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Макрорегион",
      "name": "mrName",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "АТП",
      "name": "atp",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "МВЗ",
      "name": "mvz",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Тип МВЗ",
      "name": "mvzType",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Тип ТС",
      "name": "vehicleType",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Вид ТС",
      "name": "vehicleGroup",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Марка ТС",
      "name": "brand",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Модель ТС",
      "name": "model",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Тоннаж ТС",
      "name": "tonnage",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Пробег ТС на конец периода",
      "name": "mileage",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Гос. номер ТС",
      "name": "licenseNum",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Единица оборудования ТС",
      "name": "eqUnit",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "VIN номер ТС",
      "name": "vin",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Год ввода в эксплуатацию ТС",
      "name": "commissioningDate",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Тип прицепа",
      "name": "trailerType",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "Вид прицепа",
      "name": "trailerGroup",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "Марка прицепа",
      "name": "trailerBrand",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "Модель прицепа",
      "name": "trailerModel",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "Тоннаж прицепа",
      "name": "trailerTonnage",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Гос. номер прицепа",
      "name": "trailerLicenseNum",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Единица оборудования прицепа",
      "name": "trailerEqUnit",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "VIN номер прицепа",
      "name": "trailerVin",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "Год ввода в эксплуатацию прицепа",
      "name": "trailerCommissioningDate",
      "isPinned": false,
      "isVisible": false
    },
    {
      "label": "РЦ подачи",
      "name": "rcSubmitting",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Код РЦ подачи",
      "name": "rcSubmittingCode",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Дата подачи в ТГ",
      "name": "dateSubmitting",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Статус",
      "name": "status",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Причина",
      "name": "reason",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "ПЛ",
      "name": "waybills",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "Рем. ПЛ",
      "name": "waybillsRepair",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "КТГ, %",
      "name": "ktg",
      "isPinned": false,
      "isVisible": true
    },
    {
      "label": "РГ, %",
      "name": "rg",
      "isPinned": false,
      "isVisible": true
    }
  ],
  "filters": [
    {
      "name": "vehicleGroup",
      "condition": "contain",
      "value": [
        "Основное"
      ],
      "readonly": true
    }
  ],
  "sort": []
}'::jsonb
            where type = 'default'
              and report = 'KTG';

            ]]></sql>
    </changeSet>
</databaseChangeLog>