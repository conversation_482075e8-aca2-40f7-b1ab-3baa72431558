<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="2267319.1" author="ayzhan.zheksembek">
        <sql><![CDATA[

ALTER TABLE rr_structure_dictionary
	ADD COLUMN if not exists start_date date,
	ADD COLUMN if not exists end_date date;

update rr_structure_dictionary
	set start_date = MAKE_DATE(year, EXTRACT(MONTH FROM to_date(month::varchar, 'month'))::int, 1),
		end_date = (MAKE_DATE(year, EXTRACT(MONTH FROM to_date(month::varchar, 'month'))::int, 1) + '1 month'::interval)::date;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
