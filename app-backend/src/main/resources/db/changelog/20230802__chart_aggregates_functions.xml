<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="566642_1.1" author="anton.podoprygolov">
        <sql><![CDATA[
            alter table dict_aggregates
                add functions varchar;

            truncate table dict_aggregates;

            insert into dict_aggregates (name, sys_name, functions, ordering)
            values
                ('Количество заказов', 'order_amount', '["count"]', 1),
                ('Стоимость заказов', 'order_costs', '["sum","avg","min","max"]', 2),
                ('НЧ', 'services_amount', '["sum","avg","min","max","count"]', 3),
                ('Стоимость НЧ', 'services_expenses', '["sum","avg","min","max"]', 4),
                ('ЗЧ', 'order_detail_amount', '["sum","avg","min","max","count"]', 5),
                ('Стоимость ЗЧ', 'order_detail_costs', '["sum","avg","min","max"]', 6),
                ('Руб./км', 'order_rub_km','["sum"]', 8),
                ('Руб./ТС', 'order_rub_ts', '["sum"]', 9),
                ('Кол-во ремонтов/ТС', 'order_repair_count_ts', '["count"]', 10)
            on conflict do nothing;
        ]]></sql>
    </changeSet>
</databaseChangeLog>
