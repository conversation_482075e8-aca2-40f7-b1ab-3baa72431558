<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1870646_0.2" author="ayzhan.zheksembek">
        <sql><![CDATA[

            drop view if exists ts.repair_resources_view;

            CREATE OR REPLACE VIEW ts.repair_resources_view
            AS SELECT rr.order_id,
                rr.repair_start_date,
                rr.equnr,
                o.territory_id as vehicle_territory_id,
                o.territory_name AS vehicle_territory_name,
                o.mvz_id AS vehicle_mvz_id,
                o.mvz_name AS vehicle_mvz_name,
                o.mvz_type AS vehicle_mvz_type,
                o.atp_id AS vehicle_atp_id,
                o.atp_name AS vehicle_atp,
                o.atp_type AS vehicle_atp_type,
                o.retail_network AS vehicle_retail_network,
                o.mr_id AS vehicle_mr_id,
                o.mr_name AS vehicle_mr,
                rr.vrt,
                tw.name AS vrt_name,
                tws.id AS repair_subtype_id,
                tws.name AS repair_subtype_name,
                twt.id AS repair_type_id,
                twt.name AS repair_type_name,
                rr.ts_load_wgt,
                rr.ts_marka,
                rr.ts_model,
                rr.ts_create_date,
                rr.ts_license_num,
                rr.material_type_id,
                rr.material_type_name,
                rr.material_group_id,
                rr.material_group_name,
                rr.material_parent_id,
                rr.material_parent_name,
                rr.material_article,
                rr.material_produce,
                rr.ts_type,
                rr.ts_group,
                rr.ts_gbo,
                rr.material_id,
                rr.material_name,
                rr.gl_account,
                rr.material_amount_plan,
                rr.material_amount_fact,
                rr.material_amount,
                rr.material_expenses,
                rr.material_price,
                date_part('year'::text, rr.ts_create_date) AS vehicle_year
               FROM ts.repair_resources rr
                 LEFT JOIN ts.organizational_units_timeline o ON rr.vehicle_mvz_id::text = o.mvz_id::text AND o.start_date <= rr.repair_start_date AND rr.repair_start_date < o.end_date
                 LEFT JOIN ts.toro_works tw ON rr.vrt::text = tw.id::text
                 LEFT JOIN ts.toro_works_types twt ON tw.type_id = twt.id
                 LEFT JOIN ts.toro_works_subtypes tws ON tw.subtype_id = tws.id;

        ]]></sql>
    </changeSet>
</databaseChangeLog>
