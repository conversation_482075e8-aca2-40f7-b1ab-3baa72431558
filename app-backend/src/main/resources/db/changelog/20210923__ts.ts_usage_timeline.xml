<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="vlad.chayn">
        <sql>
            <![CDATA[
            DROP MATERIALIZED VIEW IF EXISTS ts.ts_usage_timeline;

            CREATE MATERIALIZED VIEW ts.ts_usage_timeline AS
            with q_pl as (
	            select *
		            , CAST(strmn AS timestamp without time zone)
			        + CAST(strur AS time) as start_date
		            , CAST((CASE WHEN user_stat <> 'Ч<PERSON><PERSON><PERSON>' AND user_stat <> 'ЗАКР' THEN ltrmn ELSE bezdt END) AS timestamp without time zone)
			        + CAST((CASE WHEN user_stat <> 'ЧЗАК' AND user_stat <> 'ЗАКР' THEN ltrur ELSE bezur END) AS time) as end_date
	            from pl_dt pl
	            WHERE pl_deleted = false
	                AND pl_otl = false
	                AND user_stat <> 'СТРН'
            )
            , q_atp as (
	            select mr.id as mr_id
		            , mr."name" as mr_name
		            , atp.id as atp_id
		            , atp.name as atp_name
		            , al.start_date as start_date
		            , lead(al.start_date, 1, '9999-12-31'::date) over (partition by atp.id order by al.start_date) end_date
	            from  atp_log al
		            join atp on atp.id = al.atp_id
		            join macro_region mr on mr.id = al.mr_id
	            where not al.is_deleted
            )
            , q_mvz as (
	            select ml.uid as mvz_id
		            , ml.name as mvz_name
		            , mvz_type."type" as mvz_type
		            , ml.start_date as start_date
		            , ml.atp_id as atp_id
		            , lead(ml.start_date, 1, '9999-12-31'::date) over (partition by ml.uid order by ml.start_date) as end_date
	            from mvz_log ml
		            join mvz_type on mvz_type."type" = ml."type"
	            where not ml.is_deleted
            )
            , q_ts_atp_hist as (
	            select tmh.equnr
		            , al.start_date
	            from ts_mvz_hist tmh
		            join ts_data td on td.equnr = tmh.equnr
            --		join types t on t.ts_type = td.ts_type
		            join q_mvz m on m.mvz_id = tmh.mvz and tmh.start_date < m.end_date and m.start_date < tmh.end_date
		            join atp_log al on al.atp_id = m.atp_id and tmh.start_date < al.start_date and al.start_date < tmh.end_date
            )
            , q_ts_data as (
	            select td.*
		            , case when ts_inactive = 'true' then CAST(inactive_day AS date) + CAST(inactive_time AS time) else null end as ts_inactive_date
		            , case when ts_deleted = 'true' then CAST(deleted_day AS date) + CAST(deleted_time AS time) else null end as ts_deleted_date
	            from ts_data td
	            where (ts_inactive = 'false' or inactive_day is not null)
		            and (ts_deleted = 'false' or deleted_day is not null)
            )
            , q_ts_dates as (
	            select equnr
		            , start_date
		            , end_date
	            from (
		            select equnr
			            , date as start_date
			            , lead( date, 1) over (partition by equnr order by date) as end_date
		            from (
			            select equnr, cast( start_date as timestamp) as date from ts_mvz_hist tmh
			            union
			            select equnr, cast( end_date as timestamp) as date from ts_mvz_hist tmh
			            union
			            select equnr, start_date as date from q_pl
			            union
			            select equnr, end_date as date from q_pl
			            union
			            select equnr, start_date as date from ts_ready_hist
			            union
			            select equnr, end_date as date from ts_ready_hist
			            union
			            select equnr, cast( start_date as timestamp) as date  from q_ts_atp_hist
		            ) _
	            ) _
	            where end_date is not null
            )
            , q_ts_record as (
	            select d.equnr
	                , greatest( d.start_date, pl.start_date, tps.start_date) as start_date
                    , least( d.end_date, pl.end_date, tps.end_date, td.ts_inactive_date, td.ts_deleted_date) as end_date
                    , tmh.start_date as ts_start_date
                    , tmh.end_date as ts_end_date
                    , tmh.mvz as mvz_id
                    , atp.mr_id
                    , atp.atp_id
                    , pl.qmnum
                    /**
                     * КИП
                     */
                    , case when pl.qmnum is null or pl.msaus = true then 0 else 1 end as kip_flag
                    , case when tps.remont_flag then 1 else 0 end as remont_flag
                    , case when tps.idle_flag then 1 else 0 end as idle_flag
                    , case
                        when pl.qmnum is null or pl.msaus = true then 0
                        when tps.qmnum is null then 1
                        else 0
                      end as no_trips_flag
                    , case when tmh.mvz <> pl.mvz then 1 else 0 end as wrong_pl_mvz_flag
                    , case
                        when pl.user_stat not in ('ЧЗАК', 'ЗАКР' ) then 0
                        when pl.probeg <= 0 then 1
                        when pl.zz_fuel_fact + pl.zz_gas_fact <= 0 then 1
                        else 0
		              end as abnormal_pl_flag
                    /**
                     * КТГ статус
                     */
		            , case
                        when pl.msaus									then 'remont'
                        when tr.status = '5C' and tr.reason in (2, 3)	then 'remont'
                        when tr.status is null 							then 'not ready'
                        when tr.status = '5C' and tr.reason <> 1 		then 'not ready'
                        else 'resource ready'
                      end as ktg_status
		            , pl.msaus
	            from q_ts_dates d
                    join q_ts_data td on td.equnr = d.equnr and (td.ts_inactive_date is null or d.start_date < td.ts_inactive_date) and (td.ts_deleted_date is null or d.start_date < td.ts_deleted_date)
                    left join ts_mvz_hist tmh on tmh.equnr = d.equnr and d.start_date < tmh.end_date and tmh.start_date < d.end_date
                    left join q_mvz mvz      on mvz.mvz_id = tmh.mvz and d.start_date < mvz.end_date and mvz.start_date < d.end_date
                    left join q_atp atp   on atp.atp_id = mvz.atp_id and d.start_date < atp.end_date and atp.start_date < d.end_date
                    left join q_pl pl          on pl.equnr = d.equnr and d.start_date <  pl.end_date and  pl.start_date < d.end_date
                    left join ts_ready_hist tr on tr.equnr = d.equnr and d.start_date <  tr.end_date and  tr.start_date < d.end_date
                    left join pl_segments tps on tps.qmnum = pl.qmnum and tps.equnr = d.equnr and d.start_date < tps.end_date and tps.start_date < d.end_date
            )
            , q_ts_record2 as (
                select equnr
                    , start_date
                    , end_date
                    , ts_start_date
                    , ts_end_date
                    , mvz_id
                    , mr_id
                    , atp_id
                    , qmnum
                    /**
                     * КИП
                     */
                    , kip_flag
                    , remont_flag
                    , idle_flag
                    , no_trips_flag
                    , wrong_pl_mvz_flag
                    , abnormal_pl_flag
                    /**
                     * КТГ
                     */
                    , case when ktg_status = 'remont'								then 1 else 0 end as ktg_remont_flag
                    , case when ktg_status <> 'remont'								then 1 else 0 end as ktg_flag
                    , case when ktg_status = 'not ready'							then 1 else 0 end as ktg_not_ready_flag
                    , case when ktg_status = 'not ready'		and not msaus		then 1 else 0 end as ktg_not_ready_with_pl_flag
                    , case when ktg_status = 'resource ready' 						then 1 else 0 end as ktg_resource_ready_flag
                    , case when ktg_status = 'resource ready'	and msaus is null	then 1 else 0 end as ktg_no_pl_flag
                from q_ts_record
            )
            select * from q_ts_record2;

            create index if not exists idx_ts_usage_timeline_1 on ts_usage_timeline (mvz_id, atp_id, mr_id, equnr, end_date, start_date);
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>