<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1" author="vlad.litus">
        <sql>
            <![CDATA[
            drop materialized view if exists repair;

            create materialized view ts.repair as
            with q_atp as (
                select mr.id as mr_id
                     , mr."name" as mr_name
                     , atp.id as atp_id
                     , atp.name as atp_name
                     , al.start_date as start_date
                     , lead(al.start_date, 1, '9999-12-31'::date) over (partition by atp.id order by al.start_date) end_date
                from  ts.atp_log al
                          join atp on atp.id = al.atp_id
                          join macro_region mr on mr.id = al.mr_id
                where not al.is_deleted
            )
               , q_mvz as (
                select ml.uid as mvz_id
                     , ml.name as mvz_name
                     , mvz_type.type as mvz_type
                     , ml.start_date as start_date
                     , ml.atp_id as atp_id
                     , lead(ml.start_date, 1, '9999-12-31'::date) over (partition by ml.uid order by ml.start_date) as end_date
                     , ml.ut
                from ts.mvz_log ml
                         join ts.mvz_type on mvz_type."type" = ml."type"
                where not ml.is_deleted
            )
                        select distinct on (toro.aufnr)
                            toro.aufnr                  as order_id,
                            aufk_sys_stat               as order_sys_stat,
                            toro.iwerk                  as order_ut,
                            kostv                       as order_mvz,
                            tmh.mvz                     as vehicle_mvz,
                            vatp.atp_id                 as vehicle_atp_id,
                            vatp.atp_name               as vehicle_atp,
                            vatp.mr_id                  as vehicle_mr_id,
                            vatp.mr_name                as vehicle_mr,
                            mvz.mvz_name                as mvz_name,
                            atp.atp_id                  as atp_id,
                            atp.atp_name                as atp_name,
                            atp.mr_id                   as mr_id,
                            atp.mr_name                 as mr_name,

                            toro.qmnum                  as req_id,
                            qmart                       as req_type,
                            qmel_user_stat              as req_user_stat,
                            qmel_sys_stat               as req_sys_stat,

                            auart                       as repair_kind,
                            ktext                       as order_text,
                            x5_toro                     as our_workshop,
                            ilart                       as vrt,              --toro_works.id
                            ilatx                       as vrt_name,

                            toro.equnr                  as equnr,
                            pd.tplnr                    as motorcade_number, -- pl_dt.tplnr
                            pd.pltxt                    as motorcade_name,   --pl_dt.pltxt
                            bukrs                       as vehicle_bu,

                            parnr                       as mechanic_id,
                            pd.tab1                     as driver_id,

                            belnr                       as invoice_number,
                            lifnr                       as creditor_number,
                            name1                       as creditor_name,
                            case
                            when belnr is not null then zzfactzatr
                            when belnr is null and zzfactzatr > 0 then zzfactzatr
                            when belnr is NULL and (zzfactzatr <= 0 or zzfactzatr is NULL)
                            then coalesce(zzplanzatr, 0)
                                    end                     as repair_expenses,
                            erdat                       as repair_start_date,
                            bldat                       as invoice_date,
                            augdt                       as payout_date,
                            toro.end_date               as repair_end_date,
                            toro.gltrp                  as repair_plan_end_date,
                            toro.idat1                  as deblock_date,
                            tws.id as repair_subtype_id,
                            tws.name as repair_subtype_name,
                            tws.color as  repair_subtype_color,
                            twt.id as repair_type_id,
                            twt.name as repair_type_name,
                            pd.probeg as pl_km,
                            ts_data.marka as ts_marka,
                            ts_data.model as ts_model,
                            ts_data.gbo as ts_gbo,
                            ts_data.load_wgt as ts_load_wgt,
                            ts_data.create_date as ts_create_date,
                            toro.mileage as mileage,
                            toro.mileage_date as mileage_date,

                            resources.posnr as repair_works_id,
                            resources.maktx as detail,
                            resources.bdmng as detail_quant_plan,
                            resources.enmng as detail_quant_fact,
                            resources.enwrt as detail_expenses
            from public.zpm_r01 as toro
                     join public.zpm_r01_resources as resources on toro.aufnr = resources.aufnr
                     left join ts.ts_mvz_hist tmh on tmh.equnr = toro.equnr and tmh.start_date <= toro.erdat and toro.erdat < tmh.end_date
                     left join q_mvz vm on tmh.mvz = vm.mvz_id and vm.start_date <= toro.erdat and toro.erdat < vm.end_date
                     left join q_atp vatp on vatp.atp_id = vm.atp_id and vatp.start_date <= toro.erdat and toro.erdat < vm.end_date
                     join q_mvz mvz on toro.kostv = mvz.mvz_id and mvz.start_date <= toro.erdat and toro.erdat < mvz.end_date
                     join q_atp atp on atp.atp_id = mvz.atp_id and atp.start_date <= toro.erdat and toro.erdat < atp.end_date
                     join pl_dt pd on toro.equnr = pd.equnr and toro.erdat between pd.strmn and pd.bezdt
                     join toro_works tw on tw.id = toro.ilart
                     join toro_works_subtypes tws on tws.id = tw.subtype
                     join toro_works_types twt on twt.id = tws.parent_id
                     left join ts_data on ts_data.equnr = toro.equnr
            where not aufk_deleted
              and not aufk_strn
              and not loekz;

            create index if not exists idx_repair_mvz_dates
                on repair(repair_start_date, vehicle_mvz, vehicle_atp_id, vehicle_mr_id)

            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
