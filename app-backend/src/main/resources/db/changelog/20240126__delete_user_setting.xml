<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1465678.1" author="denis.berestinskii">
        <sql endDelimiter="//"><![CDATA[
            do $$
                declare r record;
                declare i integer;
                declare ind integer;
                declare fnd boolean;
                begin
                    for r in select value, email from ts.user_setting where key = 'REPAIR_LIST_SETTINGS' loop
                        fnd := false;
                        for i in 0..(json_array_length(r.value) - 1) loop
                            if r.value->i->>'name' = 'mechanicFullName' then
                                ind := i;
                                fnd := true;
                            end if;
                        end loop;
                        if fnd then
                            update ts.user_setting
                            set value = r.value::jsonb - ind
                            where email = r.email and key = 'REPAIR_LIST_SETTINGS';
                        end if;
                    end loop;
                end
            $$
            ]]></sql>
    </changeSet>
</databaseChangeLog>
