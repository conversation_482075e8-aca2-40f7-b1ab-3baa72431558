<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1501750.1" author="anton.podoprygolov">
        <sql><![CDATA[
            drop view if exists drivers_per_vehicle_group_view;
            create view drivers_per_vehicle_group_view as
            WITH hapl AS (SELECT hr_atp_places_log.atp_id,
                                 hr_atp_places_log.hrp_id,
                                 hr_atp_places_log.deleted,
                                 hr_atp_places_log.start_date,
                                 lead(hr_atp_places_log.start_date - 1)
                                     OVER (PARTITION BY hr_atp_places_log.atp_id ORDER BY hr_atp_places_log.start_date) AS end_date
                          FROM hr_atp_places_log
                          WHERE hr_atp_places_log.deleted = false)
            SELECT atp.id   AS atp_id,
                   atp.name AS atp_name,
                   mr.name  AS mr_name,
                   hp.name  AS place_name,
                   al.retail_network,
                   al.type  AS atp_type,
                   y.year
            FROM atp
                     cross join (select extract(year from generate_series('2000-01-01':: timestamp, now() + '3 years':: interval,
                                                                          '1 year':: interval))::int AS year) as y
                     LEFT JOIN atp_log_view al ON atp.id = al.atp_id AND al.start_date <= CURRENT_DATE AND
                                                  COALESCE(al.end_date, '9999-01-01'::date) >= CURRENT_DATE
                     LEFT JOIN macro_region mr ON al.mr_id = mr.id
                     LEFT JOIN hapl ON atp.id = hapl.atp_id AND hapl.start_date <= CURRENT_DATE AND
                                       COALESCE(hapl.end_date, '9999-01-01'::date) >= CURRENT_DATE
                     LEFT JOIN hr_places hp ON hapl.hrp_id = hp.id
            WHERE atp.id NOT IN (SELECT atp_id
                                 FROM (SELECT *,
                                              COALESCE(LEAD(start_date) OVER (PARTITION BY atp_id ORDER BY start_date ASC),
                                                       '9999-12-31') end_date
                                       FROM (SELECT id,
                                                    atp_id,
                                                    hrp_id,
                                                    LAG(hrp_id) OVER (PARTITION BY atp_id ORDER BY start_date ASC) prev_hrp_id,
                                                     start_date
                                             FROM ts.hr_atp_places_log
                                             WHERE deleted = FALSE) a
                                       WHERE (prev_hrp_id != hrp_id OR prev_hrp_id IS NULL)
                                         AND hrp_id = 65) b
                                 WHERE b.start_date <= make_date(year, 1, 1)
                                   AND b.end_date > make_date(year, 12, 31));
        ]]></sql>
    </changeSet>
</databaseChangeLog>
