package com.x5.logistics.util

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.WeekFields
import java.util.*

object DateFormatUtils {
    private val formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val monthFormatter = DateTimeFormatter.ofPattern("MM.yyyy")
    private val yearFormatter = DateTimeFormatter.ofPattern("yyyy")

    fun formatDay(date: LocalDate): String = formatter.format(date)

    fun formatReportWeek(date: LocalDate): String {
        // Отчетная неделя начинается с пятницы, заканчивается четвергом
        val dayOfWeek = date.dayOfWeek.value
        val daysToFriday = if (dayOfWeek >= 5) dayOfWeek - 5 else dayOfWeek + 2
        val startOfReportWeek = date.minusDays(daysToFriday.toLong())
        val weekNumber = (startOfReportWeek.dayOfYear / 7) + 1
        return "ОН $weekNumber.${startOfReportWeek.year}"
    }

    fun formatCalendarWeek(date: LocalDate): String {
        val weekNumber = date.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear())
        return "Н $weekNumber.${date.year}"
    }

    fun formatMonth(date: LocalDate): String = monthFormatter.format(date)

    fun formatQuarter(date: LocalDate): String {
        val quarter = (date.monthValue - 1) / 3 + 1
        return "Кв $quarter.${date.year}"
    }

    fun formatYear(date: LocalDate): String = yearFormatter.format(date)

}