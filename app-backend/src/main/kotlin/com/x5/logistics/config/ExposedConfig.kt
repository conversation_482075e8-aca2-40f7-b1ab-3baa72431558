package com.x5.logistics.config

import jakarta.persistence.EntityManagerFactory
import org.jetbrains.exposed.spring.autoconfigure.ExposedAutoConfiguration
import org.jetbrains.exposed.sql.DatabaseConfig
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.transaction.TransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import org.springframework.transaction.annotation.Transactional

@Configuration
@ImportAutoConfiguration(
    value = [ExposedAutoConfiguration::class],
    exclude = [DataSourceTransactionManagerAutoConfiguration::class]
)
class ExposedConfig(
) {

    @Bean
    fun databaseConfig() = DatabaseConfig {
        useNestedTransactions = true
    }
}

@Transactional(transactionManager = "springTransactionManager")
annotation class ExposedTransactional

@Configuration
@EnableTransactionManagement
class SpringTransactions(
    val entityManagerFactory: EntityManagerFactory
) {
    @Bean
    @Primary
    fun transactionManager(): TransactionManager {
        val transactionManager = JpaTransactionManager()
        transactionManager.entityManagerFactory = entityManagerFactory
        return transactionManager
    }
}