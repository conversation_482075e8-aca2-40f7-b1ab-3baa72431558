package com.x5.logistics.repository.dictionary.rr

import com.x5.logistics.data.dictionary.rr.Month
import com.x5.logistics.data.dictionary.rr.RrDictionaryEntity
import com.x5.logistics.data.dictionary.rr.RrDictionaryEntityId
import com.x5.logistics.rest.dto.dictionary.DictionaryInfoDto
import com.x5.logistics.service.dictionary.DictionaryUpdateInfo
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import jakarta.persistence.Tuple
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant
import java.time.ZoneId

@Repository
interface RrDictionaryEntityRepo: JpaRepository<RrDictionaryEntity, RrDictionaryEntityId>, RrDictionaryEntityRepoCustom {
    @Query("""
        select entity
        from RrDictionaryEntity entity
        where entity.year = :year
        and entity.atp.id = :atpId
        and entity.tonnage = :tonnage
    """)
    fun findByYearAtpTonnage(
        @Param("year") year: Int,
        @Param("atpId") atpId: Long,
        @Param("tonnage") tonnage: BigDecimal
    ): List<RrDictionaryEntity>

    @Modifying
    @Query("""
        delete from RrDictionaryEntity entity
        where entity.year = :year
        and entity.atp.id = :atpId
        and entity.tonnage = :tonnage
    """)
    fun removeByYearAtpTonnage(
        @Param("year") year: Int,
        @Param("atpId") atpId: Long,
        @Param("tonnage") tonnage: BigDecimal
    )

    fun findByYearAndAtp_IdAndTonnageAndToroWork_IdAndMonth(
        year: Int,
        id: Long,
        tonnage: BigDecimal,
        toroWorkId: String,
        month: Month
    ): RrDictionaryEntity?

}

interface RrDictionaryEntityRepoCustom {
    fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T
}

@Service
class RrDictionaryEntityRepoCustomImpl: RrDictionaryEntityRepoCustom, DictionaryUpdateInfo {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    override fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T =
        entityManager.jpaFunction()

    override fun getLastUpdateInfo(): DictionaryInfoDto {
        val tupleRes = (entityManager.createNativeQuery(
            """
                (select updated_at as updatedat, updated_by as author
                 from rr_dictionary
                 where updated_by != 'system'
                 order by updated_at desc nulls last 
                 limit 1)
                union
                (select created_at as updatedat, created_by as author
                 from rr_dictionary
                 where created_by != 'system'
                 order by created_at desc nulls last 
                 limit 1)
                order by updatedat desc nulls last 
                limit 1
            """.trimIndent(), Tuple::class.java
        ).resultList as List<Tuple>).firstOrNull()
        return DictionaryInfoDto(
            name = "rr_dictionary",
            updatedBy = (tupleRes?.get("author") as String?),
            updatedAt = (tupleRes?.get("updatedat") as Instant?)?.atZone(ZoneId.systemDefault())?.toLocalDateTime(),
        )
    }
}
