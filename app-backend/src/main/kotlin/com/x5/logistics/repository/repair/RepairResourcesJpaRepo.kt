package com.x5.logistics.repository.repair

import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service

@Repository
interface RepairResourcesJpaRepo: JpaRepository<RepairResourcesDao, String>, RepairResourcesJpaRepoCustom {
    fun findAllByOrderIdInOrderByOrderIdAscMaterialIdAsc(ids: Collection<Long>): List<RepairResourcesDao>
}

interface RepairResourcesJpaRepoCustom {
    fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T
}

@Service
class RepairResourcesJpaRepoCustomImpl : RepairResourcesJpaRepoCustom {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    override fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T = entityManager.jpaFunction()
}
