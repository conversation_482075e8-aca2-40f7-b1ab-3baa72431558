package com.x5.logistics.repository

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.Amortization
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.amortization.AmortizationReportColumn
import com.x5.logistics.rest.dto.amortization.AmortizationReportColumnFilter
import com.x5.logistics.rest.dto.amortization.AmortizationReportItem
import com.x5.logistics.rest.dto.amortization.AmortizationReportMassFilterReq
import com.x5.logistics.rest.dto.amortization.AmortizationReportReq
import com.x5.logistics.rest.dto.amortization.AmortizationReportResp
import com.x5.logistics.rest.dto.amortization.AmortizationReportSortOrder
import com.x5.logistics.util.checkFilterValues
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.ColumnSet
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andHaving
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.max
import org.jetbrains.exposed.sql.sum
import org.springframework.stereotype.Component
import java.time.LocalDate

@Suppress("UNCHECKED_CAST")
@Component
class AmortizationExposedRepo {
    val logger = getLogger()

    @ExposedTransactional
    fun getFilterValues(req: AmortizationReportMassFilterReq): List<LabeledValue> {
        val requestedColumn = req.request.name
        val request = AmortizationReportReq(
            pageNumber = null,
            pageSize = null,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = (req.columns + requestedColumn).distinct(),
            sort = listOf(AmortizationReportSortOrder(requestedColumn, true)),
            filters = req.filters + listOf(
                AmortizationReportColumnFilter(
                    name = requestedColumn,
                    condition = FilterCondition.contain,
                    value = req.request.value
                )
            ),
        )
        val queryData = prepareQueries(
            req = request,
            totals = false,
            strictFilters = false
        )

        logger.debug(queryData.query.prepareSQL(QueryBuilder(false)))
        return queryData.query.map {
            it[queryData.columns[requestedColumn] as ExpressionAlias]
        }.distinct().map {
            LabeledValue(
                it?.toString() ?: "Пусто",
                it.toString()
            )
        }
    }

    @ExposedTransactional
    fun checkMassFilter(req: AmortizationReportMassFilterReq): MassFilterResp {
        val requestedColumn = req.request.name
        val request = AmortizationReportReq(
            pageNumber = null,
            pageSize = null,
            from = req.from,
            to = req.to,
            geoFilter = req.geoFilter,
            columns = (req.columns + requestedColumn).distinct(),
            sort = listOf(AmortizationReportSortOrder(requestedColumn, true)),
            filters = req.filters + listOf(
                AmortizationReportColumnFilter(
                    name = requestedColumn,
                    condition = FilterCondition.equal,
                    value = req.request.value
                )
            ),
        )
        val queryData = prepareQueries(request, false)

        logger.debug(queryData.query.prepareSQL(QueryBuilder(false)))
        val values = queryData.query.map {
            it[queryData.columns[requestedColumn] as ExpressionAlias]?.toString() ?: "Пусто"
        }.distinct()
        val userValues = req.request.value
        return checkFilterValues(userValues, values)
    }

    @ExposedTransactional
    fun getReport(req: AmortizationReportReq): AmortizationReportResp {
        val queryData = prepareQueries(req)
        var count: Long? = null
        val query = queryData.query
        val columns = queryData.columns
        logger.debug(query.prepareSQL(QueryBuilder(false)))

        val items = query.map { row ->
            if (count == null && queryData.countAlias != null) {
                count = row[queryData.countAlias]
            }

            AmortizationReportItem(
                assetId = if (AmortizationReportColumn.assetId in req.columns) row[columns[AmortizationReportColumn.assetId] as ExpressionAlias<String?>] else null,
                amoDate = if (AmortizationReportColumn.amoDate in req.columns) row[columns[AmortizationReportColumn.amoDate] as ExpressionAlias<LocalDate?>] else null,
                ter = if (AmortizationReportColumn.ter in req.columns) row[columns[AmortizationReportColumn.ter] as ExpressionAlias<String?>] else null,
                mr = if (AmortizationReportColumn.mr in req.columns) row[columns[AmortizationReportColumn.mr] as ExpressionAlias<String?>] else null,
                atp = if (AmortizationReportColumn.atp in req.columns) row[columns[AmortizationReportColumn.atp] as ExpressionAlias<String?>] else null,
                mvz = if (AmortizationReportColumn.mvz in req.columns) row[columns[AmortizationReportColumn.mvz] as ExpressionAlias<String?>] else null,
                mvzName = if (AmortizationReportColumn.mvzName in req.columns) row[columns[AmortizationReportColumn.mvzName] as ExpressionAlias<String?>] else null,
                retailNetwork = if (AmortizationReportColumn.retailNetwork in req.columns) row[columns[AmortizationReportColumn.retailNetwork] as ExpressionAlias<String?>] else null,
                atpType = if (AmortizationReportColumn.atpType in req.columns) row[columns[AmortizationReportColumn.atpType] as ExpressionAlias<String?>] else null,
                mvzType = if (AmortizationReportColumn.mvzType in req.columns) row[columns[AmortizationReportColumn.mvzType] as ExpressionAlias<String?>] else null,
                osClass = if (AmortizationReportColumn.osClass in req.columns) row[columns[AmortizationReportColumn.osClass] as ExpressionAlias<String?>] else null,
                be = if (AmortizationReportColumn.be in req.columns) row[columns[AmortizationReportColumn.be] as ExpressionAlias<String?>] else null,
                inventoryNum = if (AmortizationReportColumn.inventoryNum in req.columns) row[columns[AmortizationReportColumn.inventoryNum] as ExpressionAlias<String?>] else null,
                createDate = if (AmortizationReportColumn.createDate in req.columns) row[columns[AmortizationReportColumn.createDate] as ExpressionAlias<LocalDate?>] else null,
                activeDate = if (AmortizationReportColumn.activeDate in req.columns) row[columns[AmortizationReportColumn.activeDate] as ExpressionAlias<LocalDate?>] else null,
                firstFinTransactionDate = if (AmortizationReportColumn.firstFinTransactionDate in req.columns) row[columns[AmortizationReportColumn.firstFinTransactionDate] as ExpressionAlias<LocalDate?>] else null,
                firstTripDate = if (AmortizationReportColumn.firstTripDate in req.columns) row[columns[AmortizationReportColumn.firstTripDate] as ExpressionAlias<LocalDate?>] else null,
                inactiveDate = if (AmortizationReportColumn.inactiveDate in req.columns) row[columns[AmortizationReportColumn.inactiveDate] as ExpressionAlias<LocalDate?>] else null,
                fleetNum = if (AmortizationReportColumn.fleetNum in req.columns) row[columns[AmortizationReportColumn.fleetNum] as ExpressionAlias<String?>] else null,
                equnr = if (AmortizationReportColumn.equnr in req.columns) row[columns[AmortizationReportColumn.equnr] as ExpressionAlias<String?>] else null,
                tsTer = if (AmortizationReportColumn.tsTer in req.columns) row[columns[AmortizationReportColumn.tsTer] as ExpressionAlias<String?>] else null,
                tsMr = if (AmortizationReportColumn.tsMr in req.columns) row[columns[AmortizationReportColumn.tsMr] as ExpressionAlias<String?>] else null,
                tsAtp = if (AmortizationReportColumn.tsAtp in req.columns) row[columns[AmortizationReportColumn.tsAtp] as ExpressionAlias<String?>] else null,
                tsMvz = if (AmortizationReportColumn.tsMvz in req.columns) row[columns[AmortizationReportColumn.tsMvz] as ExpressionAlias<String?>] else null,
                tsMvzName = if (AmortizationReportColumn.tsMvzName in req.columns) row[columns[AmortizationReportColumn.tsMvzName] as ExpressionAlias<String?>] else null,
                tsRetailNetwork = if (AmortizationReportColumn.tsRetailNetwork in req.columns) row[columns[AmortizationReportColumn.tsRetailNetwork] as ExpressionAlias<String?>] else null,
                tsAtpType = if (AmortizationReportColumn.tsAtpType in req.columns) row[columns[AmortizationReportColumn.tsAtpType] as ExpressionAlias<String?>] else null,
                tsMvzType = if (AmortizationReportColumn.tsMvzType in req.columns) row[columns[AmortizationReportColumn.tsMvzType] as ExpressionAlias<String?>] else null,
                tsClass = if (AmortizationReportColumn.tsClass in req.columns) row[columns[AmortizationReportColumn.tsClass] as ExpressionAlias<String?>] else null,
                tsBe = if (AmortizationReportColumn.tsBe in req.columns) row[columns[AmortizationReportColumn.tsBe] as ExpressionAlias<String?>] else null,
                tsGroup = if (AmortizationReportColumn.tsGroup in req.columns) row[columns[AmortizationReportColumn.tsGroup] as ExpressionAlias<String?>] else null,
                tsType = if (AmortizationReportColumn.tsType in req.columns) row[columns[AmortizationReportColumn.tsType] as ExpressionAlias<String?>] else null,
                tsBrand = if (AmortizationReportColumn.tsBrand in req.columns) row[columns[AmortizationReportColumn.tsBrand] as ExpressionAlias<String?>] else null,
                tsModel = if (AmortizationReportColumn.tsModel in req.columns) row[columns[AmortizationReportColumn.tsModel] as ExpressionAlias<String?>] else null,
                tsTonnage = if (AmortizationReportColumn.tsTonnage in req.columns) row[columns[AmortizationReportColumn.tsTonnage] as ExpressionAlias<Float?>] else null,
                tsGbo = if (AmortizationReportColumn.tsGbo in req.columns) row[columns[AmortizationReportColumn.tsGbo] as ExpressionAlias<String?>] else null,
                tsCreateYear = if (AmortizationReportColumn.tsCreateYear in req.columns) row[columns[AmortizationReportColumn.tsCreateYear] as ExpressionAlias<Int?>] else null,
                tsLicenseNum = if (AmortizationReportColumn.tsLicenseNum in req.columns) row[columns[AmortizationReportColumn.tsLicenseNum] as ExpressionAlias<String?>] else null,
                tsMileage = if (AmortizationReportColumn.tsMileage in req.columns) row[columns[AmortizationReportColumn.tsMileage] as ExpressionAlias<Double?>] else null,
                tsOwnershipType = if (AmortizationReportColumn.tsOwnershipType in req.columns) row[columns[AmortizationReportColumn.tsOwnershipType] as ExpressionAlias<String?>] else null,
                tsCreateDate = if (AmortizationReportColumn.tsCreateDate in req.columns) row[columns[AmortizationReportColumn.tsCreateDate] as ExpressionAlias<LocalDate?>] else null,
                tsInactiveDate = if (AmortizationReportColumn.tsInactiveDate in req.columns) row[columns[AmortizationReportColumn.tsInactiveDate] as ExpressionAlias<LocalDate?>] else null,
                startValue = if (AmortizationReportColumn.startValue in req.columns) row[columns[AmortizationReportColumn.startValue] as ExpressionAlias<Double?>] else null,
                planAmo = if (AmortizationReportColumn.planAmo in req.columns) row[columns[AmortizationReportColumn.planAmo] as ExpressionAlias<Double?>] else null,
                factAmo = if (AmortizationReportColumn.factAmo in req.columns) row[columns[AmortizationReportColumn.factAmo] as ExpressionAlias<Double?>] else null,
                remainingCost = if (AmortizationReportColumn.remainingCost in req.columns) row[columns[AmortizationReportColumn.remainingCost] as ExpressionAlias<Double?>] else null,
                accumulatedAmo = if (AmortizationReportColumn.accumulatedAmo in req.columns) row[columns[AmortizationReportColumn.accumulatedAmo] as ExpressionAlias<Double?>] else null
            )
        }

        return AmortizationReportResp(
            count = count?.toInt() ?: 0,
            pageNumber = req.pageNumber ?: 0,
            pageSize = req.pageSize ?: 0,
            items = items
        )
    }

    private fun prepareQueries(
        req: AmortizationReportReq,
        totals: Boolean = true,
        strictFilters: Boolean = true
    ): AmortizationQueries {
        val userColumns = (req.columns + req.filters.map { it.name }).toSet()

        val osColumns = sequenceOf(
            AmortizationReportColumn.ter to OrganizationalUnitsTimelineTable.territoryName.alias("ter")
                .takeIf { AmortizationReportColumn.ter in userColumns || req.geoFilter.territory?.isNotEmpty() == true },
            AmortizationReportColumn.mr to OrganizationalUnitsTimelineTable.mrName.alias("mr")
                .takeIf { AmortizationReportColumn.mr in userColumns || req.geoFilter.mr?.isNotEmpty() == true },
            AmortizationReportColumn.atp to OrganizationalUnitsTimelineTable.atpName.alias("atp")
                .takeIf { AmortizationReportColumn.atp in userColumns || req.geoFilter.atp?.isNotEmpty() == true },
            AmortizationReportColumn.retailNetwork to OrganizationalUnitsTimelineTable.retailNetwork.alias("retail_network")
                .takeIf { AmortizationReportColumn.retailNetwork in userColumns || req.geoFilter.retailNetwork?.isNotEmpty() == true },
            AmortizationReportColumn.atpType to OrganizationalUnitsTimelineTable.atpType.alias("atp_type")
                .takeIf { AmortizationReportColumn.atpType in userColumns || req.geoFilter.atpType?.isNotEmpty() == true },
            AmortizationReportColumn.mvzType to OrganizationalUnitsTimelineTable.mvzType.alias("mvz_type")
                .takeIf { AmortizationReportColumn.mvzType in userColumns || req.geoFilter.mvzType?.isNotEmpty() == true }
        ).filterNot { it.second == null }.associateBy({ it.first }, { it.second!! })

        val tsColumns = sequenceOf(
            AmortizationReportColumn.tsTer to OrganizationalUnitsTimelineTable.territoryName.alias("ts_ter")
                .takeIf { AmortizationReportColumn.tsTer in userColumns },
            AmortizationReportColumn.tsMr to OrganizationalUnitsTimelineTable.mrName.alias("ts_mr")
                .takeIf { AmortizationReportColumn.tsMr in userColumns },
            AmortizationReportColumn.tsAtp to OrganizationalUnitsTimelineTable.atpName.alias("ts_atp")
                .takeIf { AmortizationReportColumn.tsAtp in userColumns },
            AmortizationReportColumn.tsRetailNetwork to OrganizationalUnitsTimelineTable.retailNetwork.alias("ts_retail_network")
                .takeIf { AmortizationReportColumn.tsRetailNetwork in userColumns },
            AmortizationReportColumn.tsAtpType to OrganizationalUnitsTimelineTable.atpType.alias("ts_atp_type")
                .takeIf { AmortizationReportColumn.tsAtpType in userColumns },
            AmortizationReportColumn.tsMvzType to OrganizationalUnitsTimelineTable.mvzType.alias("ts_mvz_type")
                .takeIf { AmortizationReportColumn.tsMvzType in userColumns }
        ).filterNot { it.second == null }.associateBy({ it.first }, { it.second!! })

        val osQueryAlias =
            if (osColumns.isNotEmpty()) {
                OrganizationalUnitsTimelineTable.select(
                    osColumns.values +
                    listOf(
                        OrganizationalUnitsTimelineTable.mvzId,
                        OrganizationalUnitsTimelineTable.startDate,
                        OrganizationalUnitsTimelineTable.endDate)
                ).applyGeoFilter(req.geoFilter).alias("os_query")
            } else null

        val tsQueryAlias =
            if (tsColumns.isNotEmpty()) {
                OrganizationalUnitsTimelineTable.select(
                    tsColumns.values +
                    listOf(
                        OrganizationalUnitsTimelineTable.mvzId,
                        OrganizationalUnitsTimelineTable.startDate,
                        OrganizationalUnitsTimelineTable.endDate)
                ).alias("ts_query")
            } else null

        val maxAmoDateAlias = Amortization.amoDate.max().alias("max_amo_date")
        val sumPlanAmoAlias = Amortization.planAmo.sum().alias("plan_amo")
        val sumFactAmoAlias = Amortization.factAmo.sum().alias("fact_amo")
        val amoValues = sequenceOf(
            AmortizationReportColumn.startValue to Amortization.startValue.sum()
                .takeIf { AmortizationReportColumn.startValue in userColumns}?.alias("start_value"),
            AmortizationReportColumn.remainingCost to Amortization.remainderCost.sum()
                .takeIf{ AmortizationReportColumn.remainingCost in userColumns}?.alias("remainder_cost"),
            AmortizationReportColumn.accumulatedAmo to Amortization.accumulatedAmo.sum()
                .takeIf{ AmortizationReportColumn.accumulatedAmo in userColumns}?.alias("accumulated_amo")
        ).filterNot { it.second == null }.associateBy({ it.first }, { it.second!! })

        //если дата амортизации не выбрано и выбрана хотя бы одна колонка из (startValue, remainingCost, accumulatedAmo)
        //то делаем join (для вычисления хронологически последние значения для каждого ОС в группе)
        val subQueryAlias =
            if (AmortizationReportColumn.amoDate !in userColumns && amoValues.isNotEmpty()) {
                Amortization.select(
                    maxAmoDateAlias,
                    Amortization.assetId,
                    sumPlanAmoAlias,
                    sumFactAmoAlias
                ).where {
                    (Amortization.amoDate greaterEq req.from) and (Amortization.amoDate lessEq req.to)
                }.groupBy(Amortization.assetId).alias("sub_query")
            } else null

        var sourceQuery: ColumnSet = Amortization

        if (osQueryAlias != null) {//если выбраны колонки ОС, join по mvzId
            sourceQuery = sourceQuery.join(
                otherTable = osQueryAlias,
                joinType = JoinType.INNER,
                additionalConstraint = {
                    Amortization.mvzId eq osQueryAlias[OrganizationalUnitsTimelineTable.mvzId] and
                            (osQueryAlias[OrganizationalUnitsTimelineTable.startDate] lessEq Amortization.amoDate) and
                            (Amortization.amoDate less osQueryAlias[OrganizationalUnitsTimelineTable.endDate])
                })
        }

        if (tsQueryAlias != null) {//если выбраны колонки ЕО, join по tsMvzId
            sourceQuery = sourceQuery.join(
                otherTable = tsQueryAlias,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    Amortization.tsMvzId eq tsQueryAlias[OrganizationalUnitsTimelineTable.mvzId] and
                            (tsQueryAlias[OrganizationalUnitsTimelineTable.startDate] lessEq Amortization.amoDate) and
                            (Amortization.amoDate less tsQueryAlias[OrganizationalUnitsTimelineTable.endDate])
                })
        }

        if (subQueryAlias != null) {//если выбраны числовые показатели
            sourceQuery = sourceQuery.join(
                otherTable = subQueryAlias,
                joinType = JoinType.INNER,
                additionalConstraint = {
                    Amortization.assetId eq subQueryAlias[Amortization.assetId] and
                            (Amortization.amoDate eq subQueryAlias[maxAmoDateAlias])
                })
        }

        //если выбраны числовые показатели plan_amo и fact_amo
        val subQueryColumns = sequenceOf(
            AmortizationReportColumn.planAmo to
                    if (AmortizationReportColumn.planAmo in userColumns) {
                        if (subQueryAlias != null) {//если делаем join - берем сумму их сумм из подзапроса
                            Sum(subQueryAlias[sumPlanAmoAlias], DoubleColumnType()).alias("plan_amo")
                        } else sumPlanAmoAlias//иначе Суммировать эти значения при группировке
                    } else null,
            AmortizationReportColumn.factAmo to
                    if (AmortizationReportColumn.factAmo in userColumns) {
                        if (subQueryAlias != null) {
                            Sum(subQueryAlias[sumFactAmoAlias], DoubleColumnType()).alias("fact_amo")
                        } else sumFactAmoAlias
                    } else null
        ).filterNot { it.second == null }.associateBy({ it.first }, { it.second!! })

        val count = CountAll().alias("count").takeIf { totals }

        //берем expression для колонок из Amortization (все, кроме орг.структуры и числовых колонок амортизации)
        val amortizationColumns =
            req.columns.filterNot { col -> col in (osColumns + tsColumns + subQueryColumns + amoValues) }
                .associateWith { it.exposedExpression.alias(it.name) }

        val columnAliases = amortizationColumns.values +
                    //если выбраны колонки ОС, берем из подзапроса osQueryAlias
                    (if (osQueryAlias != null) {
                        osColumns.filter { it.key in req.columns }.values
                            .map { osCol -> osQueryAlias[osCol].alias(osCol.alias) }
                    } else emptyList()) +
                    //если выбраны колонки ЕО, берем из подзапроса tsQueryAlias
                    (if (tsQueryAlias != null) {
                        tsColumns.filter { it.key in req.columns }.values
                            .map { tsCol -> tsQueryAlias[tsCol].alias(tsCol.alias) }
                    } else emptyList()) +
                    //числовые показатели plan_amo, fact_amo
                    subQueryColumns.filter { it.key in req.columns }.values +
                    //числовые показатели startValue, remainingCost, accumulatedAmo
                    amoValues.filter { it.key in req.columns }.values

        val query = sourceQuery.select(columnAliases + listOfNotNull(count))
            .where { (Amortization.amoDate greaterEq req.from) and (Amortization.amoDate lessEq req.to) }
            .apply {
                if (!req.geoFilter.mvz.isNullOrEmpty()) {
                    andWhere { Amortization.mvzId inList req.geoFilter.mvz }
                }
            }
            .apply {
                req.filters.forEach {
                    if (it.name in amoValues || it.name in subQueryColumns) {
                        andHaving {
                            val exp = if (it.name in amoValues) {
                                amoValues[it.name]?.delegate!!
                            } else {
                                subQueryColumns[it.name]?.delegate!!
                            }
                            buildFilterPredicate(
                                condition = it.condition,
                                values = it.value,
                                type = it.name.type,
                                exposedExpression = exp
                            )
                        }
                    } else {
                        andWhere {
                            buildFilterPredicate(
                                condition = it.condition,
                                values = it.value,
                                type = it.name.type,
                                exposedExpression = if (it.name in osColumns && osQueryAlias != null) {
                                    osQueryAlias[it.name.exposedExpression]
                                } else if (it.name in tsColumns && tsQueryAlias != null) {
                                    tsQueryAlias[it.name.exposedExpression]
                                } else it.name.exposedExpression,
                                strictFilter = strictFilters
                            )
                        }
                    }
                }
            }.apply {
                req.sort.forEach { sort ->
                    val exp = if (sort.column in osColumns && osQueryAlias != null) {
                        osQueryAlias[sort.column.exposedExpression]
                    } else if (sort.column in tsColumns && tsQueryAlias != null) {
                        tsQueryAlias[sort.column.exposedExpression]
                    } else if (sort.column in subQueryColumns) {
                        (subQueryColumns[sort.column] as ExpressionAlias<Double?>).aliasOnlyExpression()
                    } else if (sort.column in amoValues) {
                        (amoValues[sort.column] as ExpressionAlias<Double?>).aliasOnlyExpression()
                    } else sort.column.exposedExpression

                    orderBy(exp, if (sort.asc) SortOrder.ASC else SortOrder.DESC)
                }
            }.apply {
                if (req.pageSize != null && req.pageNumber != null) {
                    limit(
                        n = req.pageSize,
                        offset = (req.pageNumber * req.pageSize).toLong()
                    )
                }
            }.groupBy(*columnAliases.filterNot { it in subQueryColumns.values || it in amoValues.values }.toTypedArray())

        return AmortizationQueries(
            query = query,
            columns = amortizationColumns + amoValues + subQueryColumns +
                    osColumns.asSequence()
                        .map { (k, v) -> if (osQueryAlias != null) {
                            k to osQueryAlias[v].alias(v.alias)
                        } else k to null } +
                    tsColumns.asSequence()
                        .map { (k, v) -> if (tsQueryAlias != null) {
                            k to tsQueryAlias[v].alias(v.alias)
                        } else k to null },
            countAlias = count
        )
    }

    private class AmortizationQueries(
        val query: Query,
        var columns: Map<AmortizationReportColumn, ExpressionAlias<out Any?>?>,
        val countAlias: ExpressionAlias<Long>?,
    )
}