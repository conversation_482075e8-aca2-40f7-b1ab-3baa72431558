package com.x5.logistics.repository.dictionary.hr.drivers;

import com.x5.logistics.data.dictionary.hr.drivers.HrDriversPerVehicleDictionaryGroupViewDao
import com.x5.logistics.data.dictionary.hr.drivers.HrDriversPerVehicleDictionaryGroupViewId
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service

@Repository
interface HrDriversPerVehicleDictionaryGroupRepo: JpaRepository<HrDriversPerVehicleDictionaryGroupViewDao, HrDriversPerVehicleDictionaryGroupViewId>,
    HrDriversPerVehicleDictionaryGroupRepoCustom {
    @Query("""
        select distinct e.atpName
        from HrDriversPerVehicleDictionaryGroupViewDao e
        order by e.atpName
    """)
    fun getAtpNames(): List<String>

    @Query("""
        select distinct e.mrName
        from HrDriversPerVehicleDictionaryGroupViewDao e
        order by e.mrName
    """)
    fun getMrNames(): List<String>

    @Query("""
        select distinct e.placeName
        from HrDriversPerVehicleDictionaryGroupViewDao e
        order by e.placeName
    """)
    fun getPlaceNames(): List<String>

    @Query("""
        select distinct e.retailNetwork
        from HrDriversPerVehicleDictionaryGroupViewDao e
        order by e.retailNetwork
    """)
    fun getRetailNetworkNames(): List<String>

    @Query("""
        select distinct e.atpType
        from HrDriversPerVehicleDictionaryGroupViewDao e
        order by e.atpType
    """)
    fun getAtpTypes(): List<String>
}

interface HrDriversPerVehicleDictionaryGroupRepoCustom {
    fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T
}

@Service
class HrDriversPerVehicleDictionaryGroupRepoCustomImpl: HrDriversPerVehicleDictionaryGroupRepoCustom {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    override fun <T> getByJpa(jpaFunction: EntityManager.() -> T): T =
        entityManager.jpaFunction()
}
