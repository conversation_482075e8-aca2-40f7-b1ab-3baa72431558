package com.x5.logistics.repository.repair

import com.x5.logistics.repository.AbstractDetailsRepository
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.repair.modal.RepairModalTopOrdersResp
import com.x5.logistics.rest.dto.repair.modal.RepairTopOrdersData
import com.x5.logistics.rest.dto.repair.modal.expenses.RepairModalExpensesData
import com.x5.logistics.rest.dto.repair.modal.expenses.RepairModalExpensesResp
import com.x5.logistics.rest.dto.repair.modal.expenses.RepairModalMinMaxExpensesResp
import com.x5.logistics.rest.dto.repair.modal.orders.RepairModalOrdersBarData
import com.x5.logistics.rest.dto.repair.modal.orders.RepairModalOrdersReq
import com.x5.logistics.util.getLogger
import getGeoFiltersClause
import jakarta.persistence.EntityManager
import jakarta.persistence.Query
import jakarta.persistence.Tuple
import org.hibernate.query.TypedParameterValue
import org.hibernate.type.StandardBasicTypes
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.math.BigInteger
import java.util.*

/**
 * Repository for JRAAVTO-39_Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам
 * https://wiki.x5.ru/pages/viewpage.action?pageId=280896393
 * <AUTHOR> Belogolovsky ( <EMAIL> )
 */
@Repository
@Suppress("UNCHECKED_CAST")
class RepairModalOrdersRepository(
    em: EntityManager,
    @Qualifier("monthLocale") monthLocale: Locale
) : AbstractDetailsRepository(em, monthLocale) {
    private val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    private val commonQuery = """
        -- Топ ремонтов + распределение на СТО
        with repair_ur as (select
                                  repair.order_id
                                , repair.equnr
                                , repair.ts_marka
                                , repair.ts_model
                                , repair.ts_load_wgt
                                , ts_data.license_num
                                , repair.our_workshop
                                , repair.repair_subtype_id
                                , repair.repair_subtype_name
                                , repair.repair_expenses
                                from ts.repair
                                join ts.ts_data on ts_data.equnr = repair.equnr
                                where repair_start_date >= cast(:start_date as date) 
                                and repair_start_date < cast(:end_date as date)
                                :geo_filter_repair
                                and repair.ts_model in (:models)
                   )
        -- Максимальные и минимальные стоимости ремонтов
        , max_min_expenses as (select  
                               max(repair_expenses) as max_expenses
                             , min(repair_expenses) as min_expenses
                               from repair_ur
                   )
        -- Группировка всех заказов ТОРО
        , expenses_group_all as (select
                                   our_workshop
                                 , repair_subtype_id
                                 , repair_subtype_name
                                 , count(order_id) as repair_count_all
                                 , string_agg(to_char(repair_expenses, 'FM99999999990D099999999'), ',') as repair_expenses_all_array
                                 , sum(repair_expenses) as repair_expenses_all
                                 , max(repair_expenses) as max_expense
                               from repair_ur
                               group by 
                                   our_workshop
                                 , repair_subtype_id
                                 , repair_subtype_name
        )

        -- Группировка заказов ТОРО, где стоимость ремонта отфильтрована
        , expenses_group_filter as (select
                                   our_workshop
                                 , repair_subtype_id
                                 , repair_subtype_name
                                 , count(order_id) as repair_count_filter
                                 , sum(repair_expenses) as repair_expenses_filter
                               from repair_ur, max_min_expenses
                               where repair_expenses >= coalesce(:filter_min, min_expenses)
                               and   repair_expenses <= coalesce(:filter_max, max_expenses)
                               group by 
                                   our_workshop
                                 , repair_subtype_id
                                 , repair_subtype_name
        )
    """.trimIndent()

    private val minMaxExpensesQuery = commonQuery + """
        -- ЛЕВАЯ ЧАСТЬ
        -- AC-JRAAVTO-39-5 значения максимума и минимума по заказам           
        select  max_expenses -- Максимальная стоимость заказа ТОРО, руб
             , min_expenses -- Минимальная стоимость заказа ТОРО, руб
        from max_min_expenses
    """.trimIndent()

    private val distributionQuery = commonQuery + """
        -- ЛЕВАЯ ЧАСТЬ
        -- AC-JRAAVTO-39-6 распред. затрат по подвидам и месту выполнения
        , expenses_by_type_and_workshop as (select  egl.our_workshop  -- РЕМЗОНА - true СТО - false
             , egl.repair_subtype_id -- id подвида ремонта
             , egl.repair_subtype_name -- Название подвида ремонта
             , repair_expenses_all -- Затраты в рублях всего (КРАСИТЬ ПОЛУПРОЗРАЧНЫМ)
             , repair_expenses_all_array
             , max_expense
             , coalesce(repair_expenses_filter, 0) as repair_expenses_filter -- Затраты в рублях по фильтру (КРАСИТЬ НЕПРОЗРАЧНЫМ, ОТОБРАЖАТЬ В ПОДСКАЗКЕ)
             , repair_count_all -- Кол-во ремонтов всего (отображать в подсказке. второе число через /)
             , coalesce(repair_count_filter, 0) as repair_count_filter -- Кол-во ремонтов по фильтру (отображать в подсказке. первое число через /)
        from  expenses_group_all egl
                  left join expenses_group_filter egf
                            on egl.our_workshop = egf.our_workshop
                                and egl.repair_subtype_id = egf.repair_subtype_id
                                and egl.repair_subtype_name = egf.repair_subtype_name
        )
            select coalesce(exp1.repair_subtype_id, exp2.repair_subtype_id) as repair_subtype_id
                , coalesce(exp1.repair_subtype_name, exp2.repair_subtype_name) as repair_subtype_name
                , coalesce(exp1.repair_count_all, 0) as our_repair_count_all
                , coalesce(exp2.repair_count_all, 0) as sto_repair_count_all
                , coalesce(exp1.repair_count_filter, 0) as our_repair_count_filter
                , coalesce(exp2.repair_count_filter, 0) as sto_repair_count_filter
                , coalesce(exp1.repair_expenses_all, 0) as our_repair_expenses_all
                , coalesce(exp2.repair_expenses_all, 0) as sto_repair_expenses_all
                , coalesce(exp1.repair_expenses_filter, 0) as our_repair_expenses_filter
                , coalesce(exp2.repair_expenses_filter, 0) as sto_repair_expenses_filter
                , exp1.repair_expenses_all_array as our_repair_expenses_all_array
                , exp2.repair_expenses_all_array as sto_repair_expenses_all_array
                , coalesce(exp1.max_expense, 0) as our_max_expense
                , coalesce(exp2.max_expense, 0) as sto_max_expense
        from (select * from expenses_by_type_and_workshop where our_workshop = true) exp1
        full join (select * from expenses_by_type_and_workshop where our_workshop = false) exp2 on exp1.repair_subtype_id = exp2.repair_subtype_id
        """.trimIndent()

    private val topQuery = commonQuery + """
        -- ПРАВАЯ ЧАСТЬ
        -- AC-JRAAVTO-39-9 ТОП ЗАКАЗОВ, руб          
        select  order_id -- Заказ ТОРО
             , license_num -- Гос номер ТС
             , repair_expenses -- Затраты, руб
        from repair_ur, max_min_expenses
        where repair_expenses >= coalesce(:filter_min, min_expenses)
        and   repair_expenses <= coalesce(:filter_max, max_expenses)
        order by repair_expenses DESC
        limit 10
    """.trimIndent()

    fun getMinMaxExpenses(req: RepairModalOrdersReq): RepairModalMinMaxExpensesResp {
        val nativeQuery = getNativeQuery(minMaxExpensesQuery, req)

        val tupleResult = nativeQuery
            .singleResult as Tuple

        return RepairModalMinMaxExpensesResp(
            minExpenses = (tupleResult["min_expenses"] as Double? ?: 0.0).toBigDecimal(),
            maxExpenses = (tupleResult["max_expenses"] as Double? ?: 0.0).toBigDecimal()
        )
    }

    fun getDistribution(req: RepairModalOrdersReq): RepairModalExpensesResp {
        val nativeQuery = getNativeQuery(distributionQuery, req)
        val tupleResult = nativeQuery
            .resultList as List<Tuple>

        return RepairModalExpensesResp(
            expenses = if (tupleResult.isEmpty()) emptyList() else tupleResult.map {
                RepairModalExpensesData(
                    subtypeId = (it["repair_subtype_id"] as Int).toBigInteger(),
                    subtypeName = it["repair_subtype_name"] as String,
                    ourRepairCountAll = (it["our_repair_count_all"] as BigInteger),
                    stoRepairCountAll = (it["sto_repair_count_all"] as BigInteger),
                    ourRepairCountFilter = (it["our_repair_count_filter"] as BigInteger),
                    stoRepairCountFilter = (it["sto_repair_count_filter"] as BigInteger),
                    ourRepairExpensesAll = (it["our_repair_expenses_all"] as Double).toBigDecimal(),
                    stoRepairExpensesAll = (it["sto_repair_expenses_all"] as Double).toBigDecimal(),
                    ourRepairExpensesFilter = (it["our_repair_expenses_filter"] as Double).toBigDecimal(),
                    stoRepairExpensesFilter = (it["sto_repair_expenses_filter"] as Double).toBigDecimal(),
                    ourRepairs = RepairModalOrdersBarData(
                        orders = (it["our_repair_expenses_all_array"] as String?)?.split(",")?.map(::BigDecimal),
                        barSum = (it["our_repair_expenses_all"] as Double).toBigDecimal(),
                        maxOrder = (it["our_max_expense"] as Double).toBigDecimal()
                    ),
                    stoRepairs = RepairModalOrdersBarData(
                        orders = (it["sto_repair_expenses_all_array"] as String?)?.split(",")?.map(::BigDecimal),
                        barSum = (it["sto_repair_expenses_all"] as Double).toBigDecimal(),
                        maxOrder = (it["sto_max_expense"] as Double).toBigDecimal()
                    ),
                )
            }
        )
    }

    fun getTopOrders(req: RepairModalOrdersReq): RepairModalTopOrdersResp {
        val nativeQuery = getNativeQuery(topQuery, req)
        val tupleResult = nativeQuery
            .resultList as List<Tuple>

        return RepairModalTopOrdersResp(
            topOrders = if (tupleResult.isEmpty()) emptyList() else tupleResult.map {
                RepairTopOrdersData(
                    orderNumber = it["order_id"] as BigInteger,
                    licenseNumber = it["license_num"] as String,
                    cost = (it["repair_expenses"] as Double).toBigDecimal()
                )
            }
        )
    }

    private fun getNativeQuery(query: String, req: RepairModalOrdersReq): Query {
        val queryWithMvz = query
            .replace(
                ":geo_filter_repair",
                prepareRepairGeoFilter(req.geoFilter)
            )
        if (logQuery) log.debug("Query with MVZ triple: $queryWithMvz")

        return em.createNativeQuery(queryWithMvz, Tuple::class.java)
            .setParameter("start_date", req.from)
            .setParameter("end_date", req.to)
            .setParameter("filter_min", TypedParameterValue(StandardBasicTypes.DOUBLE, null))
            .setParameter("filter_max", TypedParameterValue(StandardBasicTypes.DOUBLE, null))
            .setParameter("models", req.modelFilters)
    }

    private fun prepareRepairGeoFilter(geoFilter: GeoFilter) = getGeoFiltersClause(
        filters = geoFilter,
        mrColumnName = "vehicle_mr_id",
        atpColumnName = "vehicle_atp_id",
        mvzColumnName = "vehicle_mvz_id",
        retailNetworkColumnName = "vehicle_retail_network",
        atpTypeColumnName = "vehicle_atp_type",
        mvzTypeColumnName = "vehicle_mvz_type"
    )
}
