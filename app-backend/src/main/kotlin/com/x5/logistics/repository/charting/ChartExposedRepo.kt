package com.x5.logistics.repository.charting

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.charting.ChartKtgJoinedView
import com.x5.logistics.data.charting.ChartRepairStatsJoinedView
import com.x5.logistics.data.charting.RepairChartJoinedView
import com.x5.logistics.repository.CoalesceList
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.CountFiltered
import com.x5.logistics.repository.DateTruncPeriod
import com.x5.logistics.repository.GreatestFromList
import com.x5.logistics.repository.LeastFromList
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.buildJoinPredicate
import com.x5.logistics.repository.castToDate
import com.x5.logistics.repository.castToDouble
import com.x5.logistics.repository.castToInt
import com.x5.logistics.repository.castToString
import com.x5.logistics.repository.dateTrunc
import com.x5.logistics.repository.div
import com.x5.logistics.repository.groupingSets
import com.x5.logistics.repository.minus
import com.x5.logistics.repository.nullable
import com.x5.logistics.repository.plus
import com.x5.logistics.repository.toInterval
import com.x5.logistics.repository.toNullable
import com.x5.logistics.rest.dto.charting.ChartColumn
import com.x5.logistics.rest.dto.charting.ChartColumnFilter
import com.x5.logistics.rest.dto.charting.ChartColumnRequest
import com.x5.logistics.rest.dto.charting.ChartDataWidgetRequest
import com.x5.logistics.rest.dto.charting.ChartDataWidgetValueRequest
import com.x5.logistics.rest.dto.charting.ValueCombine
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.hashWithPrefix
import org.jetbrains.exposed.sql.Avg
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ColumnSet
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Max
import org.jetbrains.exposed.sql.Min
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.plus
import org.jetbrains.exposed.sql.StringColumnType
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.countDistinct
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.javatime.dateLiteral
import org.jetbrains.exposed.sql.longLiteral
import org.jetbrains.exposed.sql.min
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.sum
import org.springframework.stereotype.Component
import java.time.LocalDate

@Component
class ChartExposedRepo {
    val logger = getLogger()

    @ExposedTransactional
    fun getChartData(
        req: ChartDataWidgetRequest
    ): ChartData {
        //выбранные группировки
        val chartColumns = (req.rows + req.columns).map { it.name }
        //выбранные показатели
        val values = req.values.map { it.name }

        //разделение простых и вычисляемых показателей
        val (calculatedValues, simpleValues) = req.values.partition { value ->
            CalculatedValues.entries.map { v -> v.column }.any { it == value.name }
        }

        //находим показатели (части вычисляемых) по ремонтам
        val repairCalculatedValuesAliases = calculatedValues.flatMap {
            val exp = ChartCategoryValues.REPAIR_VALUES.values[it.name]
            if (exp != null) {
                ((it.shares?.map { sh -> sh.function } ?: emptyList()) + it.functions).distinct()
                    .map { f ->
                        //числитель вычисляемого показателя
                        val function = when (it.name) {
                            ChartColumn.order_rub_ts -> "sum"
                            ChartColumn.order_repair_count_ts -> "count"
                            ChartColumn.order_rub_km -> "sum"
                            //иначе - сама функция
                            else -> f
                        }
                        //генерируем aliasExpression
                        getValueExpression(FlatValue(it.name, function), exp)
                    }
            } else emptyList()
        }

        //простые (не вычисляемые) показатели по ремонтам
        val repairValueAliases = simpleValues.flatMap {
            val exp = ChartCategoryValues.REPAIR_VALUES.values[it.name]
            if (exp != null) {
                ((it.shares?.map { sh -> sh.function } ?: emptyList()) + it.functions).distinct()
                    .map { f ->
                        //генерируем aliasExpression
                        getValueExpression(FlatValue(it.name, f), exp)
                    }
            } else emptyList()
        }

        //находим показатели (части вычисляемых) по пробегу
        val mileageCalculatedValueAliases = calculatedValues.flatMap {
            val exp = ChartCategoryValues.MILEAGE_VALUES.values[it.name]
            if (exp != null) {
                ((it.shares?.map { sh -> sh.function } ?: emptyList()) + it.functions).distinct()
                    .map { f ->
                        //знаменатель вычисляемого показателя
                        val function = when (it.name) {
                            ChartColumn.order_rub_km -> "sum"
                            //иначе - сама функция
                            else -> f
                        }
                        getValueExpression(FlatValue(it.name, function), exp)
                    }
            } else emptyList()
        }

        //простые (не вычисляемые) показатели по пробегу
        val mileageValueAliases = simpleValues.flatMap {
            val exp = ChartCategoryValues.MILEAGE_VALUES.values[it.name]
            if (exp != null) {
                ((it.shares?.map { sh -> sh.function } ?: emptyList()) + it.functions).distinct()
                    .map { f ->
                        //генерируем aliasExpression
                        getValueExpression(FlatValue(it.name, f), exp)
                    }
            } else emptyList()
        }

        //находим показатели по КТГ
        val ktgValueAliases = req.values.mapNotNull { value ->
            val exp = ChartCategoryValues.KTG_VALUES.values[value.name]

            if (exp != null) {
                value.name to getKtgValueExpression(value.name, chartColumns, req)
                    ?.alias(value.name.name + "_" + value.functions.first())
            } else null
        }

        //подзапросы, с выборкой группировок и показателей
        val repairQuery = getRepairChartQuery(req, repairCalculatedValuesAliases + repairValueAliases).takeIf {
            ChartCategoryValues.REPAIR_VALUES.values.any { values.contains(it.key) }
        }
        val mileageQuery = getMileageChartQuery(req, mileageCalculatedValueAliases + mileageValueAliases).takeIf {
            ChartCategoryValues.MILEAGE_VALUES.values.any { values.contains(it.key) }
        }
        val ktgQuery = getKtgChartQuery(req, ktgValueAliases.mapNotNull { it.second }).takeIf {
            ChartCategoryValues.KTG_VALUES.values.any { values.contains(it.key) }
        }

        // Получаем подзапросы для комбинированных показателей
        val combinedQueries = req.valuesCombine.map { valueCombine ->
            val (numeratorIndex, denominatorIndex) = parseExpression(valueCombine.settings.expressions)
            val (numeratorQuery, denominatorQuery) = getCombinedValueQueries(req, valueCombine)

            CombinedQueryInfo(
                name = valueCombine.name.hashWithPrefix(),
                numeratorQuery = numeratorQuery,
                denominatorQuery = denominatorQuery,
                numeratorParameter = valueCombine.settings.parameters[numeratorIndex],
                denominatorParameter = valueCombine.settings.parameters[denominatorIndex]
            )
        }

        val baseQueries = listOfNotNull(
            repairQuery,
            mileageQuery,
            ktgQuery
        )

        // Объединяем все запросы
        val allQueries = baseQueries.map { query ->
            QueryAliasInfo(
                queryAlias = query,
                category = ChartCategory.entries.find { it.name.hashWithPrefix() == query.alias }!!
            )
        } + combinedQueries.flatMap { queryInfo ->
            listOf(
                QueryAliasInfo(
                    queryAlias = queryInfo.numeratorQuery.queryAlias,
                    category = determineTargetCategory(queryInfo.numeratorParameter),
                    groupings = queryInfo.numeratorQuery.aliases
                ),
                QueryAliasInfo(
                    queryAlias = queryInfo.denominatorQuery.queryAlias,
                    category = determineTargetCategory(queryInfo.denominatorParameter),
                    groupings = queryInfo.denominatorQuery.aliases
                ),
            )
        }

        //группировки по ремонтам
        val repairViewColumns = if (repairQuery != null) {
            chartColumns.mapNotNull { chartColumn ->
                RepairChartColumnExpressions.entries.firstOrNull { chartColumn == it.chartColumn }?.let {
                    chartColumn to repairQuery[it.expression]
                }

            }
        } else listOf()

        //группировки по пробегу
        val mileageViewColumns = if (mileageQuery != null) {
            chartColumns.mapNotNull { chartColumn ->
                MileageChartColumnExpressions.entries.firstOrNull { chartColumn == it.chartColumn }?.let {
                    chartColumn to mileageQuery[it.expression]
                }
            }
        } else listOf()

        //группировки по КТГ
        val ktgViewColumns = if (ktgQuery != null) {
            chartColumns.mapNotNull { chartColumn ->
                KtgChartColumnExpressions.entries.firstOrNull { chartColumn == it.chartColumn }?.let {
                    chartColumn to ktgQuery[it.expression]
                }
            }
        } else listOf()

        //выборка группировок из финального join запроса
        val columnsToSelect = chartColumns.mapNotNull { chartColumn ->
            // Для каждой группировки из запроса
            val listExp = when {
                // Если есть комбинированные показатели, берем группировки из их запросов
                combinedQueries.isNotEmpty() -> {
                    combinedQueries.flatMap { info ->
                        val numeratorCategory = determineTargetCategory(info.numeratorParameter)
                        val denominatorCategory = determineTargetCategory(info.denominatorParameter)
                        val numeratorExp = when {
                            info.numeratorQuery.aliases?.isNotEmpty() == true -> info.numeratorQuery.aliases.find {
                                it.first == chartColumn
                            }?.second?.aliasOnlyExpression()

                            else -> getWrappedColumn(numeratorCategory, info.numeratorQuery.queryAlias, chartColumn)
                        }
                        val denominatorExp = when {
                            info.denominatorQuery.aliases?.isNotEmpty() == true -> info.denominatorQuery.aliases.find {
                                it.first == chartColumn
                            }?.second?.aliasOnlyExpression()

                            else -> getWrappedColumn(denominatorCategory, info.denominatorQuery.queryAlias, chartColumn)
                        }
                        listOfNotNull(
                            numeratorExp, denominatorExp,
//                            getWrappedColumn(numeratorCategory, it.numeratorQuery, chartColumn),
//                            getWrappedColumn(denominatorCategory, it.denominatorQuery, chartColumn)
                        )
                    }
                }
                // Иначе используем существующую логику
                else -> {
                    listOfNotNull(
                        repairViewColumns.find { it.first == chartColumn }?.second,
                        mileageViewColumns.find { it.first == chartColumn }?.second,
                        ktgViewColumns.find { it.first == chartColumn }?.second
                    )
                }
            }

            if (listExp.isNotEmpty()) {
                chartColumn to CoalesceList(listExp + stringLiteral("N/A"))
            } else {
                null
            }
        }

        val combinedValueExpressions = combinedQueries.map { queryInfo ->
            val numeratorExpression = when {
                queryInfo.numeratorQuery.valueAlias != null -> queryInfo.numeratorQuery.valueAlias
                else -> getParameterValueExpression(queryInfo.numeratorParameter, req)
            }
            val denominatorExpression = when {
                queryInfo.denominatorQuery.valueAlias != null -> queryInfo.denominatorQuery.valueAlias
                else -> getParameterValueExpression(queryInfo.denominatorParameter, req)
            }

            Case()
                .When(
                    cond = queryInfo.denominatorQuery.queryAlias[denominatorExpression].castToDouble() neq doubleLiteral(
                        0.0
                    ),
                    result = queryInfo.numeratorQuery.queryAlias[numeratorExpression].castToDouble() /
                            queryInfo.denominatorQuery.queryAlias[denominatorExpression].castToDouble()
                )
                .Else(Op.nullOp())
                .alias(queryInfo.name)
        }

        //выборка всех остальных (простых, не вычисляемых) показателей из финального join запроса
        val simpleValuesToSelect =
            (if (repairQuery != null) {
                repairValueAliases.map { repairQuery[it].alias(it.alias) }//выборка по ремонтам
            } else emptyList()) +
                    (if (mileageQuery != null) {
                        mileageValueAliases.map { mileageQuery[it].alias(it.alias) }//выборка по пробегу
                    } else emptyList()) +
                    (if (ktgQuery != null) {
                        ktgValueAliases.filterNot {
                            it.first in listOf(ChartColumn.order_rub_ts, ChartColumn.order_repair_count_ts)
                        }
                            .mapNotNull {
                                it.second?.let { exp ->
                                    ktgQuery[exp].alias(exp.alias)
                                }
                            }//выборка по КТГ
                    } else emptyList())

        //выборка вычисляемых показателей из финального join запроса
        val calculatedValuesToSelect = flattenValues(req.values)
            .filter { v ->
                CalculatedValues.entries.map { it.column }.any { it == v.column }
            }
            .mapNotNull { value ->
                when (value.column) {
                    ChartColumn.order_rub_ts -> {
                        //ищем по alias
                        val orderRubTsRepair =
                            repairCalculatedValuesAliases.find { it.alias == (value.column.name + "_" + value.function) }
                        val tsCount =
                            ktgValueAliases.find { it.second?.alias == (value.column.name + "_" + value.function) }
                                ?.second
                        if (orderRubTsRepair != null && tsCount != null && repairQuery != null && ktgQuery != null) {
                            Case()
                                .When(
                                    cond = ktgQuery[tsCount].castToDouble() neq doubleLiteral(0.0),
                                    result = repairQuery[orderRubTsRepair].castToDouble().toNullable() /
                                            ktgQuery[tsCount].castToDouble()
                                )
                                .Else(Op.nullOp())
                        } else {
                            null
                        }
                    }

                    ChartColumn.order_rub_km -> {
                        val orderRubKmRepair =
                            repairCalculatedValuesAliases.find { it.alias == (value.column.name + "_" + value.function) }
                        val mileage =
                            mileageCalculatedValueAliases.find { it.alias == (value.column.name + "_" + value.function) }
                        if (orderRubKmRepair != null && mileage != null && repairQuery != null && mileageQuery != null) {
                            Case()
                                .When(
                                    cond = mileageQuery[mileage].castToDouble() neq doubleLiteral(0.0),
                                    result = repairQuery[orderRubKmRepair].castToDouble() /
                                            mileageQuery[mileage].castToDouble()
                                )
                                .Else(Op.nullOp())
                        } else {
                            null
                        }
                    }

                    ChartColumn.order_repair_count_ts -> {
                        val orderCountTsRepair =
                            repairCalculatedValuesAliases.find { it.alias == (value.column.name + "_" + value.function) }
                        val tsCount =
                            ktgValueAliases.find { it.second?.alias == (value.column.name + "_" + value.function) }
                                ?.second
                        if (orderCountTsRepair != null && tsCount != null && repairQuery != null && ktgQuery != null) {
                            Case()
                                .When(
                                    cond = ktgQuery[tsCount].castToDouble() neq doubleLiteral(0.0),
                                    result = repairQuery[orderCountTsRepair].castToDouble().toNullable() /
                                            ktgQuery[tsCount].castToDouble()
                                )
                                .Else(Op.nullOp())
                        } else {
                            null
                        }
                    }

                    else -> null

                }?.alias(value.column.name + "_" + value.function)
            }

        val query = joinQueries(
            allQueries,
            req.columns.map { it.name } + req.rows.map { it.name },
        ).select(
            columnsToSelect.map { it.second } +
                    calculatedValuesToSelect +
                    simpleValuesToSelect +
                    combinedQueries.flatMap { queryInfo ->
                        val numeratorExpression = when {
                            queryInfo.numeratorQuery.valueAlias != null -> queryInfo.numeratorQuery.valueAlias
                            else -> getParameterValueExpression(queryInfo.numeratorParameter, req)
                        }
                        val denominatorExpression = when {
                            queryInfo.denominatorQuery.valueAlias != null -> queryInfo.denominatorQuery.valueAlias
                            else -> getParameterValueExpression(queryInfo.denominatorParameter, req)
                        }
                        listOf(
                            queryInfo.numeratorQuery.queryAlias[numeratorExpression]
                                .alias("${queryInfo.name}_numerator"),
                            queryInfo.denominatorQuery.queryAlias[denominatorExpression]
                                .alias("${queryInfo.name}_denominator")
                        )
                    } +
                    combinedValueExpressions
        ).withDistinct(true)
            .also { logger.info(it.prepareSQL(QueryBuilder(false))) }

        val groupings = mutableListOf<Map<ChartColumn, String>>()
        val valuesList = mutableMapOf<String, Double>()
        // Обработка результатов запроса
        query.map { row ->
            val rowGroupings = columnsToSelect.associate { column ->
                column.first to row[column.second].toString()
            }
            if (!rowGroupings.any { (_, value) -> (value == "Итог" || value == "null") }) {
                groupings.add(rowGroupings)
            }
            val rowValues = (calculatedValuesToSelect + simpleValuesToSelect + combinedValueExpressions)
                .mapNotNull { valueExpression ->
                    val key = columnsToSelect.filterNot { row[it.second] == "Итог" }.joinToString(separator = "") {
                        row[it.second].toString()
                    } + valueExpression.alias
                    val value = row[valueExpression].nullable
                    value?.let { key to it }
                }
            valuesList.putAll(rowValues)
        }

        return ChartData(groupings, valuesList)
    }

    private fun getKtgValueExpression(
        value: ChartColumn,
        chartColumns: List<ChartColumn>,
        req: ChartDataWidgetRequest
    ) = when (value) {
        ChartColumn.ktg_share -> {
            Case()
                .When(
                    cond = ChartKtgJoinedView.equnr.count() neq longLiteral(0),
                    result = ChartKtgJoinedView.ktgCounter.sum().castToDouble() /
                            ChartKtgJoinedView.equnr.count().castToDouble()
                )
                .Else(Op.nullOp())
        }

        ChartColumn.rg_share -> {
            Case()
                .When(
                    cond = ChartKtgJoinedView.equnr.count() neq longLiteral(0),
                    result = ChartKtgJoinedView.rgCounter.sum().castToDouble() /
                            ChartKtgJoinedView.equnr.count().castToDouble()
                )
                .Else(Op.nullOp())
        }

        ChartColumn.ktg_repair_share -> {
            Case()
                .When(
                    cond = ChartKtgJoinedView.equnr.count() neq longLiteral(0),
                    result = CountFiltered(ChartKtgJoinedView.ktgReasonId eq 2).castToDouble() /
                            ChartKtgJoinedView.equnr.count().castToDouble()
                )
                .Else(Op.nullOp())
        }

        ChartColumn.ktg_dtp_share -> {
            Case()
                .When(
                    cond = ChartKtgJoinedView.equnr.count() neq longLiteral(0),
                    result = CountFiltered(ChartKtgJoinedView.ktgReasonId eq 3).castToDouble() /
                            ChartKtgJoinedView.equnr.count().castToDouble()
                )
                .Else(Op.nullOp())
        }

        ChartColumn.ts_total_amount,
        ChartColumn.order_rub_ts,
        ChartColumn.order_repair_count_ts -> {
            if (chartColumns.contains(ChartColumn.day)) {
                ChartKtgJoinedView.equnr.count().castToDouble()
            } else {
                //группировки с отрезками времени
                val dateEndToStartExpressions =
                    chartColumns.map { selectedGrouping ->
                        when (selectedGrouping) {
                            //берем концы всех периодов
                            ChartColumn.year -> getYearEndDate()
                            ChartColumn.quarter -> getQuarterEndDate()
                            ChartColumn.month -> getMonthEndDate()
                            ChartColumn.report_week -> getReportWeekEndDate()
                            ChartColumn.week -> getWeekEndDate()
                            else -> null
                        } to
                                when (selectedGrouping) {
                                    //берем начало всех периодов
                                    ChartColumn.year -> dateTruncFromVehicleDateMin(DateTruncPeriod.YEAR)
                                    ChartColumn.quarter -> dateTruncFromVehicleDateMin(DateTruncPeriod.QUARTER)
                                    ChartColumn.month -> dateTruncFromVehicleDateMin(DateTruncPeriod.MONTH)
                                    ChartColumn.report_week -> getReportWeekStartDate()
                                    ChartColumn.week -> dateTruncFromVehicleDateMin(DateTruncPeriod.WEEK)
                                    else -> null
                                }
                    }

                val leastDate = LeastFromList(
                    list = listOf(dateLiteral(req.to).castToDate()) +
                            dateEndToStartExpressions.mapNotNull { it.first }
                )
                val greatestDate = GreatestFromList(
                    list = listOf(dateLiteral(req.from).castToDate()) +
                            dateEndToStartExpressions.mapNotNull { it.second }
                )

                ChartKtgJoinedView.equnr.count().castToDouble() /
                        ((leastDate - greatestDate).castToInt() + intLiteral(1)).castToDouble()

            }
        }

        else -> null

    }

    private data class CombinedQueryInfo(
        val name: String,
        val numeratorQuery: QueryWithAliases,
        val denominatorQuery: QueryWithAliases,
        val numeratorParameter: ValueCombine.Parameter,
        val denominatorParameter: ValueCombine.Parameter
    )

    fun getWrappedColumn(category: ChartCategory, query: QueryAlias, chartColumn: ChartColumn): Column<out Any?>? =
        getColumn(category, chartColumn)?.let { query[it] }

    fun getColumn(category: ChartCategory, chartColumn: ChartColumn): Column<out Any?>? {
        return when (category) {
            ChartCategory.REPAIR -> RepairChartColumnExpressions.entries.firstOrNull { it.chartColumn == chartColumn }?.expression
            ChartCategory.MILEAGE -> MileageChartColumnExpressions.entries.firstOrNull { it.chartColumn == chartColumn }?.expression
//            ChartCategory.TS_COUNT -> TsCountChartColumnExpressions.entries.firstOrNull { it.chartColumn == chartColumn }?.expression
            ChartCategory.KTG -> KtgChartColumnExpressions.entries.firstOrNull { it.chartColumn == chartColumn }?.expression
        }
    }

    private fun parseExpression(expression: String): Pair<Int, Int> {
        val regex = "\\[(\\d+)]\\s*/\\s*\\[(\\d+)]".toRegex()
        val matchResult = regex.find(expression)
            ?: throw WrongRequestDataException("Неверный формат expression")
        val numerator = matchResult.groupValues[1].toInt()
        val denominator = matchResult.groupValues[2].toInt()

        return Pair(numerator, denominator)
    }

    private fun determineTargetCategory(parameter: ValueCombine.Parameter): ChartCategory {
        return when (parameter.name) {
            in ChartCategoryValues.REPAIR_VALUES.values.keys -> ChartCategory.REPAIR
            in ChartCategoryValues.MILEAGE_VALUES.values.keys -> ChartCategory.MILEAGE
//            in ChartCategoryValues.TS_COUNT_VALUES.values.keys -> ChartCategory.TS_COUNT
            in ChartCategoryValues.KTG_VALUES.values.keys -> ChartCategory.KTG
            else -> throw WrongRequestDataException("Неподдерживаемый показатель ${parameter.name}")
        }
    }

    private fun getYearEndDate() =
        //sql: (date_trunc('year', min(vehicle_date)) + '1 YEAR'::INTERVAL - '1 DAY'::INTERVAL)::date
        (dateTruncFromVehicleDateMin(DateTruncPeriod.YEAR) + "1 YEAR".toInterval() - "1 DAY".toInterval()).castToDate()

    private fun getQuarterEndDate() =
        //sql: (date_trunc('quarter', min(vehicle_date)) + '3 MONTH'::INTERVAL - '1 DAY'::INTERVAL)::date
        (dateTruncFromVehicleDateMin(DateTruncPeriod.QUARTER) + "3 MONTH".toInterval() - "1 DAY".toInterval()).castToDate()

    private fun getMonthEndDate() =
        //sql: (date_trunc('month', min(vehicle_date)) + '1 MONTH'::INTERVAL - '1 DAY'::INTERVAL)::date
        (dateTruncFromVehicleDateMin(DateTruncPeriod.MONTH) + "1 MONTH".toInterval() - "1 DAY".toInterval()).castToDate()

    private fun getWeekEndDate() =
        //sql: date_trunc('week', min(vehicle_date))::date + 6
        (dateTruncFromVehicleDateMin(DateTruncPeriod.WEEK) + "6 DAY".toInterval()).castToDate()

    private fun getReportWeekEndDate() =
        //sql: date_trunc('week', min(vehicle_date) + 3)::date + 3
        (dateTrunc(
            period = DateTruncPeriod.WEEK,
            date = ChartKtgJoinedView.vehicleDate.min().castToDate() + "3 DAY".toInterval()
        ) + "3 DAY".toInterval()).castToDate()

    private fun getReportWeekStartDate() =
        //sql: date_trunc('week', min(vehicle_date) + 3)::date - 3
        (dateTrunc(
            period = DateTruncPeriod.WEEK,
            date = ChartKtgJoinedView.vehicleDate.min().castToDate() + "3 DAY".toInterval()
        ) - "3 DAY".toInterval()).castToDate()

    private fun dateTruncFromVehicleDateMin(
        period: DateTruncPeriod
    ): Expression<LocalDate> = dateTrunc(period, ChartKtgJoinedView.vehicleDate.min().castToDate())

    private fun flattenValues(values: List<ChartDataWidgetValueRequest>): List<FlatValue> {
        val res = mutableListOf<FlatValue>()
        values.forEach { value ->
            value.functions.forEach { function ->
                res.add(
                    FlatValue(
                        column = value.name,
                        function = function,
                    )
                )
            }
        }
        return res
    }

    private fun getRepairChartQuery(
        req: ChartDataWidgetRequest,
        repairValueAliases: List<ExpressionAlias<*>>,
        additionalFilters: List<ChartColumnFilter> = emptyList(),
        queryAlias: String = ChartCategory.REPAIR.name
    ): QueryAlias {
        val columns = RepairChartColumnExpressions.entries.filter { e ->
            (req.columns + req.rows).map { it.name }.contains(e.chartColumn)
        }.map { getCoalesceTotalAlias(it.expression) }

        val groupingSets = generateGroupingSets(req)

        return with(RepairChartJoinedView) {
            select(columns + repairValueAliases)
                .where { (dateLiteral(req.from) lessEq startDate) and (startDate lessEq dateLiteral(req.to)) }
        }.apply {
            RepairChartJoinedView.getGeoFilter(req.geoFilter).forEach { andWhere { it } }
        }.apply {
            // Применяем базовые фильтры
            req.filters.forEach {
                val exposedExpression =
                    RepairChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                if (exposedExpression != null) {
                    andWhere {
                        buildFilterPredicate(
                            condition = it.condition,
                            values = it.value,
                            type = ColumnType.STRING,
                            strictFilter = true,
                            exposedExpression = exposedExpression.castToString()
                        )
                    }
                }
            }
            // Применяем дополнительные фильтры
            additionalFilters.forEach {
                val exposedExpression =
                    RepairChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                if (exposedExpression != null) {
                    andWhere {
                        buildFilterPredicate(
                            condition = it.condition,
                            values = it.value,
                            type = ColumnType.STRING,
                            strictFilter = true,
                            exposedExpression = exposedExpression.castToString()
                        )
                    }
                }
            }
        }.groupingSets(
            exprList = groupingSets.map { set ->
                set.mapNotNull {
                    RepairChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                }
            }.toSet(),
            total = (req.total.rows && req.total.columns) ||
                    req.toChartDataRequest().values.filter { it.isShare }.map { it.divider }.any { it == null }
        ).alias(queryAlias.hashWithPrefix())
    }

    private fun getMileageChartQuery(
        req: ChartDataWidgetRequest,
        mileageValueAliases: List<ExpressionAlias<*>>,
        additionalFilters: List<ChartColumnFilter> = emptyList(),
        queryAlias: String = ChartCategory.MILEAGE.name
    ): QueryAlias {
        val columns = MileageChartColumnExpressions.entries.filter { e ->
            (req.columns + req.rows).map { it.name }.contains(e.chartColumn)
        }.map { getCoalesceTotalAlias(it.expression) }

        val groupingSets = generateGroupingSets(req)

        return with(ChartRepairStatsJoinedView) {
            select(columns + mileageValueAliases)
                .where { (dateLiteral(req.from) lessEq vehicleDate) and (vehicleDate lessEq dateLiteral(req.to)) }
        }.apply {
            ChartRepairStatsJoinedView.getGeoFilter(req.geoFilter).forEach { andWhere { it } }
        }.apply {
            (req.filters + additionalFilters).forEach {
                val exposedExpression =
                    MileageChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                if (exposedExpression != null) {
                    andWhere {
                        buildFilterPredicate(
                            condition = it.condition,
                            values = it.value,
                            type = ColumnType.STRING,
                            strictFilter = true,
                            exposedExpression = exposedExpression.castToString()
                        )
                    }
                }
            }
        }.groupingSets(
            exprList = groupingSets.map { set ->
                set.mapNotNull {
                    MileageChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                }
            }.toSet(),
            total = (req.total.rows && req.total.columns) ||
                    req.toChartDataRequest().values.filter { it.isShare }.map { it.divider }.any { it == null }
        ).alias(queryAlias.hashWithPrefix())
    }

    private fun getKtgChartQuery(
        req: ChartDataWidgetRequest,
        ktgValueAliases: List<ExpressionAlias<*>>,
        additionalFilters: List<ChartColumnFilter> = emptyList(),
        queryAlias: String = ChartCategory.KTG.name
    ): QueryAlias {
        val columns = KtgChartColumnExpressions.entries.filter { e ->
            (req.columns + req.rows).map { it.name }.contains(e.chartColumn)
        }.map { getCoalesceTotalAlias(it.expression) }

        val groupingSets = generateGroupingSets(req)

        return with(ChartKtgJoinedView) {
            select(columns + ktgValueAliases)
                .where { (dateLiteral(req.from) lessEq vehicleDate) and (vehicleDate lessEq dateLiteral(req.to)) }
        }.apply {
            ChartKtgJoinedView.getGeoFilter(req.geoFilter).forEach { andWhere { it } }
        }.apply {
            (req.filters + additionalFilters).forEach {
                val exposedExpression =
                    KtgChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                if (exposedExpression != null) {
                    andWhere {
                        buildFilterPredicate(
                            condition = it.condition,
                            values = it.value,
                            type = ColumnType.STRING,
                            strictFilter = true,
                            exposedExpression = exposedExpression.castToString()
                        )
                    }
                }
            }
        }.groupingSets(
            exprList = groupingSets.map { set ->
                set.mapNotNull {
                    KtgChartColumnExpressions.entries.find { exp -> exp.chartColumn == it.name }?.expression
                }
            }.toSet(),
            total = (req.total.rows && req.total.columns) ||
                    req.toChartDataRequest().values.filter { it.isShare }.map { it.divider }.any { it == null }
        ).alias(queryAlias.hashWithPrefix())
    }


    private fun getCoalesceTotalAlias(
        expression: Column<*>
    ) =
        if (expression.columnType is StringColumnType) {
            Coalesce(expression, stringLiteral("Итог")).alias(expression.name)
        } else {
            expression
        }

    @Suppress("UNCHECKED_CAST")
    private fun getValueExpression(
        value: FlatValue,
        column: Expression<*>
    ): ExpressionAlias<Double?> {
        val function = when (value.column) {
            ChartColumn.ts_amount -> "count_distinct"
            ChartColumn.order_repair_count_ts -> "count_distinct"
            else -> value.function
        }

        val expression = when (function) {
            "count" -> (column as Column<*>).count()
            "count_distinct" -> (column as Column<*>).countDistinct()
            "sum" -> Sum(column as Column<Double>, DoubleColumnType())
            "min" -> Min(column, DoubleColumnType())
            "max" -> Max(column, DoubleColumnType())
            "avg" -> Avg(column.castToDouble(), 2)
            else -> throw WrongRequestDataException("unsupported function")
        }.castToDouble()

        return expression.alias(value.column.name + "_" + value.function)
    }

    private fun getValueExpression(parameter: ValueCombine.Parameter): List<ExpressionAlias<*>> {
        val column = when (determineTargetCategory(parameter)) {
            ChartCategory.REPAIR -> ChartCategoryValues.REPAIR_VALUES.values[parameter.name]
            ChartCategory.MILEAGE -> ChartCategoryValues.MILEAGE_VALUES.values[parameter.name]
//            ChartCategory.TS_COUNT -> ChartCategoryValues.TS_COUNT_VALUES.values[parameter.name]
            ChartCategory.KTG -> ChartCategoryValues.KTG_VALUES.values[parameter.name]
        } ?: throw WrongRequestDataException("Неподдерживаемый показатель ${parameter.name}")

        return listOf(
            getValueExpression(
                FlatValue(parameter.name, parameter.functions),
                column
            )
        )
    }

    private data class QueryWithAliases(
        val queryAlias: QueryAlias,
        val aliases: List<Pair<ChartColumn, ExpressionAlias<*>>>? = null,
        val valueAlias: ExpressionAlias<Double?>? = null
    )

    private fun getCombinedValueQueries(
        req: ChartDataWidgetRequest,
        valueCombine: ValueCombine
    ): Pair<QueryWithAliases, QueryWithAliases> {
        val (numeratorIndex, denominatorIndex) = parseExpression(valueCombine.settings.expressions)

        if (numeratorIndex >= valueCombine.settings.parameters.size || denominatorIndex >= valueCombine.settings.parameters.size) {
            throw WrongRequestDataException("Индекс в expression превышает размер массива parameters")
        }

        val numerator = valueCombine.settings.parameters[numeratorIndex]
        val denominator = valueCombine.settings.parameters[denominatorIndex]

        val numeratorCategory = determineTargetCategory(numerator)
        val denominatorCategory = determineTargetCategory(denominator)

        val numeratorQuery = when (numeratorCategory) {
            ChartCategory.REPAIR -> when (numerator.name) {
                ChartColumn.order_repair_count_ts,
                ChartColumn.order_rub_ts -> {
                    val columns = req.rows.map { it.name } + req.columns.map { it.name }
                    val repValueAlias = getValueExpression(numerator)
                    val tsValueAlias = getKtgValueExpression(
                        value = numerator.name,
                        chartColumns = columns,
                        req
                    )!!.alias(numerator.name.name + "_" + numerator.functions)
                    val repairQuery = QueryAliasInfo(
                        queryAlias = getRepairChartQuery(
                            req = req,
                            repairValueAliases = repValueAlias,
                            additionalFilters = numerator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_REP_NUM"
                        ),
                        category = ChartCategory.REPAIR
                    )
                    val tsQuery = QueryAliasInfo(
                        queryAlias = getKtgChartQuery(
                            req = req,
                            ktgValueAliases = listOf(tsValueAlias),
                            additionalFilters = numerator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_TS_NUM"
                        ),
                        category = ChartCategory.KTG
                    )
                    val valueExp = Case()
                        .When(
                            cond = tsQuery.queryAlias[tsValueAlias].castToDouble() neq doubleLiteral(0.0),
                            result = repairQuery.queryAlias[repValueAlias.first()].castToDouble() /
                                    tsQuery.queryAlias[tsValueAlias].castToDouble()
                        )
                        .Else(Op.nullOp()).toNullable().alias("${numerator.name}")

                    val columnsToSelect = columns.mapNotNull { column ->
                        RepairChartColumnExpressions.entries.find {
                            it.chartColumn == column
                        }?.expression?.let {
                            column to repairQuery.queryAlias[it].alias(numerator.name.name + column.name)
                        }
                    }
                    val query = joinQueries(
                        queries = listOf(repairQuery, tsQuery),
                        columns = req.columns.map { it.name } + req.rows.map { it.name }
                    ).select(
                        columnsToSelect.map { it.second } + valueExp
                    ).alias("${valueCombine.name.hashWithPrefix()}_NUM")
                    QueryWithAliases(
                        queryAlias = query,
                        aliases = columnsToSelect,
                        valueAlias = valueExp
                    )
                }

                ChartColumn.order_rub_km -> {
                    val columns = req.rows.map { it.name } + req.columns.map { it.name }
                    val repValueAlias = getValueExpression(numerator)
                    val kmValueAlias = getValueExpression(numerator)
                    val repairQuery = QueryAliasInfo(
                        queryAlias = getRepairChartQuery(
                            req = req,
                            repairValueAliases = repValueAlias,
                            additionalFilters = numerator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_REP_NUM"
                        ),
                        category = ChartCategory.REPAIR
                    )
                    val kmQuery = QueryAliasInfo(
                        getMileageChartQuery(
                            req = req,
                            mileageValueAliases = getValueExpression(numerator),
                            additionalFilters = numerator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_KM_NUM"
                        ),
                        category = ChartCategory.MILEAGE
                    )
                    val valueExp = Case()
                        .When(
                            cond = kmQuery.queryAlias[kmValueAlias.first()].castToDouble() neq doubleLiteral(0.0),
                            result = repairQuery.queryAlias[repValueAlias.first()].castToDouble() /
                                    kmQuery.queryAlias[kmValueAlias.first()].castToDouble()
                        )
                        .Else(Op.nullOp()).toNullable().alias("${numerator.name}")

                    val columnsToSelect = columns.mapNotNull { column ->
                        RepairChartColumnExpressions.entries.find {
                            it.chartColumn == column
                        }?.expression?.let {
                            column to repairQuery.queryAlias[it].alias(numerator.name.name + column.name)
                        }
                    }
                    val query = joinQueries(
                        queries = listOf(repairQuery, kmQuery),
                        columns = req.columns.map { it.name } + req.rows.map { it.name }
                    ).select(
                        columnsToSelect.map { it.second } + valueExp
                    ).alias("${valueCombine.name.hashWithPrefix()}_NUM")
                    QueryWithAliases(
                        queryAlias = query,
                        aliases = columnsToSelect,
                        valueAlias = valueExp
                    )
                }

                else -> QueryWithAliases(
                    getRepairChartQuery(
                        req = req,
                        repairValueAliases = getValueExpression(numerator),
                        additionalFilters = numerator.filters,
                        queryAlias = "${valueCombine.name.hashWithPrefix()}_NUM"
                    )
                )
            }

            ChartCategory.MILEAGE -> QueryWithAliases(
                getMileageChartQuery(
                    req = req,
                    mileageValueAliases = getValueExpression(numerator),
                    additionalFilters = numerator.filters,
                    queryAlias = "${valueCombine.name.hashWithPrefix()}_NUM"
                )
            )

//            ChartCategory.TS_COUNT -> getTsCountChartQuery(
//                req = req,
//                tsCountValueAliases = getValueExpression(numerator),
//                additionalFilters = numerator.filters,
//                queryAlias = "${valueCombine.name.hashWithPrefix()}_NUM"
//            )

            ChartCategory.KTG -> QueryWithAliases(
                getKtgChartQuery(
                    req = req,
                    ktgValueAliases = listOf(
                        getKtgValueExpression(
                            value = numerator.name,
                            chartColumns = req.rows.map { it.name } + req.columns.map { it.name },
                            req
                        )!!.alias(numerator.name.name + "_" + numerator.functions)
                    ),
                    additionalFilters = numerator.filters,
                    queryAlias = "${valueCombine.name.hashWithPrefix()}_NUM"
                )
            )
        }

        val denominatorQuery = when (denominatorCategory) {
            ChartCategory.REPAIR -> when (denominator.name) {
                ChartColumn.order_repair_count_ts,
                ChartColumn.order_rub_ts -> {
                    val columns = req.rows.map { it.name } + req.columns.map { it.name }
                    val repValueAlias = getValueExpression(numerator)
                    val tsValueAlias = getKtgValueExpression(
                        value = denominator.name,
                        chartColumns = columns,
                        req
                    )!!.alias(denominator.name.name + "_" + denominator.functions)
                    val repairQuery = QueryAliasInfo(
                        queryAlias = getRepairChartQuery(
                            req = req,
                            repairValueAliases = repValueAlias,
                            additionalFilters = denominator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_REP_DEN"
                        ),
                        category = ChartCategory.REPAIR
                    )
                    val tsQuery = QueryAliasInfo(
                        queryAlias = getKtgChartQuery(
                            req = req,
                            ktgValueAliases = listOf(tsValueAlias),
                            additionalFilters = denominator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_TS_DEN"
                        ),
                        category = ChartCategory.KTG
                    )
                    val valueExp = Case()
                        .When(
                            cond = tsQuery.queryAlias[tsValueAlias].castToDouble() neq doubleLiteral(0.0),
                            result = repairQuery.queryAlias[repValueAlias.first()].castToDouble() /
                                    tsQuery.queryAlias[tsValueAlias].castToDouble()
                        )
                        .Else(Op.nullOp()).toNullable().alias("${denominator.name}")

                    val columnsToSelect = columns.mapNotNull { column ->
                        RepairChartColumnExpressions.entries.find {
                            it.chartColumn == column
                        }?.expression?.let {
                            column to repairQuery.queryAlias[it].alias(denominator.name.name + column.name)
                        }
                    }
                    val query = joinQueries(
                        queries = listOf(repairQuery, tsQuery),
                        columns = req.columns.map { it.name } + req.rows.map { it.name }
                    ).select(
                        columnsToSelect.map { it.second } + valueExp
                    ).alias("${valueCombine.name.hashWithPrefix()}_DEN")
                    QueryWithAliases(
                        queryAlias = query,
                        aliases = columnsToSelect,
                        valueAlias = valueExp
                    )
                }

                ChartColumn.order_rub_km -> {
                    val columns = req.rows.map { it.name } + req.columns.map { it.name }
                    val repValueAlias = getValueExpression(denominator)
                    val kmValueAlias = getValueExpression(denominator)
                    val repairQuery = QueryAliasInfo(
                        queryAlias = getRepairChartQuery(
                            req = req,
                            repairValueAliases = repValueAlias,
                            additionalFilters = denominator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_REP_DEN"
                        ),
                        category = ChartCategory.REPAIR
                    )
                    val kmQuery = QueryAliasInfo(
                        getMileageChartQuery(
                            req = req,
                            mileageValueAliases = getValueExpression(denominator),
                            additionalFilters = denominator.filters,
                            queryAlias = "${valueCombine.name.hashWithPrefix()}_KM_DEN"
                        ),
                        category = ChartCategory.MILEAGE
                    )
                    val valueExp = Case()
                        .When(
                            cond = kmQuery.queryAlias[kmValueAlias.first()].castToDouble() neq doubleLiteral(0.0),
                            result = repairQuery.queryAlias[repValueAlias.first()].castToDouble() /
                                    kmQuery.queryAlias[kmValueAlias.first()].castToDouble()
                        )
                        .Else(Op.nullOp()).toNullable().alias("${denominator.name}")

                    val columnsToSelect = columns.mapNotNull { column ->
                        RepairChartColumnExpressions.entries.find {
                            it.chartColumn == column
                        }?.expression?.let {
                            column to repairQuery.queryAlias[it].alias(denominator.name.name + column.name)
                        }
                    }
                    val query = joinQueries(
                        queries = listOf(repairQuery, kmQuery),
                        columns = req.columns.map { it.name } + req.rows.map { it.name }
                    ).select(
                        columnsToSelect.map { it.second } + valueExp
                    ).alias("${valueCombine.name.hashWithPrefix()}_DEN")
                    QueryWithAliases(
                        queryAlias = query,
                        aliases = columnsToSelect,
                        valueAlias = valueExp
                    )
                }

                else -> QueryWithAliases(
                    getRepairChartQuery(
                        req = req,
                        repairValueAliases = getValueExpression(denominator),
                        additionalFilters = denominator.filters,
                        queryAlias = "${valueCombine.name.hashWithPrefix()}_DEN"
                    )
                )
            }

            ChartCategory.MILEAGE -> QueryWithAliases(
                getMileageChartQuery(
                    req = req,
                    mileageValueAliases = getValueExpression(denominator),
                    additionalFilters = denominator.filters,
                    queryAlias = "${valueCombine.name.hashWithPrefix()}_DEN"
                )
            )

//            ChartCategory.TS_COUNT -> getTsCountChartQuery(
//                req = req,
//                tsCountValueAliases = getValueExpression(denominator),
//                additionalFilters = denominator.filters,
//                queryAlias = "${valueCombine.name.hashWithPrefix()}_DEN"
//            )

            ChartCategory.KTG -> QueryWithAliases(
                getKtgChartQuery(
                    req = req,
                    ktgValueAliases = listOf(
                        getKtgValueExpression(
                            value = denominator.name,
                            chartColumns = req.rows.map { it.name } + req.columns.map { it.name },
                            req
                        )!!.alias(denominator.name.name + "_" + denominator.functions)
                    ),
                    additionalFilters = denominator.filters,
                    queryAlias = "${valueCombine.name.hashWithPrefix()}_DEN"
                )
            )
        }

        return Pair(numeratorQuery, denominatorQuery)
    }

    private fun getParameterValueExpression(
        parameter: ValueCombine.Parameter,
        req: ChartDataWidgetRequest
    ): Expression<*> {
        val flatValue = FlatValue(parameter.name, parameter.functions)
        return when (determineTargetCategory(parameter)) {
            ChartCategory.REPAIR -> ChartCategoryValues.REPAIR_VALUES.values[parameter.name]?.let {
                getValueExpression(flatValue, it)
            }

            ChartCategory.MILEAGE -> ChartCategoryValues.MILEAGE_VALUES.values[parameter.name]?.let {
                getValueExpression(flatValue, it)
            }

//            ChartCategory.TS_COUNT -> ChartCategoryValues.TS_COUNT_VALUES.values[parameter.name]?.let {
//                getValueExpression(flatValue, it)
//            }

            ChartCategory.KTG -> getKtgValueExpression(
                parameter.name,
                req.rows.map { it.name } + req.columns.map { it.name },
                req
            )?.alias(parameter.name.name + "_" + parameter.functions)
        } ?: throw WrongRequestDataException("Неподдерживаемый показатель ${parameter.name}")
    }


    private fun generateGroupingSets(
        req: ChartDataWidgetRequest
    ): MutableList<List<ChartColumnRequest>> {

        val result = mutableListOf(req.columns + req.rows)
        val dividers = req.toChartDataRequest().values.filter { it.isShare }.map { it.divider }

        dividers.forEach { d ->
            when {
                req.rows.map { it.name }.contains(d) ->
                    result.addAll(listOf(req.rows.subList(0, req.rows.indexOfFirst { it.name == d } + 1)))

                req.columns.map { it.name }.contains(d) ->
                    result.addAll(listOf(req.columns.subList(0, req.columns.indexOfFirst { it.name == d } + 1)))
            }
        }

        val columnList = mutableListOf<List<ChartColumnRequest>>()

        columnList.add(req.columns)
        for (column in req.columns.reversed()) {
            if (column.subtotal) {
                columnList.add(req.columns.subList(0, req.columns.indexOf(column) + 1))
            }
        }

        val rowList = mutableListOf<List<ChartColumnRequest>>()

        rowList.add(req.rows)
        for (row in req.rows.reversed()) {
            if (row.subtotal) {
                rowList.add(req.rows.subList(0, req.rows.indexOf(row) + 1))
            }
        }

        if (req.total.columns) result.addAll(rowList)
        if (req.total.rows) result.addAll(columnList)

        result.addAll(intersect(rowList, columnList))
        return result
    }

    private fun intersect(
        rows: List<List<ChartColumnRequest>>,
        columns: List<List<ChartColumnRequest>>
    ) = rows.flatMap { row -> columns.map { column -> row + column } }

    private fun joinQueries(
        queries: List<QueryAliasInfo>,
        columns: List<ChartColumn>
    ): ColumnSet {
        val firstQueryAlias = queries.first()
        var queryToJoin: ColumnSet = firstQueryAlias.queryAlias
        val remainingQueries = queries.drop(1)

        for (query in remainingQueries) {
            val joinColumns = columns.asSequence().mapNotNull { column ->
                val left = when {
                    firstQueryAlias.groupings?.isNotEmpty() == true -> firstQueryAlias.groupings.find { it.first == column }?.second
                    else -> getColumn(firstQueryAlias.category, column)
                }
                val right = when {
                    query.groupings?.isNotEmpty() == true -> query.groupings.find { it.first == column }?.second
                    else -> getColumn(query.category, column)
                }
                if (left != null && right != null) {
                    left to right
                } else {
                    null
                }
            }.toList()

            queryToJoin = queryToJoin.join(
                otherTable = query.queryAlias,
                joinType = JoinType.FULL,
                additionalConstraint = {
                    if (joinColumns.isEmpty()) {
                        // Если нет общих группировок, соединяем по условию "1=1"
                        Op.TRUE
                    } else {
                        buildJoinPredicate(
                            query1 = firstQueryAlias.queryAlias,
                            query2 = query.queryAlias,
                            values = joinColumns
                        )
                    }
                }
            )
        }
        return queryToJoin
    }
}

data class ChartData(
    val groupings: List<Map<ChartColumn, String>>,
    val values: Map<String, Double>
)

private data class FlatValue(
    val column: ChartColumn,
    val function: String
)

private data class QueryAliasInfo(
    val queryAlias: QueryAlias,
    val category: ChartCategory,
    val groupings: List<Pair<ChartColumn, ExpressionAlias<*>>>? = null,
)

enum class ChartCategory {
    REPAIR,

    //    TS_COUNT,
    MILEAGE,
    KTG
}

//вычисляемые показатели
private enum class CalculatedValues(val column: ChartColumn) {
    ORDER_RUB_KM(ChartColumn.order_rub_km),
    ORDER_RUB_TS(ChartColumn.order_rub_ts),
    ORDER_REPAIR_COUNT_TS(ChartColumn.order_repair_count_ts)
}

private enum class ChartCategoryValues(
    val values: Map<ChartColumn, Column<*>>
) {
    REPAIR_VALUES(
        mapOf(
            ChartColumn.order_amount to RepairChartJoinedView.orderId,
            ChartColumn.order_costs to RepairChartJoinedView.repairExpenses,
            ChartColumn.services_amount to RepairChartJoinedView.servicesAmount,
            ChartColumn.order_services_amount to RepairChartJoinedView.servicesAmount,
            ChartColumn.services_expenses to RepairChartJoinedView.servicesExpenses,
            ChartColumn.detail_amount to RepairChartJoinedView.partAmount,
            ChartColumn.order_detail_amount to RepairChartJoinedView.partAmount,
            ChartColumn.order_detail_costs to RepairChartJoinedView.partExpenses,
            ChartColumn.order_rub_km to RepairChartJoinedView.repairExpenses,
            ChartColumn.order_rub_ts to RepairChartJoinedView.repairExpenses,
            ChartColumn.order_repair_count_ts to RepairChartJoinedView.orderId,
            ChartColumn.ts_amount to RepairChartJoinedView.equnr
        )
    ),
    MILEAGE_VALUES(
        mapOf(
            ChartColumn.order_rub_km to ChartRepairStatsJoinedView.mileage
        )
    ),
    KTG_VALUES(
        mapOf(
            ChartColumn.ktg_share to ChartKtgJoinedView.ktgCounter,
            ChartColumn.rg_share to ChartKtgJoinedView.rgCounter,
            ChartColumn.ktg_repair_share to ChartKtgJoinedView.ktgReasonId,
            ChartColumn.ktg_dtp_share to ChartKtgJoinedView.ktgReasonId,
            ChartColumn.ts_total_amount to ChartKtgJoinedView.equnr,
            ChartColumn.order_rub_ts to ChartKtgJoinedView.equnr,
            ChartColumn.order_repair_count_ts to ChartKtgJoinedView.equnr
        )
    )
}

enum class RepairChartColumnExpressions(
    val chartColumn: ChartColumn,
    val expression: Column<*>
) {
    TER_NAME(ChartColumn.terName, RepairChartJoinedView.terName),
    MR(ChartColumn.mr, RepairChartJoinedView.mrName),
    ATP(ChartColumn.atp, RepairChartJoinedView.atpName),
    MVZ(ChartColumn.mvz, RepairChartJoinedView.mvzId),
    MVZ_NAME(ChartColumn.mvz_name, RepairChartJoinedView.mvzName),
    RETAIL_NETWORK(ChartColumn.retail_network, RepairChartJoinedView.retailNetwork),
    ATP_TYPE(ChartColumn.atpType, RepairChartJoinedView.atpType),
    MVZ_TYPE(ChartColumn.mvzType, RepairChartJoinedView.mvzType),
    REPAIR_ATP(ChartColumn.repair_atp, RepairChartJoinedView.repairAtp),
    REPAIR_KIND(ChartColumn.repair_kind, RepairChartJoinedView.repairKind),
    REPAIR_PLACE(ChartColumn.repair_place, RepairChartJoinedView.repairPlace),
    REPAIR_TYPE(ChartColumn.repair_type, RepairChartJoinedView.repairTypeName),
    REPAIR_SUBTYPE(ChartColumn.repair_subtype, RepairChartJoinedView.repairSubtypeName),
    VRT_ID(ChartColumn.vrt_id, RepairChartJoinedView.vrt),
    VRT(ChartColumn.vrt, RepairChartJoinedView.vrtName),
    EVENT_ID(ChartColumn.event_id, RepairChartJoinedView.eventId),
    EVENT_TEXT(ChartColumn.event_text, RepairChartJoinedView.eventText),
    VEHICLE_GROUP(ChartColumn.vehicle_group, RepairChartJoinedView.vehicleGroup),
    VEHICLE_TYPE(ChartColumn.vehicle_type, RepairChartJoinedView.vehicleType),
    VEHICLE_BRAND(ChartColumn.vehicle_brand, RepairChartJoinedView.vehicleBrand),
    VEHICLE_MODEL(ChartColumn.vehicle_model, RepairChartJoinedView.vehicleModel),
    VEHICLE_CREATE_DATE(ChartColumn.vehicle_create_date, RepairChartJoinedView.vehicleCreateYear),
    VEHICLE_TONNAGE(ChartColumn.vehicle_tonnage, RepairChartJoinedView.vehicleTonnage),
    VEHICLE_GBO(ChartColumn.vehicle_gbo, RepairChartJoinedView.vehicleGbo),
    VEHICLE_LICENCE(ChartColumn.vehicle_licence, RepairChartJoinedView.vehicleLicense),
    YEAR(ChartColumn.year, RepairChartJoinedView.year),
    QUARTER(ChartColumn.quarter, RepairChartJoinedView.quarterWithYear),
    MONTH(ChartColumn.month, RepairChartJoinedView.monthWithYear),
    WEEK(ChartColumn.week, RepairChartJoinedView.weekWithYear),
    REPORT_WEEK(ChartColumn.report_week, RepairChartJoinedView.reportWeekWithYear),
    DAY(ChartColumn.day, RepairChartJoinedView.dayMonthYear),
    SERVICE_AMOUNT(ChartColumn.services_amount, RepairChartJoinedView.servicesAmount),
    SERVICE_EXPENSES(ChartColumn.services_expenses, RepairChartJoinedView.servicesExpenses)
}

enum class MileageChartColumnExpressions(
    val chartColumn: ChartColumn,
    val expression: Column<*>
) {
    TER_NAME(ChartColumn.terName, ChartRepairStatsJoinedView.terName),
    MR(ChartColumn.mr, ChartRepairStatsJoinedView.mrName),
    ATP(ChartColumn.atp, ChartRepairStatsJoinedView.atpName),
    MVZ(ChartColumn.mvz, ChartRepairStatsJoinedView.mvzId),
    MVZ_NAME(ChartColumn.mvz_name, ChartRepairStatsJoinedView.mvzName),
    RETAIL_NETWORK(ChartColumn.retail_network, ChartRepairStatsJoinedView.retailNetwork),
    ATP_TYPE(ChartColumn.atpType, ChartRepairStatsJoinedView.atpType),
    MVZ_TYPE(ChartColumn.mvzType, ChartRepairStatsJoinedView.mvzType),
    VEHICLE_GROUP(ChartColumn.vehicle_group, ChartRepairStatsJoinedView.tsGroup),
    VEHICLE_TYPE(ChartColumn.vehicle_type, ChartRepairStatsJoinedView.tsType),
    VEHICLE_BRAND(ChartColumn.vehicle_brand, ChartRepairStatsJoinedView.tsMarka),
    VEHICLE_MODEL(ChartColumn.vehicle_model, ChartRepairStatsJoinedView.tsModel),
    VEHICLE_CREATE_DATE(ChartColumn.vehicle_create_date, ChartRepairStatsJoinedView.tsCreateYear),
    VEHICLE_GBO(ChartColumn.vehicle_gbo, ChartRepairStatsJoinedView.tsGbo),
    VEHICLE_LICENCE(ChartColumn.vehicle_licence, ChartRepairStatsJoinedView.tsLicenseNum),
    YEAR(ChartColumn.year, ChartRepairStatsJoinedView.vehicleDateYear),
    QUARTER(ChartColumn.quarter, ChartRepairStatsJoinedView.vehicleDateQuarterWithYear),
    MONTH(ChartColumn.month, ChartRepairStatsJoinedView.vehicleDateMonthWithYear),
    WEEK(ChartColumn.week, ChartRepairStatsJoinedView.vehicleDateWeekWithYear),
    REPORT_WEEK(ChartColumn.report_week, ChartRepairStatsJoinedView.vehicleDateReportWeekWithYear),
    DAY(ChartColumn.day, ChartRepairStatsJoinedView.vehicleDateDayWithYear)
}

enum class KtgChartColumnExpressions(
    val chartColumn: ChartColumn,
    val expression: Column<*>
) {
    TER_NAME(ChartColumn.terName, ChartKtgJoinedView.terName),
    MR(ChartColumn.mr, ChartKtgJoinedView.mrName),
    ATP(ChartColumn.atp, ChartKtgJoinedView.atpName),
    MVZ(ChartColumn.mvz, ChartKtgJoinedView.mvzId),
    MVZ_NAME(ChartColumn.mvz_name, ChartKtgJoinedView.mvzName),
    RETAIL_NETWORK(ChartColumn.retail_network, ChartKtgJoinedView.retailNetwork),
    ATP_TYPE(ChartColumn.atpType, ChartKtgJoinedView.atpType),
    MVZ_TYPE(ChartColumn.mvzType, ChartKtgJoinedView.mvzType),
    VEHICLE_GROUP(ChartColumn.vehicle_group, ChartKtgJoinedView.vehicleGroup),
    VEHICLE_TYPE(ChartColumn.vehicle_type, ChartKtgJoinedView.vehicleType),
    VEHICLE_BRAND(ChartColumn.vehicle_brand, ChartKtgJoinedView.vehicleBrand),
    VEHICLE_MODEL(ChartColumn.vehicle_model, ChartKtgJoinedView.vehicleModel),
    VEHICLE_CREATE_DATE(ChartColumn.vehicle_create_date, ChartKtgJoinedView.vehicleCreateYear),
    VEHICLE_TONNAGE(ChartColumn.vehicle_tonnage, ChartKtgJoinedView.vehicleTonnage),
    VEHICLE_GBO(ChartColumn.vehicle_gbo, ChartKtgJoinedView.vehicleGbo),
    VEHICLE_LICENCE(ChartColumn.vehicle_licence, ChartKtgJoinedView.vehicleLicense),
    YEAR(ChartColumn.year, ChartKtgJoinedView.dateYear),
    QUARTER(ChartColumn.quarter, ChartKtgJoinedView.dateQuarter),
    MONTH(ChartColumn.month, ChartKtgJoinedView.dateMonth),
    WEEK(ChartColumn.week, ChartKtgJoinedView.dateWeek),
    REPORT_WEEK(ChartColumn.report_week, ChartKtgJoinedView.dateReportWeek),
    DAY(ChartColumn.day, ChartKtgJoinedView.dateDay),
    KTG_STATUS(ChartColumn.ktg_status, ChartKtgJoinedView.ktgStatus),
    KTG_REASON(ChartColumn.ktg_reason, ChartKtgJoinedView.ktgReason),
    RC_SUBMIT_CODE(ChartColumn.rc_submit_code, ChartKtgJoinedView.rcSubmitCode),
    RC_SUBMIT_NAME(ChartColumn.rc_submit_name, ChartKtgJoinedView.rcSubmitName)
}
