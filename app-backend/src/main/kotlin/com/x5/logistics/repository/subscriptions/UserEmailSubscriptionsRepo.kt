package com.x5.logistics.repository.subscriptions

import com.x5.logistics.data.subscriptions.SubscriptionSubject
import com.x5.logistics.data.subscriptions.UserEmailSubscriptionSubjectTable
import com.x5.logistics.data.subscriptions.UserEmailSubscriptionTable
import com.x5.logistics.rest.dto.subscriptions.UserSubscriptionItem
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.stereotype.Component

@Component
class UserEmailSubscriptionsRepo {
    fun getUserSubscriptions(userEmail: String): List<UserSubscriptionItem> = transaction {
        UserEmailSubscriptionTable.selectAll().where {
            UserEmailSubscriptionTable.email eq userEmail
        }
            .map {
                UserSubscriptionItem(
                    email = it[UserEmailSubscriptionTable.email],
                    subject = it[UserEmailSubscriptionTable.subject].value.name,
                    subscriptionDate = it[UserEmailSubscriptionTable.subscriptionDate],
                    unsubscriptionDate = it[UserEmailSubscriptionTable.unsubscriptionDate],
                    createdBy = it[UserEmailSubscriptionTable.createdBy],
                    changedBy = it[UserEmailSubscriptionTable.changedBy]
                )
            }
    }

    fun saveNewUserSubscriptions(userEmail: String, subjects: List<UserSubscriptionItem>) = transaction {
        subjects.forEach { item ->
            val subjectReq = SubscriptionSubject.valueOf(item.subject)
            UserEmailSubscriptionTable.insert {
                it[email] = userEmail
                it[subject] = UserEmailSubscriptionSubjectTable.select(UserEmailSubscriptionSubjectTable.id).where { UserEmailSubscriptionSubjectTable.id eq subjectReq }
                it[subscriptionDate] = item.subscriptionDate
                it[unsubscriptionDate] = item.unsubscriptionDate
                it[createdBy] = item.createdBy
                it[changedBy] = userEmail
            }
        }
    }

    fun updateUserSubscriptions(userEmail: String, subjects: List<UserSubscriptionItem>) = transaction {
        subjects.forEach { item ->
            val subjectReq = SubscriptionSubject.valueOf(item.subject)
            UserEmailSubscriptionTable.update({
                (UserEmailSubscriptionTable.email eq userEmail) and
                        (UserEmailSubscriptionTable.subject eq subjectReq)
            }) {
                it[email] = userEmail
                it[subject] = UserEmailSubscriptionSubjectTable.select(UserEmailSubscriptionSubjectTable.id).where { UserEmailSubscriptionSubjectTable.id eq subjectReq }
                it[subscriptionDate] = item.subscriptionDate
                it[unsubscriptionDate] = item.unsubscriptionDate
                it[createdBy] = item.createdBy
                it[changedBy] = userEmail
            }
        }
    }
}