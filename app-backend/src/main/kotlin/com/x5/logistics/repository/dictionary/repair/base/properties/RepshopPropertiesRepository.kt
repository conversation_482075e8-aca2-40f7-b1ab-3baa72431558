package com.x5.logistics.repository.dictionary.repair.base.properties

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.dictionary.RepshopsPropertiesView
import com.x5.logistics.data.dictionary.RepshopsTable
import com.x5.logistics.data.dictionary.rr.Month
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.rest.dto.dictionary.DictionaryInfoDto
import com.x5.logistics.rest.dto.dictionary.repair.base.properties.PropertyType
import com.x5.logistics.rest.dto.dictionary.repair.base.properties.RepairBasePropertiesColumnFilter
import com.x5.logistics.rest.dto.dictionary.repair.base.properties.RepairBasePropertiesDetails
import com.x5.logistics.rest.dto.dictionary.repair.base.properties.RepairBasePropertiesDto
import com.x5.logistics.rest.dto.dictionary.repair.base.properties.RepairBasePropertiesSortOrder
import com.x5.logistics.rest.dto.dictionary.repair.base.properties.RepairBasePropertiesValue
import com.x5.logistics.service.dictionary.DictionaryUpdateInfo
import jakarta.persistence.EntityManager
import jakarta.persistence.Tuple
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.select
import org.jetbrains.exposed.sql.selectAll
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Repository
import java.sql.Timestamp

@Repository
class RepshopPropertiesRepository(
    private val em: EntityManager
): DictionaryUpdateInfo {
    @ExposedTransactional
    fun getCount(year: Int, filters: List<RepairBasePropertiesColumnFilter>): Int {
        val (_, query) = createQuery(year)
        return query.applyFilters(filters).count().toInt()
    }

    @ExposedTransactional
    fun getRawData(
        year: Int,
        filters: List<RepairBasePropertiesColumnFilter>,
        sort: List<RepairBasePropertiesSortOrder>,
        page: PageRequest,
    ): List<RepairBasePropertiesDto> {
        var (rbp, query) = createQuery(year)
        query = query.applyFilters(filters)
        sort.forEach { item ->
            query = query.orderBy(item.column.exposedExpression, if (item.asc) SortOrder.ASC else SortOrder.DESC)
        }

        val count = query.count()

        query = query.limit(page.pageSize, (page.pageNumber * page.pageSize).toLong())
        val rawData = query.map { it.toRawData(count, rbp) }
        val propertyTypes = PropertyType.values().toList()
        val result = rawData.map { dataForMvz ->
            val atpId = dataForMvz.atpId
            val rbName = dataForMvz.rsName
            val details = propertyTypes.map { propertyType ->
                RepairBasePropertiesDetails(
                    name = propertyType.sqlColumnName,
                    label = propertyType.description,
                    values = Month.values().map { month ->
                        val property = dataForMvz.properties?.firstOrNull {
                            it.month.uppercase() == month.name.uppercase() && it.name == propertyType.sqlColumnName
                        }
                        val value = property?.value
                        RepairBasePropertiesValue(
                            month = month.name,
                            value = value
                        )
                    }
                )
            }
            RepairBasePropertiesDto(
                rsName = rbName,
                rsId = atpId,
//                atpName = atpName,
                details = details,
            )
        }

        return result
    }

    fun getRepshopNameList(): List<String> {
         val query = """
            select distinct name
            from repshops
            where (deleted = false or id in (select rs_id from repshops_properties)) and has_properties = true
        """.trimIndent()
        return em.createNativeQuery(query).resultList as List<String>
    }

    override fun getLastUpdateInfo(): DictionaryInfoDto {
        val tupleRes = (em.createNativeQuery(
            """
                (select updated_at as updatedat, updated_by as author
                 from repshops_properties
                 where updated_by != 'system'
                 order by updated_at desc nulls last 
                 limit 1)
                union
                (select created_at as updatedat, created_by as author
                 from repshops_properties
                 where created_by != 'system'
                 order by created_at desc nulls last 
                 limit 1)
                order by updatedat desc nulls last 
                limit 1
            """.trimIndent(), Tuple::class.java
        ).resultList as List<Tuple>).firstOrNull()
        return DictionaryInfoDto(
            name = "repair_base_properties",
            updatedBy = (tupleRes?.get("author") as String?),
            updatedAt = (tupleRes?.get("updatedat") as Timestamp?)?.toLocalDateTime(),
        )
    }

    private data class Property(
        val month: String,
        val name: String,
        val value: Double?,
    )

    private data class RepshopPropertiesRawData(
        val atpId: Long,
        val rsName: String,
        val properties: List<Property>?,
        val count: Long,
    )

    private fun ResultRow.toRawData(count: Long, rbp: QueryAlias): RepshopPropertiesRawData {
        val propertiesString = this[rbp[RepshopsPropertiesView.properties]]
        val JSON = jacksonObjectMapper()
        val properties =
            if (propertiesString == null) null else JSON.readValue<List<Property>>(propertiesString)

        return RepshopPropertiesRawData(
            atpId = this[RepshopsTable.id],
            rsName = this[RepshopsTable.name]!!,
            count = count,
            properties = properties,
        )
    }

    private fun createQuery(year: Int): Pair<QueryAlias, Query> {
        val rbp = RepshopsPropertiesView.slice(
            RepshopsPropertiesView.rsId,
            RepshopsPropertiesView.properties
        ).select {
            RepshopsPropertiesView.year eq year
        }.alias("rbp")
        val query = RepshopsTable.join(
            otherTable = rbp,
            joinType = JoinType.LEFT,
            onColumn = RepshopsTable.id,
            otherColumn = rbp[RepshopsPropertiesView.rsId]
        ).slice(
            RepshopsTable.id,
            RepshopsTable.name,
            rbp[RepshopsPropertiesView.properties]
        ).selectAll().withDistinct().andWhere {
            RepshopsTable.name.isNotNull()
        }
        return rbp to query
    }

    private fun Query.applyFilters(filters: List<RepairBasePropertiesColumnFilter>): Query {
        var filteredQuery = this.andWhere { RepshopsTable.hasProperties eq true }
            .andWhere { RepshopsTable.deleted eq false }
        filters.forEach { filter ->
            filteredQuery = this.andWhere {
                buildFilterPredicate(
                    condition = filter.condition,
                    values = filter.value,
                    type = filter.name.type,
                    exposedExpression = filter.name.exposedExpression
                )
            }
        }
        return filteredQuery
    }
}
