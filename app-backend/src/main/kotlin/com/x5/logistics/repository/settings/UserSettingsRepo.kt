package com.x5.logistics.repository.settings

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.Table
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository
import java.io.Serializable

@Repository
interface UserSettingsRepo: CrudRepository<UserSettingDao, UserSettingDaoId> {
    fun findByEmail(email: String): List<UserSettingDao>
}

@Entity
@Table(name = "user_setting")
@IdClass(UserSettingDaoId::class)
data class UserSettingDao(
    @Id
    @Column(name = "email", nullable = false)
    val email: String,

    @Id
    @Column(name = "key", nullable = false)
    val key: String,

    @Column(name = "value", nullable = false)
    val value: String
)

data class UserSettingDaoId(
    var email: String = "",
    var key: String = ""
): Serializable
