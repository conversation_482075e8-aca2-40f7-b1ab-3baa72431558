package com.x5.logistics.repository.dictionary.rr

import com.x5.logistics.data.dictionary.rr.RrDictionaryViewEntity
import com.x5.logistics.data.dictionary.rr.RrDictionaryViewEntityId
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface RrDictionaryViewEntityRepo: JpaRepository<RrDictionaryViewEntity, RrDictionaryViewEntityId> {
    @Query("""
        select entity
        from RrDictionaryViewEntity entity
        where entity.atp.id = :atpId
        and entity.tonnage = :tonnage
    """)
    fun findByAtpTonnage(
        @Param("atpId") atpId: Long,
        @Param("tonnage") tonnage: BigDecimal
    ): List<RrDictionaryViewEntity>

    @Query("""
        select count(entity)
        from RrDictionaryViewEntity entity
        where entity.atp.id = :atpId
        and entity.tonnage = :tonnage
    """)
    fun countByAtpTonnage(
        @Param("atpId") atpId: Long,
        @Param("tonnage") tonnage: BigDecimal
    ): Long
}
