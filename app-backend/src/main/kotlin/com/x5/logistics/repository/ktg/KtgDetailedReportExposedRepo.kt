package com.x5.logistics.repository.ktg

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.ktg.KtgDetailedReport
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.castToString
import com.x5.logistics.repository.nullable
import com.x5.logistics.rest.dto.charting.ChartColumn
import com.x5.logistics.rest.dto.charting.ChartColumnFilter
import com.x5.logistics.rest.dto.detailedreport.ExportDetailedReportRequest
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.count
import org.springframework.stereotype.Component

@Component
class KtgDetailedReportExposedRepo {
    val logger = getLogger()

    @ExposedTransactional
    fun getKtgExcelReportBody(
        req: ExportDetailedReportRequest,
        pageNumber: Int? = null,
        pageSize: Int? = null
    ): List<KtgDetailedReport> {
        val query = getQueryByColumns(
            req,
            KtgDetailedExportView.vehicleDate,
            KtgDetailedExportView.mvzId,
            KtgDetailedExportView.mvzName,
            KtgDetailedExportView.vehicleType,
            KtgDetailedExportView.vehicleGroup,
            KtgDetailedExportView.vehicleBrand,
            KtgDetailedExportView.vehicleModel,
            KtgDetailedExportView.vehicleTonnage,
            KtgDetailedExportView.vehicleLicense,
            KtgDetailedExportView.equnr,
            KtgDetailedExportView.vehicleVin,
            KtgDetailedExportView.rcSubmitId,
            KtgDetailedExportView.rcSubmitName,
            KtgDetailedExportView.trailerLicense,
            KtgDetailedExportView.retailNetwork,
            KtgDetailedExportView.atpType,
            KtgDetailedExportView.mrName,
            KtgDetailedExportView.atpName,
            KtgDetailedExportView.mvzType,
            KtgDetailedExportView.tsType,
            KtgDetailedExportView.marka,
            KtgDetailedExportView.model,
            KtgDetailedExportView.loadWgt,
            KtgDetailedExportView.tsDataEqunr,
            KtgDetailedExportView.fleetNum,
            KtgDetailedExportView.tsGroup,
            KtgDetailedExportView.status,
            KtgDetailedExportView.reason,
            KtgDetailedExportView.ktg,
            KtgDetailedExportView.rg,
            KtgDetailedExportView.createDateYear
        ).apply {
            if (pageSize != null && pageNumber != null) {
                limit(
                    n = pageSize,
                    offset = (pageNumber * pageSize).toLong()
                )
            }
        }.orderBy(
            KtgDetailedExportView.mrName to SortOrder.ASC,
            KtgDetailedExportView.atpName to SortOrder.ASC,
            KtgDetailedExportView.mvzName to SortOrder.ASC,
            KtgDetailedExportView.equnr to SortOrder.ASC,
            KtgDetailedExportView.vehicleDate to SortOrder.ASC
        )

        logger.info(query.prepareSQL(QueryBuilder(false)))

        return query.map { row ->
            KtgDetailedReport(
                vehicleDate = row[KtgDetailedExportView.vehicleDate],
                mvzId = row[KtgDetailedExportView.mvzId],
                mvzName = row[KtgDetailedExportView.mvzName],
                vehicleType = row[KtgDetailedExportView.vehicleType],
                vehicleGroup = row[KtgDetailedExportView.vehicleGroup],
                vehicleBrand = row[KtgDetailedExportView.vehicleBrand],
                vehicleModel = row[KtgDetailedExportView.vehicleModel],
                vehicleTonnage = row[KtgDetailedExportView.vehicleTonnage],
                vehicleLicense = row[KtgDetailedExportView.vehicleLicense],
                equnr = row[KtgDetailedExportView.equnr],
                vehicleVin = row[KtgDetailedExportView.vehicleVin],
                rcSubmitId = row[KtgDetailedExportView.rcSubmitId],
                rcSubmitName = row[KtgDetailedExportView.rcSubmitName],
                trailerLicense = row[KtgDetailedExportView.trailerLicense],
                retailNetwork = row[KtgDetailedExportView.retailNetwork].nullable,
                atpType = row[KtgDetailedExportView.atpType].nullable,
                mrName = row[KtgDetailedExportView.mrName].nullable,
                atpName = row[KtgDetailedExportView.atpName],
                mvzType = row[KtgDetailedExportView.mvzType],
                tsType = row[KtgDetailedExportView.tsType].nullable,
                marka = row[KtgDetailedExportView.marka],
                model = row[KtgDetailedExportView.model],
                loadWgt = row[KtgDetailedExportView.loadWgt],
                tsDataEqunr = row[KtgDetailedExportView.tsDataEqunr].nullable,
                fleetNum = row[KtgDetailedExportView.fleetNum],
                tsGroup = row[KtgDetailedExportView.tsGroup],
                status = row[KtgDetailedExportView.status],
                reason = row[KtgDetailedExportView.reason],
                ktg = row[KtgDetailedExportView.ktg],
                rg = row[KtgDetailedExportView.rg],
                year = row[KtgDetailedExportView.createDateYear]
            )
        }
    }

    @ExposedTransactional
    fun getKtgCount(req: ExportDetailedReportRequest) =
        getQueryByColumns(req, KtgDetailedExportView.equnr.count()).count()

    private fun getQueryByColumns(
        req: ExportDetailedReportRequest,
        column: Expression<*>,
        vararg columns: Expression<*>
    ): Query = with(KtgDetailedExportView) {
        select(
            column,
            *columns
        ).where {
            vehicleDate greaterEq req.from and (vehicleDate less req.to)
        }
    }.apply {
        KtgDetailedExportView.getGeoFilter(req.geoFilter).forEach { andWhere { it } }
    }.apply {
        req.filters?.forEach {
            val exposedExpression = getExposedExpression(it)
            if (exposedExpression != null) {
                andWhere {
                    buildFilterPredicate(
                        condition = it.condition,
                        values = it.value,
                        type = ColumnType.STRING,
                        strictFilter = true,
                        exposedExpression = exposedExpression.castToString()
                    )
                }
            }
        }
    }

    private fun getExposedExpression(filter: ChartColumnFilter?) = when (filter?.name) {
        ChartColumn.retail_network -> KtgDetailedExportView.retailNetwork
        ChartColumn.atpType -> KtgDetailedExportView.atpType
        ChartColumn.atp -> KtgDetailedExportView.atpName
        ChartColumn.mvz_name -> KtgDetailedExportView.mvzName
        ChartColumn.mvzType -> KtgDetailedExportView.mvzType
        ChartColumn.vehicle_type -> KtgDetailedExportView.vehicleType
        ChartColumn.vehicle_group -> KtgDetailedExportView.vehicleGroup
        ChartColumn.vehicle_brand -> KtgDetailedExportView.vehicleBrand
        ChartColumn.vehicle_model -> KtgDetailedExportView.vehicleModel
        ChartColumn.vehicle_licence -> KtgDetailedExportView.vehicleLicense
        ChartColumn.vehicle_gbo -> KtgDetailedExportView.vehicleGbo
        ChartColumn.mr -> KtgDetailedExportView.mrName
        ChartColumn.mvz -> KtgDetailedExportView.mvzId
        ChartColumn.vehicle_tonnage -> KtgDetailedExportView.vehicleTonnage
        ChartColumn.repair_atp -> KtgDetailedExportView.repshopName
        ChartColumn.year -> KtgDetailedExportView.dateYear
        ChartColumn.quarter -> KtgDetailedExportView.dateQuarter
        ChartColumn.month -> KtgDetailedExportView.dateMonth
        ChartColumn.week -> KtgDetailedExportView.dateWeek
        ChartColumn.report_week -> KtgDetailedExportView.dateReportWeek
        ChartColumn.day -> KtgDetailedExportView.dateDay
        ChartColumn.vehicle_create_date -> KtgDetailedExportView.createDateYear
        ChartColumn.terName -> KtgDetailedExportView.terName
        else -> null
    }
}