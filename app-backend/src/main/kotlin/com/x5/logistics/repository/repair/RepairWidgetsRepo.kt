package com.x5.logistics.repository.repair

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.repair.PlsByDay
import com.x5.logistics.data.repair.PlsByDayGroup
import com.x5.logistics.data.repair.RepairExposed
import com.x5.logistics.data.repair.RepairRatesView
import com.x5.logistics.data.repair.RepairStructures
import com.x5.logistics.repository.CountFiltered
import com.x5.logistics.repository.NullIf
import com.x5.logistics.repository.SumFiltered
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.times
import com.x5.logistics.repository.toNullable
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.repair.workspace.RepairWidgetExpensiveRepairReq
import com.x5.logistics.rest.dto.repair.workspace.RepairWidgetExpensiveRepairResp
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.isAlmostZero
import jakarta.persistence.EntityManager
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.Count
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.FloatColumnType
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.between
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.LocalDate

@Repository
class RepairWidgetsRepo(
    val em: EntityManager,
) {
    val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    @ExposedTransactional
    fun getExpensiveRepairsData(req: RepairWidgetExpensiveRepairReq): RepairWidgetExpensiveRepairResp {
        val qty = CountFiltered(RepairExposed.repairExpensesFull greater req.filterSum.toFloat()).alias("qty")
        val cost = SumFiltered(
            RepairExposed.repairExpensesFull,
            RepairExposed.repairExpensesFull greater req.filterSum.toFloat(),
            FloatColumnType()
        ).toNullable()
            .alias("repair_cost")
        val totalQty = Count(RepairExposed.orderId).alias("total_qty")
        val totalCost = Sum(RepairExposed.repairExpensesFull, FloatColumnType()).toNullable().alias("total_cost")
        val query = RepairExposed
            .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq RepairExposed.mvz) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq RepairExposed.startDate) and
                        (RepairExposed.startDate less OrganizationalUnitsTimelineTable.endDate)
            }
            .select(qty, cost, totalQty, totalCost)
            .where((RepairExposed.startDate greaterEq req.from) and (RepairExposed.startDate lessEq req.to))
            .applyGeoFilter(req.geoFilter)
        val res = query.single()

        val qtyOrders = res[qty].toBigInteger()
        val totalQtyOrders = res[totalQty].toBigInteger()
        val sumCostOrders = res[cost]?.toBigDecimal() ?: BigDecimal.ZERO
        val totalCostOrders = res[totalCost]?.toBigDecimal() ?: BigDecimal.ZERO

        return RepairWidgetExpensiveRepairResp(
            qtyOrders = qtyOrders,
            totalQtyOrders = totalQtyOrders,
            sumCostOrders = sumCostOrders,
            totalCostOrders = totalCostOrders,
            isEmpty = qtyOrders.isAlmostZero && totalQtyOrders.isAlmostZero && sumCostOrders.isAlmostZero
                    && totalCostOrders.isAlmostZero
        )
    }

    suspend fun getPlanExpenses(from: LocalDate, to: LocalDate, geoFilter: GeoFilter): Double? =
        newSuspendedTransaction {
            if (logQuery) addLogger(StdOutSqlLogger)
            val planExpenses =
                Sum(
                    PlsByDayGroup.mileage * Coalesce(RepairRatesView.rate, doubleLiteral(0.0)),
                    DoubleColumnType()
                ).alias("repairRubPlan")
            PlsByDayGroup
                .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                    (OrganizationalUnitsTimelineTable.mvzId eq PlsByDayGroup.mvzId) and
                            (OrganizationalUnitsTimelineTable.startDate lessEq PlsByDayGroup.vehicleDate) and
                            (PlsByDayGroup.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
                }
                .join(RepairRatesView, JoinType.LEFT) {
                    OrganizationalUnitsTimelineTable.atpId eq RepairRatesView.atpId and
                            (RepairRatesView.startDate lessEq PlsByDayGroup.vehicleDate) and
                            (RepairRatesView.endDate greater PlsByDayGroup.vehicleDate) and
                            (RepairRatesView.tonnage eq PlsByDayGroup.plTonnage)
                }
                .select(planExpenses)
                .where(PlsByDayGroup.vehicleDate.between(from, to))
                .applyGeoFilter(geoFilter)
                .also { if (logQuery) log.debug(it.prepareSQL(QueryBuilder(false))) }
                .single()[planExpenses]
        }

    suspend fun getFactExpenses(from: LocalDate, to: LocalDate, geoFilter: GeoFilter): Double? =
        newSuspendedTransaction {
            if (logQuery) addLogger(StdOutSqlLogger)
            val factExpenses = RepairStructures.structureExpense.sum().alias("factExp")
            RepairStructures
                .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                    (OrganizationalUnitsTimelineTable.mvzId eq RepairStructures.mvzId) and
                            (OrganizationalUnitsTimelineTable.startDate lessEq RepairStructures.startDate) and
                            (RepairStructures.startDate less OrganizationalUnitsTimelineTable.endDate)
                }
                .select(factExpenses)
                .where(RepairStructures.startDate.between(from, to))
                .applyGeoFilter(geoFilter)
                .single()[factExpenses]
        }

    suspend fun getMileage(from: LocalDate, to: LocalDate, geoFilter: GeoFilter): Double? = newSuspendedTransaction {
        if (logQuery) addLogger(StdOutSqlLogger)
        val mileage = Coalesce(
            NullIf(PlsByDayGroup.mileage.sum(), doubleLiteral(0.0)),
            doubleLiteral(1.0)
        ).alias("mileage")
        PlsByDayGroup
            .join(OrganizationalUnitsTimelineTable, JoinType.INNER) {
                (OrganizationalUnitsTimelineTable.mvzId eq PlsByDayGroup.mvzId) and
                        (OrganizationalUnitsTimelineTable.startDate lessEq PlsByDayGroup.vehicleDate) and
                        (PlsByDayGroup.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
            }
            .select(mileage)
            .where(PlsByDayGroup.vehicleDate.between(from, to) and (PlsByDayGroup.tsGroup neq stringLiteral("Вспомогательное")))
            .applyGeoFilter(geoFilter)
            .single()[mileage]
    }
}
