package com.x5.logistics.repository.dictionary.hrplaces

import com.x5.logistics.data.dictionary.hrplaces.HrTotalPlaceDao
import com.x5.logistics.rest.dto.dictionary.DictionaryInfoDto
import com.x5.logistics.service.dictionary.DictionaryUpdateInfo
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import jakarta.persistence.Tuple
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service
import java.sql.Timestamp

@Repository
interface HrTotalPlacesRepo: JpaRepository<HrTotalPlaceDao, Long>, HrTotalPlacesRepoCustom

interface HrTotalPlacesRepoCustom {
    fun getByName(name: String): HrTotalPlaceDao?
}

@Service
class HrTotalPlacesRepoCustomImpl: HrTotalPlacesRepoCustom, DictionaryUpdateInfo {
    @PersistenceContext
    private lateinit var entityManager: EntityManager

    override fun getByName(name: String): HrTotalPlaceDao? {
        val query = entityManager.createQuery("""
            select e
            from HrTotalPlaceDao e
            where e.name = :name
        """, HrTotalPlaceDao::class.java)
        query.setParameter("name", name)
        return query.resultList.singleOrNull()
    }

    override fun getLastUpdateInfo(): DictionaryInfoDto {
        val tupleRes = (entityManager.createNativeQuery(
            """
                (select updated_at as updatedat, updated_by as author
                 from hr_total_places
                 where updated_by != 'system'
                 order by updated_at desc nulls last 
                 limit 1)
                union
                (select created_at as updatedat, created_by as author
                 from hr_total_places
                 where created_by != 'system'
                 order by created_at desc nulls last 
                 limit 1)
                order by updatedat desc nulls last 
                limit 1
            """.trimIndent(), Tuple::class.java
        ).resultList as List<Tuple>).firstOrNull()
        return DictionaryInfoDto(
            name = "hr",
            updatedBy = (tupleRes?.get("author") as String?),
            updatedAt = (tupleRes?.get("updatedat") as Timestamp?)?.toLocalDateTime(),
        )
    }
}
