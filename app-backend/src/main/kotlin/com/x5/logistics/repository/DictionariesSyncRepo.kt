package com.x5.logistics.repository

import com.x5.logistics.ActiveDB
import com.x5.logistics.DataBaseConfigurations
import com.x5.logistics.config.ORG_UNIT_VERSION
import com.x5.logistics.getSingleConnectionHikariDataSource
import com.x5.logistics.util.getLogger
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.sql.Connection

@Component
class DictionariesSyncRepo(
    private val sources: DataBaseConfigurations
) {
    private val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    fun sync() {
        val activeSource = sources.dbConfigurations[ActiveDB.name]!!
        val sourcesToUpdate = sources.dbConfigurations.filterNot { it.key == ActiveDB.name }

        val activeHcpCon = getSingleConnectionHikariDataSource(activeSource)
        val sourcesToUpdateHcpCon = sourcesToUpdate.mapValues {
            getSingleConnectionHikariDataSource(it.value)
        }

        try {
            activeHcpCon.connection.use { activeDBConnection ->
                activeDBConnection.autoCommit = false
                val otherConnections = mutableListOf<Connection>()
                sourcesToUpdateHcpCon.forEach { (_, config) ->
                    config.connection.use { connection ->
                        connection.autoCommit = false
                        otherConnections.add(connection)

                        val tablesSortedToDelete = TablesForSync.entries.sortedBy { it.deletionOrder }
                        val tablesSortedToInsert = TablesForSync.entries.sortedByDescending { it.deletionOrder }

                        try {
                            tablesSortedToDelete.forEach { table ->
                                table.dropFkQueries?.let {
                                    connection.prepareStatement(it.also { log(it) }).execute()
                                }
                                connection.prepareStatement("DELETE FROM ${table.tableName} WHERE TRUE".also { log(it) })
                                    .execute()
                            }
                            tablesSortedToInsert.forEach { table ->
                                log.info("Processing table {}", table.tableName)
                                val result = activeDBConnection
                                    .prepareStatement("SELECT * FROM ${table.tableName}".also { log(it) })
                                    .executeQuery()
                                val placeHolders =
                                    table.fields.joinToString(separator = ",") { "?" }
                                val insert = connection.prepareStatement(
                                    "INSERT INTO ${table.tableName} VALUES ($placeHolders)".also { log(it) }
                                )
                                while (result.next()) {
                                    insert.clearParameters()
                                    table.fields.forEachIndexed { index, field ->
                                        log.info("Processing field {} with type {}", field, field.type)
                                        val idx = index + 1
                                        when (field.type) {
                                            FieldType.STRING -> {
                                                val value = result.getString(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.VARCHAR)
                                                } else {
                                                    insert.setString(idx, value)
                                                }
                                            }

                                            FieldType.INT -> {
                                                val value = result.getInt(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.INTEGER)
                                                } else {
                                                    insert.setInt(idx, value)
                                                }
                                            }

                                            FieldType.LONG -> {
                                                val value = result.getLong(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.BIGINT)
                                                } else {
                                                    insert.setLong(idx, value)
                                                }
                                            }

                                            FieldType.FLOAT -> {
                                                val value = result.getFloat(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.FLOAT)
                                                } else {
                                                    insert.setFloat(idx, value)
                                                }
                                            }

                                            FieldType.DOUBLE -> {
                                                log.info("Double field name: {}, expected type: {}, index: {}, idx: {}, raw value: {}", field.name, field.type, index, idx, result.getObject(idx).toString())
                                                val value = result.getDouble(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.DOUBLE)
                                                } else {
                                                    insert.setDouble(idx, value)
                                                }
                                            }
                                            FieldType.BOOLEAN -> {
                                                val value = result.getBoolean(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.BOOLEAN)
                                                } else {
                                                    insert.setBoolean(idx, value)
                                                }
                                            }

                                            FieldType.DATE -> {
                                                val value = result.getDate(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.DATE)
                                                } else {
                                                    insert.setDate(idx, value)
                                                }
                                            }
                                            FieldType.TIME -> {
                                                val value = result.getTime(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.TIME)
                                                } else {
                                                    insert.setTime(idx, value)
                                                }
                                            }
                                            FieldType.TIMESTAMP -> {
                                                val value = result.getTimestamp(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.TIMESTAMP)
                                                } else {
                                                    insert.setTimestamp(idx, value)
                                                }
                                            }

                                            FieldType.NUMERIC -> {
                                                val value = result.getBigDecimal(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.NULL)
                                                } else {
                                                    insert.setBigDecimal(idx, value)
                                                }
                                            }
                                            FieldType.MONTH -> {
                                                val value = result.getString(idx)
                                                if (result.wasNull()) {
                                                    insert.setNull(idx, java.sql.Types.VARCHAR)
                                                } else {
                                                    insert.setString(idx, value)
                                                }
                                            }
                                        }
                                    }
                                    insert.addBatch()
                                }
                                insert.executeBatch()
                                table.returnFkQueries?.let {
                                    connection.prepareStatement(it.also { log(it) }).execute()
                                }
                                if (table.haveIdSeq) {
                                    val seqName = table.customIdSeqName ?: "${table.tableName}_id_seq"
                                    connection.prepareStatement(
                                        "SELECT setval('$seqName',(SELECT MAX(id) FROM ${table.tableName}))"
                                            .also { log(it) }
                                    ).execute()
                                }
                            }
                            connection.prepareStatement("REFRESH MATERIALIZED VIEW ts.organizational_units_timeline").execute()
                            ORG_UNIT_VERSION.incrementAndGet()
                            connection.commit()
                        } catch (e: Exception) {
                            activeDBConnection.rollback()
                            activeDBConnection.close()
                            connection.rollback()
                            connection.close()
                            throw e
                        }
                    }
                }
                activeDBConnection.let { if (!it.isClosed) it.commit() }
                otherConnections.forEach { connection ->
                    connection.let { if (!it.isClosed) it.commit() }
                    connection.close()
                }

            }
        } catch (e: Exception) {
            activeHcpCon.close()
            sourcesToUpdateHcpCon.forEach { (_, config) ->
                config.close()
            }
            throw e
        } finally {
            activeHcpCon.close()
            sourcesToUpdateHcpCon.forEach { (_, config) -> config.close() }
        }
    }

    private fun log(message: String) {
        if (logQuery) log.debug(message)
    }

//    private enum class TablesForSync(
//        val tableName: String,
//        val fields: List<Field>,
//        val haveIdSeq: Boolean,
//        val deletionOrder: Int = 1000,
//    ) {
//        TEST_TABLE(
//            tableName = "test_table",
//            fields = listOf(
//                Field("id", FieldType.INT),
//                Field("name", FieldType.STRING),
//                Field("start_date", FieldType.DATE),
//                Field("fk", FieldType.INT),
//            ),
//            haveIdSeq = true,
//            deletionOrder = 10,
//        ),
//        TEST_TABLE_CHILD(
//            tableName = "test_table_child",
//            fields = listOf(
//                Field("id", FieldType.INT),
//            ),
//            haveIdSeq = true,
//        ),
//    }

    private enum class TablesForSync(
        val tableName: String,
        val fields: List<Field>,
        val haveIdSeq: Boolean,
        val customIdSeqName: String? = null,
        val deletionOrder: Int = 1000, //HACK управляет очередностью удаления и вставки для взаимосвязанных таблиц (fk)
        val dropFkQueries: String? = null, // удаляем fk из несинхронизируемых таблиц
        val returnFkQueries: String? = null, // после синхронизации возвращаем
    ) {
        MVZ_LOG(
            tableName = "mvz_log",
            fields = listOf(
                Field("uid", FieldType.STRING),
                Field("atp_id", FieldType.LONG),
                Field("start_date", FieldType.DATE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
                Field("id", FieldType.LONG),
            ),
            haveIdSeq = true,
            customIdSeqName = "mvz_log_new_id_seq",
            deletionOrder = 10,
        ),
        ATP_LOG(
            tableName = "atp_log",
            fields = listOf(
                Field("atp_id", FieldType.LONG),
                Field("mr_id", FieldType.LONG),
                Field("start_date", FieldType.DATE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
                Field("id", FieldType.LONG),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        MR_LOG(
            tableName = "mr_log",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("mr_id", FieldType.LONG),
                Field("ter_id", FieldType.LONG),
                Field("start_date", FieldType.DATE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        KIP_DICTIONARY_BY_ATP(
            tableName = "kip_dictionary_by_atp",
            fields = listOf(
                Field("atp_id", FieldType.LONG),
                Field("tonnage", FieldType.DOUBLE),
                Field("start_date", FieldType.DATE),
                Field("percentage", FieldType.DOUBLE),
                Field("author", FieldType.STRING),
                Field("updateat", FieldType.TIMESTAMP),
            ),
            haveIdSeq = false,
            deletionOrder = 10,
        ),
        KIP_DICTIONARY_BY_MVZ(
            tableName = "kip_dictionary_by_mvz",
            fields = listOf(
                Field("mvz_id", FieldType.STRING),
                Field("tonnage", FieldType.DOUBLE),
                Field("start_date", FieldType.DATE),
                Field("percentage", FieldType.DOUBLE),
                Field("author", FieldType.STRING),
                Field("updateat", FieldType.TIMESTAMP),
            ),
            haveIdSeq = false,
            deletionOrder = 10,
        ),
        HR_ATP_PLACES_LOG(
            tableName = "hr_atp_places_log",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("atp_id", FieldType.INT),
                Field("hrp_id", FieldType.INT),
                Field("start_date", FieldType.DATE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        HR_PLACES_TPLACES_LOG(
            tableName = "hr_places_tplaces_log",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("p_id", FieldType.INT),
                Field("tp_id", FieldType.INT),
                Field("start_date", FieldType.DATE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        HR_DRIVERS_PER_VEHICLE(
            tableName = "hr_drivers_per_vehicle",
            fields = listOf(
                Field("atp_id", FieldType.LONG),
                Field("year", FieldType.INT),
                Field("month", FieldType.MONTH),
                Field("value", FieldType.NUMERIC),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
            ),
            haveIdSeq = false,
            deletionOrder = 10,
        ),
        MVZ_CODES(
            tableName = "mvz_codes",
            fields = listOf(
                Field("uid", FieldType.STRING),
                Field("name", FieldType.STRING),
                Field("type", FieldType.STRING),
                Field("start_date", FieldType.DATE),
                Field("ut", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        ATP(
            tableName = "atp",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("name", FieldType.STRING),
                Field("type", FieldType.STRING),
                Field("retail_network", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            dropFkQueries = "ALTER table ts.repair_base_properties_atp DROP constraint repair_base_properties_atp_vrt_fk;",
            returnFkQueries = "ALTER table ts.repair_base_properties_atp add constraint repair_base_properties_atp_vrt_fk FOREIGN KEY (atp_id) REFERENCES ts.atp(id);"
        ),
        MACRO_REGION(
            tableName = "macro_region",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("name", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
        ),
        TERRITORY(
            tableName = "territory",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("name", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
        ),
        MVZ_TYPE(
            tableName = "mvz_type",
            fields = listOf(
                Field("type", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        ATP_TYPE(
            tableName = "atp_type",
            fields = listOf(
                Field("type", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        KIP_TARIF(
            tableName = "kip_tarif",
            fields = listOf(
                Field("mvz_id", FieldType.INT),
                Field("mvz", FieldType.STRING),
                Field("load_wgt", FieldType.NUMERIC),
                Field("month", FieldType.DATE),
                Field("kip_tr", FieldType.NUMERIC),
            ),
            haveIdSeq = false,
        ),
        KIP_TARIF_LOG(
            tableName = "kip_tarif_log",
            fields = listOf(
                Field("mvz_uid", FieldType.STRING),
                Field("load_wgt", FieldType.NUMERIC),
                Field("start_date", FieldType.DATE),
                Field("kip_tr", FieldType.NUMERIC),
                Field("is_deleted", FieldType.BOOLEAN),
                Field("author", FieldType.STRING),
                Field("updateAt", FieldType.TIMESTAMP),
            ),
            haveIdSeq = false,
            deletionOrder = 10,
        ),
        HR_PLACES(
            tableName = "hr_places",
            fields = listOf(
                Field("name", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
                Field("id", FieldType.INT),
            ),
            haveIdSeq = true,
        ),
        HR_TOTAL_PLACES(
            tableName = "hr_total_places",
            fields = listOf(
                Field("name", FieldType.STRING),
                Field("id", FieldType.INT),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("aggregated", FieldType.BOOLEAN),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
        ),
        NH_VEHICLES(
            tableName = "nh_vehicles",
            fields = listOf(
                Field("type", FieldType.STRING),
                Field("brand", FieldType.STRING),
                Field("vehicle_tonnage", FieldType.DOUBLE),
                Field("vehicle_create_year", FieldType.INT),
                Field("id", FieldType.LONG),
            ),
            haveIdSeq = true,
        ),
        NH_PLAN(
            tableName = "nh_plan",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("vehicle_id", FieldType.LONG),
                Field("start_date", FieldType.DATE),
                Field("plan_nh", FieldType.DOUBLE),
                Field("created_by", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        RR_DICTIONARY(
            tableName = "rr_dictionary",
            fields = listOf(
                Field("atp_id", FieldType.LONG),
                Field("toro_work_id", FieldType.STRING),
                Field("tonnage", FieldType.DOUBLE),
                Field("year", FieldType.INT),
                Field("month", FieldType.MONTH),
                Field("rate", FieldType.DOUBLE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("start_date", FieldType.DATE),
                Field("end_date", FieldType.DATE)
            ),
            haveIdSeq = false,
            deletionOrder = 10,
        ),
        RR_STRUCTURE_DICTIONARY(
            tableName = "rr_structure_dictionary",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("atp_id", FieldType.LONG),
                Field("year", FieldType.INT),
                Field("structure_type", FieldType.STRING),
                Field("month", FieldType.MONTH),
                Field("rate", FieldType.DOUBLE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("start_date", FieldType.DATE),
                Field("end_date", FieldType.DATE)
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        RR_DICTIONARY_GROUPS(
            tableName = "rr_dictionary_groups",
            fields = listOf(
                Field("atp_id", FieldType.LONG),
                Field("tonnage", FieldType.DOUBLE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
            ),
            haveIdSeq = false,
            deletionOrder = 50,
        ),
        RR_DICTIONARY_TONNAGE(
            tableName = "rr_dictionary_tonnage",
            fields = listOf(
                Field("tonnage", FieldType.DOUBLE),
            ),
            haveIdSeq = false,
            deletionOrder = 100,
        ),
        RR_DICTIONARY_TONNAGE_MAPPING(
            tableName = "rr_dictionary_tonnage_mapping",
            fields = listOf(
                Field("head_tonnage", FieldType.DOUBLE),
                Field("head_type", FieldType.STRING),
                Field("trail_tonnage", FieldType.DOUBLE),
                Field("trail_type", FieldType.STRING),
                Field("count_all", FieldType.INT),
                Field("tariff_tonnage", FieldType.DOUBLE),
            ),
            haveIdSeq = false,
        ),
        TORO_WORKS(
            tableName = "toro_works",
            fields = listOf(
                Field("id", FieldType.STRING),
                Field("name", FieldType.STRING),
                Field("subtype_id", FieldType.INT),
                Field("type_id", FieldType.INT),
                Field("updated_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("use_nh", FieldType.BOOLEAN),
            ),
            haveIdSeq = false,
            deletionOrder = 50,
        ),
        TORO_WORKS_SUBTYPES(
            tableName = "toro_works_subtypes",
            fields = listOf(
                Field("name", FieldType.STRING),
                Field("color", FieldType.STRING),
                Field("id", FieldType.INT),
                Field("created_by", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            customIdSeqName = "toro_works_subtypes_tmp_id_seq"
        ),
        TORO_WORKS_TYPES(
            tableName = "toro_works_types",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("name", FieldType.STRING),
                Field("created_by", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
        ),
        REPSHOPS(
            tableName = "repshops",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("name", FieldType.STRING),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
                Field("has_properties", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
        ),
        REPSHOPS_PROPERTIES(
            tableName = "repshops_properties",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("rs_id", FieldType.LONG),
                Field("property", FieldType.STRING),
                Field("year", FieldType.INT),
                Field("month", FieldType.MONTH),
                Field("value", FieldType.NUMERIC),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        ATP_REPSHOPS_LOG(
            tableName = "atp_repshops_log",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("atp_id", FieldType.INT),
                Field("rs_id", FieldType.INT),
                Field("start_date", FieldType.DATE),
                Field("created_at", FieldType.TIMESTAMP),
                Field("created_by", FieldType.STRING),
                Field("updated_at", FieldType.TIMESTAMP),
                Field("updated_by", FieldType.STRING),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        KTG_DICTIONARY(
            tableName = "ktg_dictionary",
            fields = listOf(
                Field("vehicle_age", FieldType.LONG),
                Field("ktg_percentage", FieldType.DOUBLE),
                Field("emergency_percentage", FieldType.DOUBLE),
                Field("ktg_emergency_percentage", FieldType.DOUBLE),
            ),
            haveIdSeq = false,
        ),
        CHART_DESKTOP(
            tableName = "chart_desktop",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("owner", FieldType.STRING),
                Field("name", FieldType.STRING),
                Field("create_date", FieldType.DATE),
                Field("update_date", FieldType.DATE),
            ),
            haveIdSeq = true,
        ),
        CHART_WIDGET(
            tableName = "chart_widget",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("desk_id", FieldType.LONG),
                Field("date_from", FieldType.DATE),
                Field("date_to", FieldType.DATE),
                Field("date_span", FieldType.STRING),
                Field("mvz", FieldType.STRING),
                Field("settings", FieldType.STRING),
                Field("create_date", FieldType.DATE),
                Field("update_date", FieldType.DATE),
                Field("location", FieldType.STRING),
                Field("name", FieldType.STRING),
                Field("type", FieldType.STRING),
                Field("geo_filter", FieldType.STRING),
                Field("global_filters_source", FieldType.STRING),
            ),
            haveIdSeq = true,
            deletionOrder = 10,
        ),
        USER_SETTING(
            tableName = "user_setting",
            fields = listOf(
                Field("email", FieldType.STRING),
                Field("key", FieldType.STRING),
                Field("value", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        REPORT_TEMPLATES(
            tableName = "report_templates",
            fields = listOf(
                Field("id", FieldType.LONG),
                Field("report", FieldType.STRING),
                Field("user", FieldType.STRING),
                Field("name", FieldType.STRING),
                Field("type", FieldType.STRING),
                Field("settings", FieldType.STRING),
                Field("create_time", FieldType.TIMESTAMP),
                Field("update_time", FieldType.TIMESTAMP),
                Field("deleted", FieldType.BOOLEAN),
            ),
            haveIdSeq = true,
        ),
        LOAD_WGT(
            tableName = "load_wgt",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("load_wgt", FieldType.NUMERIC),
                Field("name", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        REPAIRS_RUB_KM_PLAN(
            tableName = "repairs_rub_km_plan",
            fields = listOf(
                Field("date_from", FieldType.DATE),
                Field("date_to", FieldType.DATE),
                Field("coef", FieldType.NUMERIC),
            ),
            haveIdSeq = false,
        ),
        TYPES(
            tableName = "types",
            fields = listOf(
                Field("ts_type", FieldType.STRING),
                Field("ts_group", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        TS_READY_REASONS(
            tableName = "ts_ready_reasons",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("reason", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        TS_READY_STATUSES(
            tableName = "ts_ready_statuses",
            fields = listOf(
                Field("id", FieldType.STRING),
                Field("status", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        TS_BRAND(
            tableName = "ts_brand",
            fields = listOf(
                Field("id", FieldType.INT),
                Field("brand", FieldType.STRING),
            ),
            haveIdSeq = false,
        ),
        SUBSCRIPTIONS_MAIL_EVENTS(
            tableName = "subscription_mail_events",
            fields = listOf(
                Field("email", FieldType.STRING),
                Field("type", FieldType.STRING),
                Field("trigger_id", FieldType.STRING),
                Field("state", FieldType.STRING),
                Field("send_time", FieldType.TIMESTAMP),
            ),
            haveIdSeq = false,
        ),
        USER_EMAIL_SUBSCRIPTIONS(
            tableName = "user_email_subscription",
            fields = listOf(
                Field("email", FieldType.STRING),
                Field("subject_id", FieldType.STRING),
                Field("subscription_date", FieldType.DATE),
                Field("unsubscription_date", FieldType.DATE),
                Field("created_by", FieldType.STRING),
                Field("changed_by", FieldType.STRING),
            ),
            haveIdSeq = false,
            deletionOrder = 10,
        ),
        USER_EMAIL_SUBSCRIPTION_SUBJECT(
            tableName = "user_email_subscription_subject",
            fields = listOf(
                Field("id", FieldType.STRING),
                Field("subject", FieldType.STRING),
                Field("order_number", FieldType.INT),
            ),
            haveIdSeq = false,
        ),
    }

    private data class Field(
        val name: String,
        val type: FieldType,
    )

    private enum class FieldType {
        STRING, INT, LONG, FLOAT, DOUBLE, BOOLEAN, DATE, TIME, TIMESTAMP, NUMERIC, MONTH
    }
}