package com.x5.logistics.repository.dictionary

import com.x5.logistics.repository.jsonbMap
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.timestamp

object ReportTemplatesTable: LongIdTable("report_templates") {
    val report = text("report")
    val user = text("user")
    val name = text("name")
    val type = text("type")
    val settings = jsonbMap("settings")
    val createTime = timestamp("create_time")
    val updateTime = timestamp("update_time")
    val deleted = bool("deleted")
}
