package com.x5.logistics.rest.dto.kip.details

import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Описание фильтра для детального отчета по КИП.")
data class KipDetailsColumnFilter(
    @Schema(description = "Колонка для фильтра.")
    val name: KipDetailsColumn,
    @Schema(description = "Тип фильтра.")
    val condition: FilterCondition,
    @Schema(description = "Список значений.")
    val value: List<Any>
)
