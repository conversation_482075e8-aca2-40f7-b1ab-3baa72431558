package com.x5.logistics.rest.dto.repair.workspace.qtycost

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос данных по ремонтам - виджет рабочего стола по кол-ву/затратам на ремонт на ТС.")
data class RepairWidgetQtyCostsReq(
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,
    @Schema(description = "Дата конца периода.")
    val to: LocalDate,
    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
)
