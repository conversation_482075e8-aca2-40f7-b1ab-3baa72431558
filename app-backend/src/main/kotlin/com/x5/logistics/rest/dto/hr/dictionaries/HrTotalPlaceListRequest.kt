package com.x5.logistics.rest.dto.hr.dictionaries

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Запрос списка итоговых подразделений")
data class HrTotalPlaceListRequest(

    @Schema(description = "Номер страницы.")
    val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    val pageSize: Int,

    @Schema(description = "Список сортировок.")
    val sort: List<HrTotalPlaceSortOrder>
)
