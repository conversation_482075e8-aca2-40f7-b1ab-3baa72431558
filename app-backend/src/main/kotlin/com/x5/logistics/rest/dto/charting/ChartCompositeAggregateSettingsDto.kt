package com.x5.logistics.rest.dto.charting

data class ChartCompositeAggregateSettingsDto(
    val expressions: String,
    val valueType: ChartValueType,
    val parameters: List<ChartCompositeAggregateSettingsParameterDto>,
)

data class ChartCompositeAggregateSettingsParameterDto(
    val name: String,
    val functions: String,
    val filters: List<ChartColumnFilter>,
)

enum class ChartValueType { share, value }
