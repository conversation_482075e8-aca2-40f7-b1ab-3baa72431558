package com.x5.logistics.rest.controller.kip

import com.x5.logistics.rest.dto.kip.qty.KipQtyReq
import com.x5.logistics.rest.dto.kip.qty.KipQtyResp
import com.x5.logistics.service.VehicleUsageService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

@RestController
class KipQtyController(
    val service: VehicleUsageService
) {
    private val log = getLogger()

    @Tag(name = "КИП данные.")
    @Operation(summary = "Запрос на КИП данные для заданного периода дат и МВЗ.")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на КИП данные.",
        content = [
            Content(
                schema = Schema(
                    implementation = KipQtyReq::class
                )
            )
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        schema = Schema(
                            implementation = KipQtyResp::class
                        )
                    )
                ]
            )
        ]
    )
    @PostMapping("/api/kip/qty")
    fun process(@RequestBody req: KipQtyReq): KipQtyResp {
        log.debug("Kip qty. [from={}, to={}, mvz={}]", req.from, req.to, req.geoFilter)
        return service.getKipQty(req)
    }
}
