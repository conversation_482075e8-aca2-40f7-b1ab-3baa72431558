package com.x5.logistics.rest.dto.dictionary.org.atp

import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import java.time.LocalDate



data class OrgAtpDictionaryCreateRequest(
    @field:Size(min = 2, max = 50)
    val atpName: String,

    val atpType: String,

    @field:Pattern(regexp = "Чижик|Пятерочка|Перекресток|Не назначена")
    val retailNetwork: String,

    val mrId: Long,

    val startDate: LocalDate
)
