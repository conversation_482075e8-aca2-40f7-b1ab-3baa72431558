package com.x5.logistics.rest.controller.ktg

import com.x5.logistics.rest.dto.kip.details.KipDetailsQtyReq
import com.x5.logistics.rest.dto.ktg.KtgDetailsQtyReq
import com.x5.logistics.service.ktg.KtgReportService
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.getLogger
import com.x5.logistics.util.moscowDateTime
import com.x5.logistics.util.urlEncoded
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.runBlocking
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody


@RestController
class KtgDetailsExportQtyController(
    val service: KtgReportService
) {
    private val log = getLogger()

    @Tag(name = "КТГ данные.")
    @Operation(summary = "Запрос на экспорт в XLSX детального отчета КТГ по количеству транспортных средств для заданного периода дат и МВЗ.")
    @SwaggerReqBody(
        description = "Запрос на экспорт в XLSX детального отчета по КТГ.",
        content = [Content(schema = Schema(implementation = KipDetailsQtyReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(type = "string", format = "binary")
                )]
            )
        ]
    )
    @PostMapping("/api/ktg/details/export")
    fun process(@RequestBody req: KtgDetailsQtyReq, token: JwtToken?): ResponseEntity<Resource> {
        log.debug("Export ktg detail data to xlsx. [req={}]", req)
        val out = runBlocking { service.export(req, token.username) }
        val fileName = "${ReportName.KTG.title} ${moscowDateTime()}.xlsx".urlEncoded
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=$fileName"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(out))
    }
}
