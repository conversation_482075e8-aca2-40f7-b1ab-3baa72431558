package com.x5.logistics.rest.dto.vehicle.details

import com.x5.logistics.rest.util.FilterGroup
import com.x5.logistics.rest.util.FilterGroup.*
import com.x5.logistics.rest.util.FilterRespEnum

@Suppress("EnumEntryName")
enum class VehicleDetailedFiltersFront(
    override val label: String,
    override val group: FilterGroup,
    override val filterable: Boolean = true
) : FilterRespEnum {

    vehicleGroup(
        group = string,
        label = "Вид ТС"
    ),
    vehicleType(
        group = string,
        label = "Тип ТС"
    ),
    terName(
        group = stringSearch,
        label = "Территория",
        filterable = false
    ),
    mrName(
        group = stringSearch,
        label = "Макрорегион",
        filterable = false
    ),
    atp(
        group = stringSearch,
        label = "АТП",
        filterable = false
    ),
    mvz(
        group = stringSearch,
        label = "МВЗ",
        filterable = false
    ),
    mvzName(
        group = stringSearch,
        label = "Название МВЗ",
        filterable = false
    ),
    mvzType(
        group = stringSearch,
        label = "Тип МВЗ",
        filterable = false
    ),
    brand(
        group = string,
        label = "Марка"
    ),
    model(
        group = string,
        label = "Модель"
    ),
    year(
        group = number,
        label = "Год выпуска"
    ),
    commissioningRealDate(
        group = date,
        label = "Дата ввода в эксплуатацию"
    ),
    commissioningDate(
        group = number,
        label = "Год ввода в эксплуатацию"
    ),
    mileage(
        group = number,
        label = "Пробег"
    ),
    tonnage(
        group = number,
        label = "Тоннаж"
    ),
    compartAmount(
        group = number,
        label = "Паллетовместимость"
    ),
    licenseNum(
        group = stringSearch,
        label = "Гос. номер ТС"
    ),
    eqUnit(
        group = stringSearch,
        label = "Единица оборудования"
    ),
    vin(
        group = stringSearch,
        label = "VIN номер"
    ),
    gbo(
        group = boolean,
        label = "ГБО"
    ),
    avgQty(
        group = number,
        label = "Среднее за период"
    ),
    qtyForLastDate(
        group = number,
        label = "ТС на конец периода"
    )
}
