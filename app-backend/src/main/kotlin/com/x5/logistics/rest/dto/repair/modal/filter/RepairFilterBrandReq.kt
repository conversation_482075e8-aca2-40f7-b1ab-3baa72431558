package com.x5.logistics.rest.dto.repair.modal.filter

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

/**
 * Implementation of JRAAVTO-40 https://wiki.x5.ru/pages/viewpage.action?pageId=280896401
 * <AUTHOR> ( <EMAIL> )
 */

@Schema(description = "Запрос полного дерева марок ТС с тоннажом.")
data class RepairFilterBrandReq(
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    val to: LocalDate,

    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,

    @Schema(description = "Список фильтров.")
    val filters: List<RepairModalColumnFilter>?,
)