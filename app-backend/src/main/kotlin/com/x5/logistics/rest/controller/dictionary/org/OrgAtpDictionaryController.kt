package com.x5.logistics.rest.controller.dictionary.org

import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryCreateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryCreateRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryDeleteLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryFilters
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryItemDto
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryListRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryPagedResponse
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionarySelects
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryUpdateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryUpdateRequest
import com.x5.logistics.service.dictionary.org.OrgAtpDictionaryService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.dateFormatter
import com.x5.logistics.util.name
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId

@RestController
@RequestMapping("/api/dictionary/org")
class OrgAtpDictionaryController(
    private val orgAtpDictionaryService: OrgAtpDictionaryService
) {

    @Tag(name = "Словари")
    @Operation(summary = "Получение содержимого справочника АТП")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на получение содержимого справочника АТП",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryPagedResponse::class))]
            )]
    )
    @PostMapping("/atp")
    suspend fun getList(@Valid @RequestBody req: OrgAtpDictionaryListRequest): OrgAtpDictionaryPagedResponse {
        return orgAtpDictionaryService.getList(req)
    }

    @Tag(name = "Словари")
    @Operation(summary = "Экспорт справочника АТП")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на экспорт справочника АТП",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryListRequest::class))]
    )
    @ApiResponses(
        value = [ApiResponse(
            responseCode = "200",
            description = "Успешный ответ.",
            content = [Content(
                mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                schema = Schema(type = "string", format = "binary")
            )]
        )]
    )
    @PostMapping("/atp/export")
    suspend fun export(@Valid @RequestBody req: OrgAtpDictionaryListRequest): ResponseEntity<Resource> {
        val fileName = "Справочник АТП ${dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))}.xlsx"
        return ResponseEntity.ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=${
                    withContext(Dispatchers.IO) {
                        URLEncoder.encode(fileName, "UTF-8")
                    }.replace("+", "%20")
                }"
            )
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(orgAtpDictionaryService.export(req)))
    }

    @Tag(name = "Словари")
    @Operation(summary = "Создание нового АТП")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на создание нового АТП",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryCreateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryItemDto::class))]
            )]
    )
    @PostMapping("/atp-new")
    suspend fun createAtp(
        token: JwtToken?,
        @Valid @RequestBody req: OrgAtpDictionaryCreateRequest
    ): OrgAtpDictionaryItemDto {
        return newSuspendedTransaction { orgAtpDictionaryService.createAtp(req, token.name) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Редактирование АТП")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на редактирование АТП",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryCreateRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryItemDto::class))]
            )]
    )
    @PutMapping("/atp")
    suspend fun updateAtp(
        token: JwtToken?,
        @Valid @RequestBody req: OrgAtpDictionaryUpdateRequest
    ): OrgAtpDictionaryItemDto {
        return newSuspendedTransaction { orgAtpDictionaryService.updateAtp(req, token.name) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Создание новой связи АТП-МР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на создание новой связи АТП-МР",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryCreateLogEntryRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryItemDto::class))]
            )]
    )
    @PostMapping("/atp-log")
    suspend fun createLogEntry(
        token: JwtToken?,
        @Valid @RequestBody req: OrgAtpDictionaryCreateLogEntryRequest
    ): OrgAtpDictionaryItemDto {
        return newSuspendedTransaction { orgAtpDictionaryService.createLogEntry(req, token.name) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Редактирование связи АТП-МР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на редактирование связи АТП-МР",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryUpdateLogEntryRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryItemDto::class))]
            )]
    )
    @PutMapping("/atp-log")
    suspend fun updateLogEntry(token: JwtToken?, @Valid @RequestBody req: OrgAtpDictionaryUpdateLogEntryRequest): OrgAtpDictionaryItemDto {
        return newSuspendedTransaction { orgAtpDictionaryService.updateLogEntry(req, token.name) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Удаление связи АТП-МР")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на удаление связи АТП-МР",
        content = [Content(schema = Schema(implementation = OrgAtpDictionaryDeleteLogEntryRequest::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryItemDto::class))]
            )]
    )
    @DeleteMapping("/atp-log")
    suspend fun deleteLogEntry(token: JwtToken?, @RequestBody req: OrgAtpDictionaryDeleteLogEntryRequest): OrgAtpDictionaryItemDto {
        return newSuspendedTransaction { orgAtpDictionaryService.deleteLogEntry(req, token.name) }
    }

    @Tag(name = "Словари")
    @Operation(summary = "Получение фильтров справочника АТП")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionaryFilters::class))]
            )]
    )
    @GetMapping("/atp-filters")
    suspend fun getFilters(): OrgAtpDictionaryFilters {
        return orgAtpDictionaryService.getFilters()
    }

    @Tag(name = "Словари")
    @Operation(summary = "Словари селектов АТП-МР")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = OrgAtpDictionarySelects::class))]
            )]
    )
    @GetMapping("/atp")
    suspend fun getSelects(): OrgAtpDictionarySelects {
        return orgAtpDictionaryService.getSelects()
    }
}