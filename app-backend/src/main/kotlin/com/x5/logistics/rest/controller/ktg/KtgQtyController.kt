package com.x5.logistics.rest.controller.ktg

import com.x5.logistics.rest.dto.kip.qty.KtgQtyReq
import com.x5.logistics.rest.dto.kip.qty.KtgQtyResp
import com.x5.logistics.service.VehicleUsageService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

@RestController
class KtgQtyController(
    val service: VehicleUsageService
) {
    private val log = getLogger()

    @Tag(name = "КТГ данные.")
    @Operation(summary = "Запрос на КТГ данные для заданного периода дат и МВЗ.")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на КТГ данные.",
        content = [
            Content(
                schema = Schema(
                    implementation = KtgQtyReq::class
                )
            )
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        schema = Schema(
                            implementation = KtgQtyResp::class
                        )
                    )
                ]
            )
        ]
    )
    @PostMapping("/api/ktg/qty")
    fun process(@RequestBody req: KtgQtyReq): KtgQtyResp {
        log.debug("Ktg qty. [from={}, to={}, mvz={}]", req.from, req.to, req.geoFilter)
        return service.getKtgQty(req)
    }
}
