package com.x5.logistics.rest.dto.carin.desktopwidget

data class CarInDesktopWidgetResponse(
    val ownCars: Double?,
    val gpsOwnCars: Double?,
    val hiredCars: Double?,
    val gpsHiredCars: Double?,
    val allCars: Double?,
    val gpsAllCars: Double?,
    val diagramPointsOwnCars: List<Double?>,
    val diagramPointsHiredCars: List<Double?>,
    val diagramPointsAllCars: List<Double?>,
    val isEmpty: <PERSON>olean,
)
