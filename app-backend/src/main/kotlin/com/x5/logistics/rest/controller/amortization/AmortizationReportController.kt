package com.x5.logistics.rest.controller.amortization

import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.MassFilterResp
import com.x5.logistics.rest.dto.amortization.AmortizationReportColumnDto
import com.x5.logistics.rest.dto.amortization.AmortizationReportMassFilterReq
import com.x5.logistics.rest.dto.amortization.AmortizationReportReq
import com.x5.logistics.rest.dto.amortization.AmortizationReportResp
import com.x5.logistics.service.amortization.AmortizationReportService
import com.x5.logistics.util.JwtToken
import com.x5.logistics.util.username
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("/api/report/amortization")
class AmortizationReportController(
    val service: AmortizationReportService
) {
    @Tag(name = "Детальный отчет по амортизации")
    @Operation(summary = "Получение названия столбцов на детальный отчет по амортизации")
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = AmortizationReportColumnDto::class))]
            )
        ]
    )
    @GetMapping
    fun getReportColumns() = service.getReportColumns()

    @Tag(name = "Детальный отчет по амортизации")
    @Operation(summary = "Получение содержимого отчета по амортизации")
    @SwaggerReqBody(
        description = "Запрос на детальный отчет по амортизации",
        content = [Content(schema = Schema(implementation = AmortizationReportReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = AmortizationReportResp::class))]
            )
        ]
    )
    @PostMapping
    fun getReport(@RequestBody req: AmortizationReportReq) = service.getReport(req)

    @Tag(name = "Детальный отчет по амортизации")
    @Operation(summary = "Запрос на получение доступных значений фильтра с учетом уже выбранных фильтров")
    @SwaggerReqBody(
        description = "Запрос на получение доступных значений фильтров",
        content = [Content(schema = Schema(implementation = AmortizationReportMassFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = LabeledValue::class))]
            )
        ]
    )
    @PostMapping("/filter")
    fun getFilterValues(@RequestBody req: AmortizationReportMassFilterReq) = service.getFilterValues(req)

    @Tag(name = "Детальный отчет по амортизации")
    @Operation(summary = "Запрос проверки данных для поиска по списку значений")
    @SwaggerReqBody(
        description = "Запрос на получение доступных значений фильтров",
        content = [Content(schema = Schema(implementation = AmortizationReportMassFilterReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = MassFilterResp::class))]
            )
        ]
    )
    @PostMapping("/massfilter")
    fun getMassFilter(@RequestBody req: AmortizationReportMassFilterReq) = service.getMassFilter(req)

    @Tag(name = "Детальный отчет по амортизации")
    @Operation(summary = "Запрос на экспорт в XLSX отчета по амортизации")
    @SwaggerReqBody(
        description = "Запрос на экспорт в XLSX детального отчета по амортизации",
        content = [Content(schema = Schema(implementation = AmortizationReportReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(
                    mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    schema = Schema(
                        type = "string",
                        format = "binary"
                    )
                )]
            )
        ]
    )
    @PostMapping("/export")
    fun exportReport(
        @RequestBody req: AmortizationReportReq,
        token: JwtToken?
    ) = service.exportReport(req, token.username)
}
