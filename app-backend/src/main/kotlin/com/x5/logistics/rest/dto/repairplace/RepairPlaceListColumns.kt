package com.x5.logistics.rest.dto.repairplace

import com.fasterxml.jackson.annotation.JsonProperty
import com.x5.logistics.repository.ColumnType

enum class RepairPlaceListColumns(
    val title: String,
    val type: ColumnType,
    val filterable: Boolean = true,
    val highlightings: List<Highlighting> = emptyList()
) {
    @JsonProperty("terName")
    TERRITORY("Территория", ColumnType.STRING),

    @JsonProperty("mrName")
    MACRORIGION("Макрорегион", ColumnType.STRING),

    @JsonProperty("repairPlace")
    REPAIR_PLACE("Ремзона", ColumnType.STRING),

    @JsonProperty("serviceTer")
    SERVICE_TER("Обслуж. территория", ColumnType.STRING),

    @JsonProperty("serviceMr")
    SERVICE_MR("Обслуж. МР", ColumnType.STRING),

    @JsonProperty("serviceAtp")
    SERVICE_ATP("Обслуж. АТП", ColumnType.STRING),

    @JsonProperty("serviceMvz")
    SERVICE_MVZ("Обслуж. МВЗ", ColumnType.STRING),

    @JsonProperty("tsNumber")
    TS_NUMBER("План кол-во ТС, шт.", ColumnType.NUMBER),

    @JsonProperty("factTsNumber")
    FACT_TS_NUMBER("Факт. кол-во ТС, шт.", ColumnType.NUMBER),

    @JsonProperty("atpRequirement")
    ATP_REQUIREMENT("Потребность АТП, НЧ", ColumnType.NUMBER),

    @JsonProperty("goalPostProduction")
    GOAL_POST_PRODUCTION("Цел. выработка поста, НЧ", ColumnType.NUMBER),

    @JsonProperty("postsNumberPerDay")
    POSTS_NUMBER_PER_DAY("Кол-во постов ДЕНЬ, шт", ColumnType.NUMBER),

    @JsonProperty("postsNumberPerNight")
    POSTS_NUMBER_PER_NIGHT("Кол-во постов НОЧЬ, шт", ColumnType.NUMBER),

    @JsonProperty("totalPostsNumber")
    TOTAL_POSTS_NUMBER("Кол-во постов ВСЕГО, шт.", ColumnType.NUMBER),

    @JsonProperty("repairPlaceWorkingPower")
    REPAIR_PLACE_WORKING_POWER("Раб. мощность РЗ, НЧ", ColumnType.NUMBER),

    @JsonProperty("repairPlaceTotalPower")
    REPAIR_PLACE_TOTAL_POWER("Пол. мощность РЗ, НЧ", ColumnType.NUMBER),

    @JsonProperty("goalRepairPlaceProduction")
    GOAL_REPAIR_PLACE_PRODUCTION("Целевая выработка РЗ, НЧ", ColumnType.NUMBER),

    @JsonProperty("repairPlaceProvision")
    REPAIR_PLACE_PROVISION(
        "Обеспеч. РЗ, %", ColumnType.NUMBER, highlightings = HighlightingsConstants.REPAIR_PLACE_PROVISION_HIGHLIGHTING
    ),

    @JsonProperty("workPercent")
    WORK_PERCENT(
        "Доля работ РЗ, %", ColumnType.NUMBER, highlightings = HighlightingsConstants.ONE_SIDE_HIGHLIGHTING
    ),

    @JsonProperty("factRepairPlaceProduction")
    FACT_REPAIR_PLACE_PRODUCTION("Факт. выработка РЗ, НЧ", ColumnType.NUMBER),

    @JsonProperty("repairPlaceProductionPlanPerformance")
    REPAIR_PLACE_PRODUCTION_PLAN_PERFORMANCE(
        "Выполнение плана выработки РЗ, %", ColumnType.NUMBER, highlightings = HighlightingsConstants.TWO_SIDE_HIGHLIGHTING
    ),

    @JsonProperty("factPostProduction")
    FACT_POST_PRODUCTION("Факт. выработка поста, НЧ", ColumnType.NUMBER),

    @JsonProperty("goalMechanicProduction")
    GOAL_MECHANIC_PRODUCTION("Цел. выработка слесаря, НЧ", ColumnType.NUMBER),

    @JsonProperty("goalMechanicRequirement")
    GOAL_MECHANIC_REQUIREMENT("Цел. потребность слесарей, чел.", ColumnType.NUMBER),

    @JsonProperty("factMechanicSsch")
    FACT_MECHANIC_SSCH("Факт. ССЧ слесарей, чел.", ColumnType.NUMBER),

    @JsonProperty("factMechanicNumber")
    FACT_MECHANIC_NUMBER("Факт. слесарей по штатному расписанию, чел.", ColumnType.NUMBER),

    @JsonProperty("factMechanicProduction")
    FACT_MECHANIC_PRODUCTION("Факт. выработка слесаря, НЧ", ColumnType.NUMBER),

    @JsonProperty("mechanicProductionPlanPerformance")
    MECHANIC_PRODUCTION_PLAN_PERFORMANCE(
        "Выполнение плана выработки слесарей, %", ColumnType.NUMBER, highlightings = HighlightingsConstants.TWO_SIDE_HIGHLIGHTING
    ),

    @JsonProperty("mechanicProvision")
    MECHANIC_PROVISION(
        "Обеспеч. слесарями", ColumnType.NUMBER, highlightings = HighlightingsConstants.TWO_SIDE_HIGHLIGHTING
    )
}

data class Highlighting(
    val threshold: Double,
    val argb: String?
)

object HighlightingsConstants {
    val ONE_SIDE_HIGHLIGHTING = listOf(
        Highlighting(0.8, "FFFFFFFF"),
        Highlighting(0.7, "FFFFD2D2"),
        Highlighting(0.6, "FFFFA49E"),
        Highlighting(-0.1, "FFF45948")
    )

    val REPAIR_PLACE_PROVISION_HIGHLIGHTING = listOf(
        Highlighting(0.95, "FFFFFFFF"),
        Highlighting(0.75, "FFFFD2D2"),
        Highlighting(0.5, "FFFFA49E"),
        Highlighting(-0.1, "FFF45948")
    )

    val TWO_SIDE_HIGHLIGHTING = listOf(
        Highlighting(1.15, "FFFAC529"),
        Highlighting(1.1, "FFF7DE6E"),
        Highlighting(1.0, "FFFDEC96"),
        Highlighting(0.95, "FFFFFFFF"),
        Highlighting(0.85, "FFFFD2D2"),
        Highlighting(0.75, "FFFFA49E"),
        Highlighting(-0.1, "FFF45948")
    )

}