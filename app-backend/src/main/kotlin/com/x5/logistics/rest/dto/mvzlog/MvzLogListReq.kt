package com.x5.logistics.rest.dto.mvzlog

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Запрос на список МВЗ.")
data class MvzLogListReq(
    @Schema(description = "Номер страницы.")
    val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    val pageSize: Int,

    @Schema(description = "Список сортировок.")
    val sort: List<MvzLogSortOrder>,

    @Schema(description = "Список фильтров.")
    val filters: List<MvzLogColumnFilter>
)
