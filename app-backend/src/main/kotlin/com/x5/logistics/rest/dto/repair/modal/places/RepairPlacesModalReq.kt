package com.x5.logistics.rest.dto.repair.modal.places

import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос данных по ремонтам - Модальный виджет Ремонты на ремзоне.")
data class RepairPlacesModalReq(
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    val to: LocalDate,

    val repshops: List<Long>
)
