package com.x5.logistics.rest.dto.waybill

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Ответ на детальный отчет по путевым листам.")
data class WaybillItemDetailsQtyResp(
    @Schema(description = "Метка ПЛ.")
    val labels: List<WaybillLabel>?,

    @Schema(description = "ПЛ с метками.")
    val labeledWB: Int?,

    @Schema(description = "Статус ПЛ.")
    val status: String?,

    @Schema(description = "Территория")
    val terName: String?,

    @Schema(description = "Наименование макрорегиона")
    val mrName: String?,

    @Schema(description = "Номер ПЛ.")
    val number: String?,

    @Schema(description = "Дата открытия.")
    val dateOpen: String?,

    @Schema(description = "Время открытия.")
    val timeOpen: String?,

    @Schema(description = "Плановая дата закрытия.")
    val dateClose: String?,

    @Schema(description = "Плановое время закрытия.")
    val timeClose: String?,

    @Schema(description = "Ссылочная дата закрытия.")
    val dateCloseReference: String?,

    @Schema(description = "Ссылочное время закрытия.")
    val timeCloseReference: String?,

    @Schema(description = "Номер МВЗ в ПЛ.")
    val mvzInWB: String?,

    @Schema(description = "Название МВЗ в ПЛ.")
    val mvzNameInWB: String?,

    @Schema(description = "Табельный номер водителя")
    val driverNumber: Long?,

    @Schema(description = "ФИО водителя")
    val driver: Long?,  //(String?) Пока что табельный номер

    @Schema(description = "Марка.")
    val brand: String?,

    @Schema(description = "Модель.")
    val model: String?,

    @Schema(description = "Год ввода в эксплуатацию.")
    val commissioningDate: Int?,

    @Schema(description = "Год выпуска.")
    val year: Short?,

    @Schema(description = "Тоннаж.")
    val tonnage: Float?,

    @Schema(description = "Гос. номер ТС.")
    val licenseNum: String?,

    @Schema(description = "Гос. номер прицепа.")
    val trailerLicenseNum: String?,

    @Schema(description = "Единица оборудования.")
    val eqUnit: Long?,

    @Schema(description = "VIN номер. ")
    val vin: String?,

    @Schema(description = "Признак установленного ГБО.")
    val gbo: Boolean?,

    @Schema(description = "Первичное топливо, л.")
    val primaryFuel: Float?,

    @Schema(description = "Вторичное топливо, л.")
    val secondaryFuel: Float?,

    @Schema(description = "Пробег ПЛ, км.")
    val mileage: Double?,

    @Schema(description = "Часы ПЛ.")
    val hours: BigDecimal?,

    @Schema(description = "Часы работы ХОУ.")
    val motoHours: Double?,

    @Schema(description = "Вид ТС.")
    val vehicleGroup: String?,

    @Schema(description = "Тип ТС.")
    val vehicleType: String?,

    @Schema(description = "АТП.")
    val atp: String?,

    @Schema(description = "МВЗ.")
    val mvz: String?,

    @Schema(description = "Наименование МВЗ.")
    val mvzName: String?,

    @Schema(description = "Кол-во ПЛ")
    val waybillQty: Int?,

    @Schema(description = "Ремонтный ПЛ")
    val maintenance: Boolean?,

    @Schema(description = "Тип ПЛ")
    val typeWB: String?,

    @Schema(description = "Признак командировка")
    val tod: Boolean?,

    @Schema(description = "Признак коммерция")
    val commerce: Boolean?,

    @Schema(description = "Признак перегон")
    val transit: Boolean?
)

@Schema(description = "Метка путевого листа.")
enum class WaybillLabel(val label: String) {
    @Schema(description = "Без отклонений.")
    NO_DEVIATIONS("Без отклонений."),

    @Schema(description = "Без рейсов.")
    NO_TRIP("Без рейсов."),

    @Schema(description = "Простой более 12ч.")
    DOWNTIME("Простой более 12ч."),

    @Schema(description = "С ремонтной точкой.")
    REPAIR_POINT("С ремонтной точкой."),

    @Schema(description = "Аномальный.")
    MVZ_MISMATCH("Аномальный."),

    @Schema(description = "Аномальный пробег.")
    BAD_MILEAGE("Аномальный пробег."),

    @Schema(description = "Долго открытый ПЛ.")
    LONG_TIME("Долго открытый ПЛ.")
}
