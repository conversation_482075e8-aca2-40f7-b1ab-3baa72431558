package com.x5.logistics.rest.controller

import com.x5.logistics.rest.dto.OffsetLimitPagedResponse
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtDictionaryTypeItem
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeDictionaryListReq
import com.x5.logistics.rest.dto.dictionary.vrt.VrtTypeItem
import com.x5.logistics.service.dictionary.vrt.VrtDictionaryService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("api/dictionary")
class VrtTypesDictionaryController(
    private val vrtDictionaryService: VrtDictionaryService
) {

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Получение записей словаря типов ВРТ.")
    @PostMapping("vrt/types")
    fun getVrtTypeDictionaryList(
        @RequestBody req: VrtTypeDictionaryListReq
    ): OffsetLimitPagedResponse<VrtTypeItem> {
        val items = vrtDictionaryService.getVrtTypeDictionaryItems(req)
        val count = vrtDictionaryService.countVrtTypeDictionaryItems(req)
        return OffsetLimitPagedResponse(
            count = count,
            offset = req.pageNumber * req.pageSize,
            items = items
        )
    }

    @Tag(name = "Словарь ВРТ")
    @Operation(summary = "Получение записей словаря типов ВРТ.")
    @PostMapping("vrt-types")
    fun getVrtDictionaryTypesList(
        @RequestBody req: VrtDictionaryListReq
    ): OffsetLimitPagedResponse<VrtDictionaryTypeItem> {
        req.setDefaultSort(isSubtypeDictionary = false)
        val items = vrtDictionaryService.getVrtDictionaryGroupList(req).map { it.toTypeItem() }
        val count = vrtDictionaryService.countVrtDictionaryGroupList(req)
        return OffsetLimitPagedResponse(
            count = count,
            offset = req.pageNumber * req.pageSize,
            items = items
        )
    }

}