package com.x5.logistics.rest.controller.carin

import com.x5.logistics.rest.dto.carin.desktopwidget.CarInDesktopWidgetRequest
import com.x5.logistics.rest.dto.carin.desktopwidget.CarInDesktopWidgetResponse
import com.x5.logistics.service.carin.CarInService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/carInDesktopWidget")
class CarInDesktopWidgetController(
    val service: CarInService
) {

    @Tag(name = "Car-in десктоп виджет.")
    @Operation(summary = "Получение данных для Car-in десктоп виджета.")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос на получение данных для Car-in десктоп виджета.",
        content = [
            Content(
                schema = Schema(
                    implementation = CarInDesktopWidgetRequest::class
                )
            )
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        schema = Schema(
                            implementation = CarInDesktopWidgetResponse::class
                        )
                    )
                ]
            )
        ]
    )
    @PostMapping
    suspend fun getWidget(@RequestBody req: CarInDesktopWidgetRequest): CarInDesktopWidgetResponse {

        return service.getCarInDesktopWidgetData(req)
    }
}