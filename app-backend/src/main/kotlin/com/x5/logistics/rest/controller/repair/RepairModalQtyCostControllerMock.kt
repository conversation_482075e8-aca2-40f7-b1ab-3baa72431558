package com.x5.logistics.rest.controller.repair

/**
 * JRAAVTO-35_Данные по ремонтам - модальный виджет распределения ВРТ и их сравнения в общем по МР
 * https://wiki.x5.ru/pages/viewpage.action?pageId=262323575
 * <AUTHOR> ( <EMAIL> )
 * Часть - 1 (левая часть модального виджета)
 */

//@RestController
//class RepairModalQtyCostControllerMock {
//
//    private val log = getLogger()
//
//    @Tag(name = "Ремонт")
//    @Operation(summary = "Данные по ремонтам - модальный виджет распределения ВРТ и их сравнения в общем по МР.")
//    @SwaggerReqBody(
//        description = "Запрос данных по ремонтам - модальный виджет распределения ВРТ и их сравнения в общем по МР.",
//        content = [Content(schema = Schema(implementation = RepairModalQtyCostReq::class))]
//    )
//    @ApiResponses(
//        value = [
//            ApiResponse(
//                responseCode = "200",
//                description = "Успешный ответ.",
//                content = [Content(schema = Schema(implementation = RepairModalQtyCostResp::class))]
//            )
//        ]
//    )
//    @PostMapping("api/repair/qtyCost")
//    fun getDetails(@RequestBody req: RepairModalQtyCostReq): RepairModalQtyCostResp {
//        log.debug("Get repair qty / price data for modal widget. Req=$req")
//        val qty = (RepairVrtColor.data).map {
//            RepairModalQtyCostData(
//                subtypeId = it.first,
//                subtypeName = it.second,
//                color = it.third,
//                value = Random.nextDouble(0.0, 10000.0).toBigDecimal()
//                    .setScale(2, RoundingMode.HALF_UP),
//                difference = Random.nextDouble(-1000.0, 1000.0).toBigDecimal()
//                    .setScale(2, RoundingMode.HALF_UP)
//            )
//        }
//        return RepairModalQtyCostResp(
//            label = "Выбрать все",
//            qty = qty,
//            cost = (RepairVrtColor.data).map {
//                RepairModalQtyCostData(
//                    subtypeId = it.first,
//                    subtypeName = it.second,
//                    color = it.third,
//                    value = Random.nextDouble(0.0, 1000000.0).toBigDecimal()
//                        .setScale(2, RoundingMode.HALF_UP),
//                    difference = Random.nextDouble(-1000.0, 1000.0).toBigDecimal()
//                        .setScale(2, RoundingMode.HALF_UP)
//                )
//            }
//        )
//    }
//}