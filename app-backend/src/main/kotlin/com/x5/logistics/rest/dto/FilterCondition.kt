package com.x5.logistics.rest.dto

import com.x5.logistics.util.PRECISION_THRESHOLD
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Тип фильтра.")
enum class FilterCondition(
    val sqlSyntax: String,
    val description: String
) {
    @Schema(description = "Содержит.")
    contain("ILIKE '%%' || %s || '%%'", "Содержит"),

    @Schema(description = "Не содержит.")
    notContain("NOT ILIKE '%%' || %s || '%%'", "Не содержит"),

    @Schema(description = "Больше или равно.")
    greater(">= (%s - $PRECISION_THRESHOLD)", "Больше или равно"),

    @Schema(description = "Меньше или равно.")
    less("<= (%s + $PRECISION_THRESHOLD)", "Меньше или равно"),

    @Schema(description = "Равно.")
    equal("- %s <= $PRECISION_THRESHOLD", "Равно"),

    @Schema(description = "Не равно.")
    notEqual("- %s > $PRECISION_THRESHOLD", "Не равно"),

    @Schema(description = "null или пустое значение.")
    nullOrEmpty("", "Все пустые"),

    @Schema(description = "Не null и не пустое значение")
    notNullOrEmpty("", "Все непустые")

}
