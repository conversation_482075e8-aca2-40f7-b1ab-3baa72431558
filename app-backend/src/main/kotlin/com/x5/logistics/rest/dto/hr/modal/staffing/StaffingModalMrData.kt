package com.x5.logistics.rest.dto.hr.modal.staffing

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.math.BigInteger

data class StaffingModalMrData(
    @Schema(description = "id макрорегиона.")
    val mrId: BigInteger?,

    @Schema(description = "Название МР.")
    val mrName: String?,

    @Schema(description = "% укомплектованности.")
    val driversPercent: BigDecimal?,

    @Schema(description = "Фактическое кол-во водителей.")
    val driversFact: BigDecimal?,

    @Schema(description = "Дефицит или профицит водителей в макрорегионе.")
    val driversDiff: BigDecimal?,

    @Schema(description = "Изменение фактической численности водителей.")
    val previousPeriodDriversDiff: BigDecimal?
)
