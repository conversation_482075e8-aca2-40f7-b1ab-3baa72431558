package com.x5.logistics.rest.dto.ktg

@Deprecated(message = "Not used anywhere!")
enum class KtgDetailsColumn(
    val sqlColumn: String,
    val grouping: Boolean,
    val dictionaryQuery: String? = null,
    val havingExpression: String? = null,
    val columnTitle: String
) {
    /*"Вид ТС": "string"*/
    vehicleGroup(
        "ts_group",
        true,
        "SELECT DISTINCT(ts_group) FROM types ORDER BY ts_group",
        null,
        "Вид ТС"
    ),

    /*"Тип ТС": "string"*/
    vehicleType(
        "ts_type",
        true,
        "SELECT DISTINCT(ts_type) FROM types ORDER BY ts_type",
        null,
        "Тип ТС"
    ),

    /*"АТП": "string"*/
    atp(
        "atp_name",
        true,
        "SELECT DISTINCT(name) FROM atp ORDER BY name",
        null,
        "АТП"
    ),

    /*"МВЗ": "string"*/
    mvz(
        "mvp_id",
        true,
        "SELECT DISTINCT(mvp_id) FROM ts_data ORDER BY mvp_id",
        null,
        "МВЗ"
    ),

    /*"Название МВЗ": "string"*/
    mvzName(
        "mvp_name",
        true,
        "SELECT DISTINCT(mvp_name) FROM ts_data ORDER BY mvp_name",
        null,
        "Название МВЗ"
    ),

    /*"Марка": "string"*/
    brand(
        "marka",
        true,
        "SELECT DISTINCT(marka) FROM ts_data ORDER BY marka",
        null,
        "Марка"
    ),

    /*"Модель": "string"*/
    model(
        "model",
        true,
        "SELECT DISTINCT(model) FROM ts_data ORDER BY model",
        null,
        "Модель"
    ),

    /*"Год выпуска": "string"*/
    year(
        "baujj",
        true,
        "SELECT DISTINCT(baujj) FROM ts_data ORDER BY baujj",
        null,
        "Год выпуска"
    ),

    /*"Тоннаж": "number"*/
    tonnage(
        "load_wgt",
        true,
        "SELECT DISTINCT(load_wgt) FROM ts_data ORDER BY load_wgt",
        null,
        "Тоннаж"
    ),

    /*"Гос. номер ТС": "string"*/
    licenseNum(
        "license_num",
        true,
        "SELECT DISTINCT(license_num) FROM ts_data ORDER BY license_num",
        null,
        "Гос. номер ТС"
    ),

    /*"Единица оборудования": "string"*/
    eqUnit(
        "equnr",
        true,
        "SELECT DISTINCT(equnr) FROM ts_data ORDER BY equnr",
        null,
        "Единица оборудования"
    ),

    /*"VIN номер": "string"*/
    vin(
        "fleet_num",
        true,
        "SELECT DISTINCT(fleet_num) FROM ts_data ORDER BY fleet_num",
        null,
        "VIN номер"
    ),

    /*"Признак установленного ГБО": "bool"*/
    gbo(
        "gbo",
        true,
        null,
        null,
        "Признак установленного ГБО"
    ),

    /*"Среднее за период": "number"*/
    avgKtg(
        "avg_kip",
        false,
        havingExpression = "coalesce(sum(t.hours) / sum(t.max_hours), 0.93)",
        columnTitle = "Среднее за период"
    )
}
