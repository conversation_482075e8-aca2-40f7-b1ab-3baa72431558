package com.x5.logistics.rest.controller

import com.x5.logistics.rest.dto.detailedreport.ExportDetailedReportRequest
import com.x5.logistics.service.DetailedReportService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URLEncoder
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("api/v1/detailedReport")
class DetailedReportController(
    private val detailedReportService: DetailedReportService
) {

    private val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm-ss")

    @Tag(name = "Детальный отчет")
    @Operation(summary = "Получение Excel детального отчета.")
    @PostMapping("export")
    fun exportDetailedReport(
        @RequestBody req: ExportDetailedReportRequest
    ): ResponseEntity<Resource> {
        val postfix = dateFormatter.format(LocalDateTime.now(ZoneId.of("Europe/Moscow")))
        val headers = HttpHeaders()
        headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        headers["Content-Disposition"] ="attachment; filename=${"Детальные_данные_$postfix.xlsx".urlEncoded}"
        return ResponseEntity.ok()
            .headers(headers)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(InputStreamResource(detailedReportService.getExcelReportBody(req.copy(to = req.to.plusDays(1)))))
    }

    private val String.urlEncoded: String
        get() = URLEncoder.encode(this, "UTF-8").replace("+", "%20")
}
