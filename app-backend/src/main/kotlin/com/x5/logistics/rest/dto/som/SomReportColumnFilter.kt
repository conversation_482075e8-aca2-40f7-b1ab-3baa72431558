package com.x5.logistics.rest.dto.som

import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Описание фильтра для детального отчета СОМ.")
data class SomReportColumnFilter(
    @Schema(description = "Колонка для фильтра.")
    val name: SomColumns,
    @Schema(description = "Тип фильтра.")
    val condition: FilterCondition,
    @Schema(description = "Список значений.")
    val value: List<Any>
)