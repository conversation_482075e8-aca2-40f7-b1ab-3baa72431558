package com.x5.logistics.rest.controller.repair

import com.x5.logistics.rest.dto.repair.modal.orders.RepairModalOrdersReq
import com.x5.logistics.rest.dto.repair.modal.orders.RepairModalOrdersResp
import com.x5.logistics.rest.dto.repair.modal.RepairSubtypesData
import com.x5.logistics.rest.dto.repair.modal.RepairTopOrdersData
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import java.math.RoundingMode
import kotlin.random.Random

/**
 * Mock for JRAAVTO-39_Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам
 * https://wiki.x5.ru/pages/viewpage.action?pageId=280896393
 * <AUTHOR> Belogolovsky ( <EMAIL> )
 */
//@RestController
@Deprecated(message = "Replaced by RepairModalOrdersController class")
class RepairModalOrdersControllerMock {

    private val log = getLogger()

    @Tag(name = "Ремонт")
    @Operation(summary = "Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам")
    @RequestBody(
        description = "Запрос данных по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам",
        content = [Content(schema = Schema(implementation = RepairModalOrdersReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairModalOrdersResp::class))]
            )
        ]
    )
    @PostMapping("api/repair/orders")
    fun getData(@RequestBody req: RepairModalOrdersReq): RepairModalOrdersResp {
        log.debug("Get repair orders data for modal widget. Req=$req")

        return RepairModalOrdersResp(
            subtypes = (1..10).map {
                val ownWorkShopAvgCost = Random.nextDouble(1.0, 1000.0).toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP)
                val ownWorkshopCount = Random.nextInt(1, 20).toBigInteger()
                val serviceStationAvgCost = Random.nextDouble(1.0, 1000.0).toBigDecimal()
                    .setScale(2, RoundingMode.HALF_UP)
                val serviceStationCount = Random.nextInt(1, 20).toBigInteger()
                RepairSubtypesData(
                    subtype = "Причина$it",
                    ownWorkshopCost = ownWorkShopAvgCost * ownWorkshopCount.toBigDecimal(),
                    ownWorkshopCount = ownWorkshopCount,
                    serviceStationCost = serviceStationAvgCost * serviceStationCount.toBigDecimal(),
                    serviceStationCount = serviceStationCount
                )
            },
            topOrders = (1..10).map {
                val letters = "ABCEHKMOPTXY"
                val numbers = "**********"
                RepairTopOrdersData(
                    orderNumber = Random(it).nextInt(100000, 999999999).toBigInteger(),
                    licenseNumber = arrayOf(
                        letters.random(),
                        (1..3).map { numbers.random() }.joinToString(""),
                        (1..2).map { letters.random() }.joinToString(""),
                        (1..2).map { numbers.random() }.joinToString("")
                    ).joinToString(""),
                    cost = Random(it).nextDouble(1.0, 100000.0).toBigDecimal()
                        .setScale(2, RoundingMode.HALF_UP)
                )
            }.sortedByDescending(RepairTopOrdersData::cost)

        )
    }
}