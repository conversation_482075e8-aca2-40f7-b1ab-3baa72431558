package com.x5.logistics.rest.dto.vehicle.details

import com.x5.logistics.rest.dto.ColumnFilter
import com.x5.logistics.rest.dto.FilterCondition
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Описание фильтра для детального отчета по транспортным средствам.")
data class VehicleDetailsColumnFilter(
    @Schema(description = "Колонка для фильтра.")
    override val name: VehicleDetailsColumn,
    @Schema(description = "Тип фильтра.")
    override val condition: FilterCondition,
    @Schema(description = "Список значений.")
    override val value: List<Any>
) : ColumnFilter(name, condition, value) {
    fun testNumeric(inputValue: BigDecimal?): Boolean {
        if (value.singleOrNull() == null) println("Value $value is null")
        val testValue = value.singleOrNull()?.let {
            when (it) {
                is Int -> it.toBigDecimal()
                is Double -> it.toBigDecimal()
                is Long -> it.toBigDecimal()
                else -> null
            }
        }
        return when (condition) {
            FilterCondition.contain -> false
            FilterCondition.notContain -> false
            FilterCondition.greater -> if (inputValue != null) inputValue >= testValue else false
            FilterCondition.less -> if (inputValue != null) inputValue <= testValue else false
            FilterCondition.equal -> if (inputValue != null && testValue != null) (inputValue - testValue).abs() < BigDecimal.valueOf(0.05) else false
            FilterCondition.notEqual -> if (inputValue != null && testValue != null) (inputValue - testValue).abs() >= BigDecimal.valueOf(0.05) else false
            FilterCondition.nullOrEmpty -> inputValue == null
            FilterCondition.notNullOrEmpty -> inputValue != null
        }
    }
}