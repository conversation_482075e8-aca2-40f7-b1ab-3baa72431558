package com.x5.logistics.rest.dto.dictionary.org.mvz

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero

data class OrgMvzDictionaryListRequest(
    @Schema(description = "Номер страницы.")
    @field:PositiveOrZero
    val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    @field:Positive
    val pageSize: Int,

    @Schema(description = "Список сортировок.")
    val sort: List<OrgMvzDictionarySortOrder>,

    @Schema(description = "Список фильтров.")
    val filters: List<OrgMvzDictionaryColumnFilter>
)