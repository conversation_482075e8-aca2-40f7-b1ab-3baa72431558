package com.x5.logistics.rest.dto.dictionary.rr

import com.x5.logistics.data.dictionary.rr.Month
import java.math.BigDecimal

data class RrDictionaryDetailedGroupDto(
    val year: Int,
    val atpId: Long,
    val atpName: String,
    val tonnage: BigDecimal,
    val deletable: Boolean,
    val updatedByVRT: List<UpdatedByVRT>,
    val details: List<RrDictionaryGroupMonthDetailsDto>
)

data class RrDictionaryGroupMonthDetailsDto(
    val month: Month,
    val sumRate: Double,
    val vrt: List<VrtDetails>
)

data class VrtDetails(
    val id: String,
    val name: String?,
    val rate: Double
)

data class UpdatedByVRT(
    val vrtId: String,
    val updatedBy: String
)
