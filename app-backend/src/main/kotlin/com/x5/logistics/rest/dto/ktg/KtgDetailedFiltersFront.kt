package com.x5.logistics.rest.dto.ktg

import com.x5.logistics.rest.util.FilterGroup
import com.x5.logistics.rest.util.FilterGroup.*
import com.x5.logistics.rest.util.FilterRespEnum

enum class KtgDetailedFiltersFront(
    override val label: String,
    override val group: FilterGroup,
    override val filterable: Boolean = true
) : FilterRespEnum {

    vehicleDays(
        group = number,
        label = "Кол-во строк"
    ),
    retailNetwork(
        group = stringSearch,
        label = "Торговая сеть АТП",
        filterable = false
    ),
    atpType(
        group = stringSearch,
        label = "Вид деятельности АТП",
        filterable = false
    ),
    terName(
        group = stringSearch,
        label = "Территория",
        filterable = false
    ),
    mrName(
        group = stringSearch,
        label = "Макрорегион",
        filterable = false
    ),
    atp(
        group = stringSearch,
        label = "АТП",
        filterable = false
    ),
    mvz(
        group = stringSearch,
        label = "МВЗ",
        filterable = false
    ),
    mvzType(
        group = stringSearch,
        label = "Тип МВЗ"
    ),
    vehicleType(
        group = string,
        label = "Тип ТС"
    ),
    vehicleGroup(
        group = string,
        label = "Вид ТС"
    ),
    brand(
        group = string,
        label = "Марка ТС"
    ),
    model(
        group = string,
        label = "Модель ТС"
    ),
    tonnage(
        group = number,
        label = "Тоннаж ТС"
    ),
    tonnageWhole (
        group = number,
        label = "Тоннаж общий"
    ),
    compartWhole (
        group = number,
        label = "Паллетовместимость общая"
    ),
    compart (
        group = number,
        label = "Паллетовместимость ТС"
    ),
    trailerCompart (
        group = number,
        label = "Паллетовместимость прицепа"
    ),
    mileage(
        group = number,
        label = "Пробег ТС на конец периода"
    ),
    licenseNum(
        group = stringSearch,
        label = "Гос. номер ТС"
    ),
    eqUnit(
        group = stringSearch,
        label = "Единица оборудования ТС"
    ),
    vin(
        group = stringSearch,
        label = "VIN номер ТС"
    ),
    rcSubmitting(
        group = stringSearch,
        label = "РЦ подачи"
    ),
    rcSubmittingCode(
        group = string,
        label = "Код РЦ подачи"
    ),
    dateSubmitting(
        group = date,
        label = "Дата подачи в ТГ"
    ),
    commissioningDate(
        group = number,
        label = "Год ввода в эксплуатацию ТС"
    ),
    trailerType(
        group = string,
        label = "Тип прицепа"
    ),
    trailerGroup(
        group = string,
        label = "Вид прицепа"
    ),
    trailerBrand(
        group = string,
        label = "Марка прицепа"
    ),
    trailerModel(
        group = string,
        label = "Модель прицепа"
    ),
    trailerTonnage(
        group = number,
        label = "Тоннаж прицепа"
    ),
    trailerLicenseNum(
        group = stringSearch,
        label = "Гос. номер прицепа"
    ),
    trailerEqUnit(
        group = stringSearch,
        label = "Единица оборудования прицепа"
    ),
    trailerVin(
        group = stringSearch,
        label = "VIN номер прицепа"
    ),
    trailerCommissioningDate(
        group = number,
        label = "Год ввода в эксплуатацию прицепа"
    ),
    status(
        group = string,
        label = "Статус"
    ),
    reason(
        group = stringSearch,
        label = "Причина"
    ),
    waybills(
        group = boolean,
        label = "ПЛ"
    ),
    waybillsRepair(
        group = boolean,
        label = "Рем. ПЛ"
    ),
    ktg(
        group = number,
        label = "КТГ, %"
    ),
    rg(
        group = number,
        label = "РГ, %"
    )
}
