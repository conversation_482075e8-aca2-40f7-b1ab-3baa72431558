package com.x5.logistics.rest.dto.charting

open class ChartCompositeAggregateRequest(
    open val name: String,
    open val settings: ChartCompositeAggregateSettingsDto,
)

data class ChartCompositeAggregateCreateRequest(
    override val name: String,
    override val settings: ChartCompositeAggregateSettingsDto,
) : ChartCompositeAggregateRequest(
    name = name,
    settings = settings,
)

data class ChartCompositeAggregateUpdateRequest(
    val id: Long,
    override val name: String,
    override val settings: ChartCompositeAggregateSettingsDto,
) : ChartCompositeAggregateRequest(
    name = name,
    settings = settings,
)