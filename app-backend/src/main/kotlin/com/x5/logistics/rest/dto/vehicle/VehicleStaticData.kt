package com.x5.logistics.rest.dto.vehicle

import java.math.BigDecimal

data class VehicleStaticData(
    val mvzId: String?,
    val equnr: Long?,
    val license: String?,
    val model: String?,
    val tsGroup: String?,
    val tsType: String?,
    val marka: String?,
    val gbo: Boolean?,
    val mvzName: String?,
    val mvzType: String?,
    val mrName: String?,
    val atpName: String?,
    val year: String?, //baujj
    val loadWgt: Float?,
    val fleetNum: String?, // vin
    val rcSubmitting: String?,
    val commissioningYear: Int?,
    val mileage: BigDecimal?,
    val status: String?,
    val reason: String?,
    val rcSubmittingCode: String?,
)
