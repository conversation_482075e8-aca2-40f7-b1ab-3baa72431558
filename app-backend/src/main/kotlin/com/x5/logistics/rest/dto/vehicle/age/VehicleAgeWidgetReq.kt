package com.x5.logistics.rest.dto.vehicle.age

import com.fasterxml.jackson.annotation.JsonFormat
import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

/**
 * JRAAVTO-27_Возраст автопарка - виджет рабочего стола
 * https://wiki.x5.ru/pages/viewpage.action?pageId=377572365
 * <AUTHOR> ( <EMAIL> )
 */

@Schema(description = "Запрос модального виджета по возрасту автопарка.")
data class VehicleAgeWidgetReq(
    @Schema(description = "Дата.")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val day: LocalDate,

    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
)
