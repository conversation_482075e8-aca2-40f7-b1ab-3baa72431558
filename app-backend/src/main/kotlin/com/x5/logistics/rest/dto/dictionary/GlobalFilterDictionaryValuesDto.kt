package com.x5.logistics.rest.dto.dictionary

import com.fasterxml.jackson.annotation.JsonPropertyOrder
import com.x5.logistics.rest.dto.ParentLabeledValue
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Словарь значений для глобальных фильтров")
@JsonPropertyOrder("territory", "mr", "atp", "mvz", "retailNetwork", "atpType", "mvzType")
data class GlobalFilterDictionaryValuesDto(
    @Schema(description = "Территория")
    val territory: ParentLabeledValue,
    @Schema(description = "Макрорегионы")
    val mr: Parent<PERSON>abeledValue,
    @Schema(description = "АТП")
    val atp: ParentLabeledValue,
    @Schema(description = "МВЗ")
    val mvz: ParentLabeledValue,
    @Schema(description = "Торговая сеть АТП")
    val retailNetwork: ParentLabeledValue,
    @Schema(description = "Вид транспортировки")
    val atpType: ParentLabeledValue,
    @Schema(description = "Тип МВЗ")
    val mvzType: ParentLabeledValue
)
