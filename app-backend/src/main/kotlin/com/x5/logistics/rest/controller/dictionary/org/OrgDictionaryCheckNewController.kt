package com.x5.logistics.rest.controller.dictionary.org

import com.x5.logistics.rest.dto.dictionary.org.OrgDictionaryCheckNewResp
import com.x5.logistics.service.dictionary.org.OrgDictionaryCheckNewService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/dictionary/check-new/org")
class OrgDictionaryCheckNewController(
    private val service: OrgDictionaryCheckNewService
) {
    @Tag(name = "Словари")
    @Operation(summary = "Список подразделений, которые требуют создания новых связей")
    @GetMapping
    suspend fun getNew(): OrgDictionaryCheckNewResp {
        return service.checkNewOrg()
    }
}
