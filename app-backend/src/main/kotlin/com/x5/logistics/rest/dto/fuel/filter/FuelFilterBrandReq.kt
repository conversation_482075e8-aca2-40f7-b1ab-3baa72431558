package com.x5.logistics.rest.dto.fuel.filter

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос полного дерева марок ТС.")
data class FuelFilterBrandReq(
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,
    @Schema(description = "Дата конца периода.")
    val to: LocalDate,
    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,
)
