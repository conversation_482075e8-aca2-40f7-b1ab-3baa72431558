package com.x5.logistics.rest.controller.repair

import com.x5.logistics.repository.repair.RepairModalOrdersRepository
import com.x5.logistics.rest.dto.repair.modal.expenses.RepairModalExpensesResp
import com.x5.logistics.rest.dto.repair.modal.expenses.RepairModalMinMaxExpensesResp
import com.x5.logistics.rest.dto.repair.modal.orders.RepairModalOrdersReq
import com.x5.logistics.rest.dto.repair.modal.RepairModalTopOrdersResp
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

/**
 * Controller for JRAAVTO-39_Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам
 * https://wiki.x5.ru/pages/viewpage.action?pageId=280896393
 * <AUTHOR> Belogolovsky ( <EMAIL> )
 */
@RestController
class RepairModalOrdersController(
    val repo: RepairModalOrdersRepository
) {
    private val log = getLogger()

    @Tag(name = "Ремонт")
    @Operation(summary = "Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам")
    @SwaggerReqBody(
        description = "Запрос данных по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам",
        content = [Content(schema = Schema(implementation = RepairModalOrdersReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairModalExpensesResp::class))]
            )
        ]
    )
    @PostMapping("api/repair/orders")
    fun getOrders(@RequestBody req: RepairModalOrdersReq): RepairModalExpensesResp {
        log.debug("Get repair orders data for modal widget. Req=$req")
        return repo.getDistribution(req)
    }

    @Tag(name = "Ремонт")
    @Operation(summary = "Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам. Минимальные и максимальные затраты")
    @SwaggerReqBody(
        description = "Запрос данных по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам",
        content = [Content(schema = Schema(implementation = RepairModalOrdersReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairModalMinMaxExpensesResp::class))]
            )
        ]
    )
    @PostMapping("api/repair/orders/minMax")
    fun getMinMax(@RequestBody req: RepairModalOrdersReq): RepairModalMinMaxExpensesResp {
        log.debug("Get min and max repair expenses for modal widget. Req=$req")
        return repo.getMinMaxExpenses(req)
    }

    @Tag(name = "Ремонт")
    @Operation(summary = "Данные по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам. Топ заказов")
    @SwaggerReqBody(
        description = "Запрос данных по ремонтам - модальный виджет распределения затрат по ВРТ и рем. зонам",
        content = [Content(schema = Schema(implementation = RepairModalOrdersReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = RepairModalTopOrdersResp::class))]
            )
        ]
    )
    @PostMapping("api/repair/orders/top")
    fun getTopOrders(@RequestBody req: RepairModalOrdersReq): RepairModalTopOrdersResp {
        return repo.getTopOrders(req)
    }

}