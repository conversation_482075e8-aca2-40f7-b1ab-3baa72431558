package com.x5.logistics.rest.dto.fuel.graph

import com.x5.logistics.rest.dto.fuel.details.FuelDetailsColumnFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Запрос графиков по топливу.")
data class FuelGraphReq(
    @Schema(description = "Список МВЗ.")
    val mvz: List<String>,
    @Schema(description = "Дата конца периода.")
    val to: LocalDate,
    @Schema(description = "Список фильтров.")
    val filters: List<FuelDetailsColumnFilter>,
)
