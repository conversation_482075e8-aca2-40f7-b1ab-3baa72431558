package com.x5.logistics.rest.dto

import com.x5.logistics.util.PRECISION_THRESHOLD
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Тип фильтра для отчетов.")
enum class ReportFilterCondition(val sqlSyntax: String) {
    @Schema(description = "Содержит.")
    contain("ILIKE %s"),

    @Schema(description = "Не содержит.")
    notContain("NOT ILIKE %s"),

    @Schema(description = "Больше или равно.")
    greater(">= (%s - $PRECISION_THRESHOLD)"),

    @Schema(description = "Меньше или равно.")
    less("<= (%s + $PRECISION_THRESHOLD)"),

    @Schema(description = "Равно.")
    equal("- %s <= $PRECISION_THRESHOLD"),

    @Schema(description = "Не равно.")
    notEqual("- %s > $PRECISION_THRESHOLD"),

    @Schema(description = "null или пустое значене.")
    nullOrEmpty(""),

    @Schema(description = "Не null и не пустое значение")
    notNullOrEmpty("")
}

