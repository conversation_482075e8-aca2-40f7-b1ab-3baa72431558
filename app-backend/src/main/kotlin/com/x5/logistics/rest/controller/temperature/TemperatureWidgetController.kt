package com.x5.logistics.rest.controller.temperature

import com.x5.logistics.rest.dto.temperature.TemperatureWidgetReq
import com.x5.logistics.rest.dto.temperature.TemperatureWidgetResp
import com.x5.logistics.service.temperature.TemperatureWidgetService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("/api/temperatureDesktopWidget")
class TemperatureWidgetController(
    val service: TemperatureWidgetService
) {
    private val log = getLogger()

    @Tag(name = "Виджет температурный режим в рейсе")
    @Operation(summary = "Получение содержимого виджета")
    @SwaggerReqBody(
        description = "Запрос на виджет температурный режим в рейсе",
        content = [Content(schema = Schema(implementation = TemperatureWidgetReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ",
                content = [Content(schema = Schema(implementation = TemperatureWidgetResp::class))]
            )
        ]
    )
    @PostMapping
    suspend fun getWidgetData(@RequestBody req: TemperatureWidgetReq): TemperatureWidgetResp {
        log.debug("TemperatureWidget request: {}", req)

        return service.getWidgetData(req)
    }
}