package com.x5.logistics.rest.dto.hr.dictionaries

import com.x5.logistics.rest.dto.ExposedCommonRequest
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Запрос экспорта справочника плановой численности водителей")
data class DriversPerVehicleDictionaryExportRequest(
    @Schema(description = "Номер страницы.")
    override val pageNumber: Int,

    @Schema(description = "Размер страницы.")
    override val pageSize: Int,

    @Schema(description = "Список сортировок.")
    override val sort: List<DriversPerVehicleExportSortOrder>,

    @Schema(description = "Список фильтров.")
    override val filters: List<DriversPerVehicleExportColumnFilter>,

    @Schema(description = "Год")
    val year: Int

) : ExposedCommonRequest
