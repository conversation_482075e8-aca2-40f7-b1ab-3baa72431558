package com.x5.logistics.rest.dto.repair.workspace

import com.x5.logistics.rest.dto.GeoFilter
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate

@Schema(description = "Запрос данных по ремонтам - виджет рабочего стола c затратами и кол-вом больших ремонтов.")
data class RepairWidgetExpensiveRepairReq(
    @Schema(description = "Дата начала периода.")
    val from: LocalDate,

    @Schema(description = "Дата конца периода.")
    val to: LocalDate,

    @Schema(description = "Глобальные фильтры.")
    val geoFilter: GeoFilter,

    @Schema(description = "Пороговое значение суммы одного заказа.")
    val filterSum: BigDecimal
)
