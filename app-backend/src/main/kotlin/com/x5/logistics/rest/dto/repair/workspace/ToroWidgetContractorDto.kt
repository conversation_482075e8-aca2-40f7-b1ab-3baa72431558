package com.x5.logistics.rest.dto.repair.workspace

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Исполнитель ремонта.")
data class ToroWidgetContractorDto(
    @Schema(description = "По исполнителю средняя стоимость заказа/средняя стоимость НЧ по услуге, средняя стоимость деталей.")
    val avgCost: Double,

    @Schema(description = "Наименование исполнителя по СТО/Ремзоны.")
    val contractorName: String,

    @Schema(description = "Итоговая стоимость по контрагенту (Ремонта/Деталей/Ремонта + деталей). ")
    val totalCost: Double,

    @Schema(description = "ourWorkshop - Ремзона, extWorkshop - СТО. ")
    val contractorType: ToroWidgetContractorType,
)