package com.x5.logistics.rest.controller.fuel

import com.x5.logistics.repository.fuel.FuelWidgetGasAndDieselRepo
import com.x5.logistics.repository.fuel.FuelWidgetType
import com.x5.logistics.rest.dto.fuel.widget.FuelWidgetGasReq
import com.x5.logistics.rest.dto.fuel.widget.FuelWidgetGasResp
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController

@RestController
class FuelWidgetGasController(
    val repo: FuelWidgetGasAndDieselRepo
) {
    private val log = getLogger()

    @Tag(name = "Топливо")
    @Operation(summary = "Запрос данных виджета ГАЗ.")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Запрос данных виджета ГАЗ.",
        content = [
            Content(
                schema = Schema(
                    implementation = FuelWidgetGasReq::class
                )
            )
        ]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [
                    Content(
                        schema = Schema(
                            implementation = FuelWidgetGasResp::class
                        )
                    )
                ]
            )
        ]
    )
    @PostMapping("api/gas/qty")
    fun getDetails(@RequestBody req: FuelWidgetGasReq): FuelWidgetGasResp {
        log.debug(
            "Get gas qty  data. [mvz={}, from={}, to={}]",
            req.geoFilter, req.from, req.to
        )

        return repo.getFuelWidgetData(
            req.from,
            req.to,
            req.geoFilter,
            FuelWidgetType.GAS
        ).let {
            FuelWidgetGasResp(
                factConsumptionOn100GasLiter = it.factConsumptionOn100Liter,
                planConsumptionOn100GasLiter = it.planConsumptionOn100Liter,
                factConsumptionGasRubKm = it.factConsumptionRubKm,
                overspendingEconomyGasRubKm = it.planConsumptionRubKm,
                overspendingEconomyGasRub = it.overspendingEconomyRub,
                isEmpty = it.isEmpty
            )
        }
    }
}
