package com.x5.logistics.rest.dto.fuel.graph

import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Ответ по графикам по топливу.")
data class FuelGraphResp(
    val v: Any? = null,
)


//    @Schema(description = "Список дат в ISO (Графики 1-4).")
//    val dates: List<LocalDate>,
//    @Schema(description = "Руб/км ДТ (График 1).")
//    val costsDiesel: List<BigDecimal?>,
//    @Schema(description = "Руб/км ГАЗ (График 1).")
//    val costsGas: List<BigDecimal?>,
//    @Schema(description = "Пройденные км (График 1).")
//    val costsRunKm: List<BigDecimal?>,
//    @Schema(description = "Отклонение от нормы ДТ, % (График 3).")
//    val deviationsDiesel: List<BigDecimal?>,
//    @Schema(description = "Отклонение от нормы Газа, % (График 3).")
//    val deviationsGas: List<BigDecimal?>,
//    @Schema(description = "Эффект по нарастающим итогам (График 2).")
//    val effectsTotals: List<BigDecimal?>,
//    @Schema(description = "Эффект по дням (График 2).")
//    val effectsDays: List<BigDecimal?>,
//    @Schema(description = "Коэф. замещ. (График 4).")
//    val coefficientValues: List<BigDecimal?>,
//    @Schema(description = "% снижения (График 4).")
//    val coefficientPercents: List<BigDecimal?>,
//    @Schema(description = "Кол-во ТС с ГБО (График 4).")
//    val coefficientGbo: List<BigDecimal?>