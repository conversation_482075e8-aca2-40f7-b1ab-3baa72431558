package com.x5.logistics.rest.controller.fuel

import com.x5.logistics.rest.dto.fuel.FuelModalWidgetReq
import com.x5.logistics.rest.dto.fuel.FuelModalWidgetResp
import com.x5.logistics.rest.dto.fuel.percent.FuelPercentModalReq
import com.x5.logistics.rest.dto.fuel.percent.FuelPercentModalResp
import com.x5.logistics.service.FuelEffectService
import com.x5.logistics.util.getLogger
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import io.swagger.v3.oas.annotations.parameters.RequestBody as SwaggerReqBody

@RestController
@RequestMapping("api/fuel")
class FuelModalController(
    val service: FuelEffectService
) {
    private val log = getLogger()

    @Tag(name = "Топливо")
    @Operation(summary = "Запрос по модальному виджету топлива для заданного периода дат и МВЗ.")
    @SwaggerReqBody(
        description = "Запрос по модальному виджету топлива для заданного периода дат и МВЗ.",
        content = [Content(schema = Schema(implementation = FuelModalWidgetReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = FuelModalWidgetResp::class))]
            )
        ]
    )
    @PostMapping("/mileage")
    suspend fun getModalWidget(@RequestBody req: FuelModalWidgetReq): FuelModalWidgetResp {
        log.debug("Fuel modal data request: {}", req)

        return service.getFuelVehicleAndDriverData(req)
    }

    @Tag(name = "Топливо")
    @Operation(summary = "Запрос топливной эффективности для заданного периода дат и МВЗ.")
    @SwaggerReqBody(
        description = "Запрос топливной эффективности для заданного периода дат и МВЗ.",
        content = [Content(schema = Schema(implementation = FuelPercentModalReq::class))]
    )
    @ApiResponses(
        value = [
            ApiResponse(
                responseCode = "200",
                description = "Успешный ответ.",
                content = [Content(schema = Schema(implementation = FuelPercentModalResp::class))]
            )
        ]
    )
    @PostMapping("/effect")
    fun getFuelEffectiveness(@RequestBody req: FuelPercentModalReq): FuelPercentModalResp {
        log.debug("Fuel effectiveness request: {}", req)

        return service.getFuelEffect(req)
    }

}