package com.x5.logistics.rest.dto.dictionary

import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Schema(description = "Информация о последнем изменении справочника")
data class DictionaryInfoDto(
    @Schema(description = "Справочник")
    val name: String,
    @Schema(description = "Автор редактирования")
    val updatedBy: String?,
    @Schema(description = "Дата редактирования")
    val updatedAt: LocalDateTime?
)
