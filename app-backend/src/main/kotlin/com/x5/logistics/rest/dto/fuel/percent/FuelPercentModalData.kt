package com.x5.logistics.rest.dto.fuel.percent

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(description = "Данные по топливу для мокапа.")
data class FuelPercentModalData(
    @Schema(description = "Марка.")
    val brand: String?,
    @Schema(description = "Модель.")
    val model: String?,
    @Schema(description = "Тоннаж.")
    val tonnage: Float,
    @Schema(description = "Количество ТС.")
    val countVehicle: Long,
    @Schema(description = "Эффект.")
    val effect: Float,
    @Schema(description = "Руб/км.")
    val moneyKm: Float,
    @Schema(description = "Признак установленного ГБО.")
    val gbo: Boolean?
)