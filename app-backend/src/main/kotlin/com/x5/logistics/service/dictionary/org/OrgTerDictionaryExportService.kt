package com.x5.logistics.service.dictionary.org

import com.x5.logistics.rest.dto.dictionary.org.ter.OrgTerDictionaryColumn
import com.x5.logistics.rest.dto.dictionary.org.ter.OrgTerDictionaryItemDto
import com.x5.logistics.rest.dto.dictionary.org.ter.OrgTerDictionaryListRequest
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import kotlinx.coroutines.runBlocking
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.springframework.stereotype.Service
import java.io.InputStream

@Service
class OrgTerDictionaryExportService(
    private val service: OrgTerDictionaryService
) {
    suspend fun export(req: OrgTerDictionaryListRequest): InputStream {
        val columns = OrgTerDictionaryColumn.entries
        var page: List<OrgTerDictionaryItemDto>
        val size = service.getList(req.copy(pageNumber = 0, pageSize = 1)).count.toInt()
        val out = streamWorkbook {
            val generalStyle = style { dataFormat = wb.createDataFormat().getFormat("General") }
            val floatStyle = style { dataFormat = wb.createDataFormat().getFormat("0.00;;0;") }
            val dateStyle = style { dataFormat = wb.createDataFormat().getFormat("dd.MM.yyyy") }
            val styles = mapOf<String, CellStyle>(
                "general" to generalStyle,
                "float" to floatStyle,
                "date" to dateStyle
            )

            sheet("Справочник территорий") {
                style {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                }
                header {
                    columns.forEach { column ->
                        head(column.columnTitle)
                    }
                }
                for (pageNumber in 0..(size / EXPORT_BATCH_SIZE)) {
                    runBlocking {
                        page = service.getList(req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE)).items
                    }
                    page.forEach { item ->
                        row {
                            columns.forEach {
                                cellByColumnType(this, styles, it, item)
                            }
                        }
                    }
                }
                (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
            }.autosize(columns.size)
        }.toInputStream()

        return out
    }

    private fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: OrgTerDictionaryColumn,
        item: OrgTerDictionaryItemDto
    ) = when(column) {
        OrgTerDictionaryColumn.TerName -> rb.cell(item.terName).cellStyle = styles["general"]
        OrgTerDictionaryColumn.UpdatedBy -> rb.cell(item.updatedBy).cellStyle = styles["general"]
    }


}