package com.x5.logistics.service.dictionary.hr.places

import com.x5.logistics.data.dictionary.hrplaces.HrPlaceDao
import com.x5.logistics.data.dictionary.hrplaces.HrPlaceTplaceLogEntityDao
import com.x5.logistics.data.dictionary.hrplaces.HrPlaceTplaceLogViewEntityDao
import com.x5.logistics.data.dictionary.hrplaces.HrTotalPlaceDao
import com.x5.logistics.repository.dictionary.hrplaces.HrPlaceTplaceLogEntityRepo
import com.x5.logistics.repository.dictionary.hrplaces.HrPlaceTplaceLogViewRepo
import com.x5.logistics.repository.dictionary.hrplaces.HrPlacesExposedRepo
import com.x5.logistics.repository.dictionary.hrplaces.HrPlacesRepo
import com.x5.logistics.repository.dictionary.hrplaces.HrTotalPlacesRepo
import com.x5.logistics.repository.refreshOrgUnit
import com.x5.logistics.rest.dto.PagedResponse
import com.x5.logistics.rest.dto.SortItem
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlaceDto
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlaceLogDto
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesCreateReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesDeleteReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesDictRes
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesEditReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogGroupDto
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogReq
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogReqFilter
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogReqFilterField
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesLogReqSortField
import com.x5.logistics.rest.dto.dictionary.hrplaces.HrPlacesPlaceDto
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.workbook
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.JoinType
import jakarta.persistence.criteria.Order
import jakarta.persistence.criteria.Predicate
import jakarta.persistence.criteria.Root
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.springframework.stereotype.Service
import java.io.InputStream
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId

private val infinityDate = LocalDate.parse("9999-12-31")

@Service
class HrPlacesDictionaryService(
    private val hrPlaceTplaceLogViewRepo: HrPlaceTplaceLogViewRepo,
    private val hrPlaceTplaceLogEntityRepo: HrPlaceTplaceLogEntityRepo,
    private val hrPlacesRepo: HrPlacesRepo,
    private val hrPlacesExposedRepo: HrPlacesExposedRepo,
    private val hrTotalPlacesRepo: HrTotalPlacesRepo
) {
    fun getHrPlacesLog(req: HrPlacesLogReq): PagedResponse<HrPlacesLogGroupDto> {
        val items = hrPlaceTplaceLogViewRepo.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(HrPlaceTplaceLogViewEntityDao::class.java)
            val root = query.from(HrPlaceTplaceLogViewEntityDao::class.java)
            root.join<HrPlaceTplaceLogViewEntityDao, HrTotalPlaceDao>("totalPlace", JoinType.LEFT)
            root.join<HrPlaceTplaceLogViewEntityDao, HrPlaceDao>("place", JoinType.LEFT)
            query.select(root)
            query.where(req.filters.getPredicates(builder, root))
            val sort = req.sort.ifEmpty {
                listOf(
                    SortItem(HrPlacesLogReqSortField.TOTAL_PLACE_NAME),
                    SortItem(HrPlacesLogReqSortField.PLACE_NAME)
                )
            }
            val order = listOf(criteriaBuilder.asc(root.get<Int>("origin"))) + sort.map { it.getOrder(builder, root) }
            query.orderBy(order)
            val typedQuery = createQuery(query)
            typedQuery.firstResult = req.pageNumber * req.pageSize
            typedQuery.maxResults = req.pageSize
            typedQuery.resultList
        }.map { it.toDto() }
        val count = hrPlaceTplaceLogViewRepo.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(Long::class.javaObjectType)
            val root = query.from(HrPlaceTplaceLogViewEntityDao::class.java)
            query.select(builder.count(root))
            query.where(req.filters.getPredicates(builder, root))
            val typedQuery = createQuery(query)
            typedQuery.singleResult.toInt()
        }
        return PagedResponse(
            count = count,
            pageNumber = req.pageNumber,
            pageSize = req.pageSize,
            items = items
        )
    }

    private val columns = HrPlaceLogColumn.entries
    fun export(req: HrPlacesLogReq): InputStream {
        val count = getHrPlacesLog(req.copy(pageSize = 1)).count
        val data = getHrPlacesLog(req.copy(pageNumber = 0, pageSize = count)).items
        val out = workbook {
            val headerStyle = style {
                alignment = HorizontalAlignment.CENTER
                verticalAlignment = VerticalAlignment.CENTER
                setFont(wb.createFont().apply {
                    bold = true
                })
            }
            val generalStyle = style { dataFormat = wb.createDataFormat().getFormat("General") }
            val floatStyle = style { dataFormat = wb.createDataFormat().getFormat("0.00;;0;") }
            val dateStyle = style { dataFormat = wb.createDataFormat().getFormat("dd.mm.yyyy") }
            val styles = mapOf(
                "general" to generalStyle,
                "float" to floatStyle,
                "date" to dateStyle,
            )

            sheet("Площадки и итоговые подразделения") {
                header {
                    currStyle = headerStyle
                    columns.forEach { head(it.title) }
                }
                data.forEach { item ->
                    currStyle = generalStyle
                    row {
                        columns.forEach { cellByColumnType(this, styles, it, item) }
                    }
                }
                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(count)
                }
            }.autosize(columns.size)
        }.toInputStream()
        return out
    }

    private fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: HrPlaceLogColumn,
        item: HrPlacesLogGroupDto
    ) = when (column) {
        HrPlaceLogColumn.PlaceName -> rb.cell(item.place.name).cellStyle = styles["general"]
        HrPlaceLogColumn.TotalPlaceName -> rb.cell(item.totalPlace?.name).cellStyle = styles["general"]
        HrPlaceLogColumn.StartDate -> rb.cell(item.startDate).cellStyle = styles["date"]
        HrPlaceLogColumn.EndDate -> rb.cell(item.endDate).cellStyle = styles["date"]
        HrPlaceLogColumn.UpdatedBy -> rb.cell(item.updatedBy).cellStyle = styles["general"]
    }

    fun createHrPlacesEntity(req: HrPlacesCreateReq, author: String): HrPlacesLogGroupDto? {
        val place = hrPlacesRepo.findById(req.placeId).orElseThrow {
            WrongRequestDataException("place with id ${req.placeId} is not found.")
        }
        if (place.deleted) {
            throw WrongRequestDataException("place with id ${place.id} is deleted.")
        }
        val totalPlace = hrTotalPlacesRepo.findById(req.totalPlaceId).orElseThrow {
            WrongRequestDataException("total place with id ${req.totalPlaceId} is not found.")
        }
        if (totalPlace.deleted) {
            throw WrongRequestDataException("total place with id ${totalPlace.id} is deleted.")
        }
        hrPlaceTplaceLogEntityRepo.findByPlaceAndStartDateAndDeletedFalse(place, req.startDate).ifPresent {
            throw WrongRequestDataException("record with total_place_id ${req.totalPlaceId} and start date ${req.startDate} is already exist.")
        }
        val entity = HrPlaceTplaceLogEntityDao(
            place = place,
            totalPlace = totalPlace,
            startDate = req.startDate,
            createdAt = LocalDateTime.now(),
            createdBy = author,
            updatedAt = LocalDateTime.now(),
            updatedBy = author,
            deleted = false,
        )
        val saved = hrPlaceTplaceLogEntityRepo.save(entity)
        return getGroupDto(saved.place.id)
    }

    fun editHrPlacesEntity(req: HrPlacesEditReq, author: String): HrPlacesLogGroupDto {
        val entity = hrPlaceTplaceLogEntityRepo.findById(req.id).orElseThrow {
            WrongRequestDataException("id ${req.id} is not found.")
        }
        val totalPlace = hrTotalPlacesRepo.findById(req.totalPlaceId).orElseThrow {
            WrongRequestDataException("total place with id ${req.totalPlaceId} is not found.")
        }
        if (totalPlace.deleted) {
            throw WrongRequestDataException("total place with id ${totalPlace.id} is deleted.")
        }
        hrPlaceTplaceLogEntityRepo.findByPlaceAndStartDateAndDeletedFalse(entity.place, req.startDate).ifPresent {
            if (it.id != req.id) {
                throw WrongRequestDataException("record with total_place_id ${req.totalPlaceId} and start date ${req.startDate} is already exist.")
            }
        }
        entity.totalPlace = totalPlace
        entity.startDate = req.startDate
        entity.updatedAt = LocalDateTime.now()
        entity.updatedBy = author
        hrPlaceTplaceLogEntityRepo.save(entity)
        return getGroupDto(entity.place.id)!!
    }

    fun deleteHrPlacesEntity(req: HrPlacesDeleteReq, author: String): HrPlacesLogGroupDto? {
        val entity = hrPlaceTplaceLogEntityRepo.findById(req.id).orElseThrow {
            WrongRequestDataException("dictionary entity with id ${req.id} is not found")
        }
        if (!entity.deleted) {
            hrPlaceTplaceLogEntityRepo.save(entity.copy(
                updatedAt = getCurrentDate(),
                updatedBy = author,
                deleted = true
            ))
        }
        return getGroupDto(entity.place.id)
    }

    fun getHrPlacesDict(): HrPlacesDictRes {
        val places = hrPlacesRepo.findAll().asSequence()
            .filter { !it.deleted }
            .map {
                HrPlacesPlaceDto(
                    id = it.id,
                    name = it.name
                )
            }
            .toList()
        val totalPlaces = hrTotalPlacesRepo.findAll().asSequence()
            .filter { !it.deleted }
            .map {
                HrPlacesPlaceDto(
                    id = it.id,
                    name = it.name
                )
            }
            .toList()
        return HrPlacesDictRes(
            places = places,
            totalPlaces = totalPlaces,
            updatedBy = hrPlaceTplaceLogViewRepo.getAllActiveUpdateBy()
        )
    }

    fun getHrPlacesFilters(): HrPlacesDictRes {
        val places = hrPlaceTplaceLogViewRepo.getAllActivePlaces().map {
            HrPlacesPlaceDto(
                id = it.id,
                name = it.name
            )
        }
        val totalPlaces = hrPlaceTplaceLogViewRepo.getAllActiveTotalPlaces().map {
            HrPlacesPlaceDto(
                id = it.id,
                name = it.name
            )
        }
        return HrPlacesDictRes(
            places = places,
            totalPlaces = totalPlaces,
            updatedBy = hrPlaceTplaceLogViewRepo.getAllActiveUpdateBy()
        )
    }

    fun getUnfilledNames(): List<String> {
        return hrPlacesExposedRepo.getUnfilled()
    }

    private fun List<HrPlacesLogReqFilter>.getPredicates(
        criteriaBuilder: CriteriaBuilder,
        root: Root<HrPlaceTplaceLogViewEntityDao>
    ): Predicate {
        val predicates = buildList(size + 2) {
            val startDatePath = root.get<LocalDate>("startDate")
            val endDatePath = root.get<LocalDate>("endDate")
            val minStartDatePath = root.get<LocalDate>("minStartDate")
            val currentDate = getCurrentDate().toLocalDate()
            add(
                criteriaBuilder.or(
                    criteriaBuilder.and(
                        criteriaBuilder.lessThanOrEqualTo(startDatePath, currentDate),
                        criteriaBuilder.or(
                            endDatePath.isNull,
                            criteriaBuilder.greaterThanOrEqualTo(endDatePath, currentDate)
                        )
                    ),
                    criteriaBuilder.and(
                        criteriaBuilder.greaterThan(minStartDatePath, currentDate),
                        criteriaBuilder.equal(minStartDatePath, startDatePath)
                    )
                )
            )
            <EMAIL> { filter ->
                val path = when (filter.name) {
                    HrPlacesLogReqFilterField.PLACE_NAME -> root.join<HrPlaceTplaceLogViewEntityDao, HrPlaceDao>("place", JoinType.LEFT).get<String>("name")
                    HrPlacesLogReqFilterField.TOTAL_PLACE_NAME ->
                        root.join<HrPlaceTplaceLogViewEntityDao, HrTotalPlaceDao>("totalPlace", JoinType.LEFT).get("name")

                    HrPlacesLogReqFilterField.UPDATED_BY ->
                        root.get("updatedBy")
                }
                add(path.`in`(filter.value))
            }
        }
        return criteriaBuilder.and(*predicates.toTypedArray())
    }

    private fun SortItem<HrPlacesLogReqSortField>.getOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<HrPlaceTplaceLogViewEntityDao>
    ): Order {
        val path = when (column) {
            HrPlacesLogReqSortField.PLACE_ID -> root.join<HrPlaceTplaceLogViewEntityDao, HrPlaceDao>("place", JoinType.LEFT).get<Long>("id")
            HrPlacesLogReqSortField.PLACE_NAME -> root.join<HrPlaceTplaceLogViewEntityDao, HrPlaceDao>("place", JoinType.LEFT).get<String>("name")
            HrPlacesLogReqSortField.TOTAL_PLACE_ID -> root.join<HrPlaceTplaceLogViewEntityDao, HrTotalPlaceDao>("totalPlace", JoinType.LEFT).get<Long>("id")
            HrPlacesLogReqSortField.TOTAL_PLACE_NAME -> root.join<HrPlaceTplaceLogViewEntityDao, HrTotalPlaceDao>("totalPlace", JoinType.LEFT).get<String>("name")
            HrPlacesLogReqSortField.START_DATE -> root.get<LocalDate>("startDate")
            HrPlacesLogReqSortField.END_DATE -> root.get<LocalDate>("endDate")
            HrPlacesLogReqSortField.CREATED_AT -> root.get<LocalDate>("createdAt")
            HrPlacesLogReqSortField.CREATED_BY -> root.get<String>("createdBy")
            HrPlacesLogReqSortField.UPDATED_AT -> root.get<LocalDate>("updatedAt")
            HrPlacesLogReqSortField.UPDATED_BY -> root.get<String>("updatedBy")
        }
        return if (asc) {
            criteriaBuilder.asc(path)
        } else {
            criteriaBuilder.desc(path)
        }
    }

    private fun getGroupDto(placeId: Long): HrPlacesLogGroupDto? {
        refreshOrgUnit()
        val date = getCurrentDate().toLocalDate()
        val entities = hrPlaceTplaceLogViewRepo.getByPlace(placeId)
        val root = entities.asSequence()
            .filter { it.startDate <= date }
            .maxByOrNull { it.startDate } ?: entities.minByOrNull { it.startDate } ?: return null
        return buildGroupDto(root, entities)
    }

    private fun buildGroupDto(
        root: HrPlaceTplaceLogViewEntityDao,
        entities: Collection<HrPlaceTplaceLogViewEntityDao>
    ): HrPlacesLogGroupDto {
        val children = entities.asSequence()
            .filter { it.totalPlace != null }
            .sortedByDescending { it.startDate }
            .map { dao ->
                HrPlaceLogDto(
                    id = dao.id,
                    place = HrPlaceDto(
                        id = dao.place.id,
                        name = dao.place.name,
                        deleted = dao.place.deleted
                    ),
                    totalPlace = dao.totalPlace?.let {
                        HrPlaceDto(
                            id = it.id,
                            name = it.name,
                            deleted = it.deleted
                        )
                    },
                    startDate = dao.startDate,
                    endDate = dao.endDate ?: infinityDate,
                    createdAt = dao.createdAt,
                    createdBy = dao.createdBy,
                    updatedAt = dao.updatedAt,
                    updatedBy = dao.updatedBy
                )
            }
            .toList()
        return HrPlacesLogGroupDto(
            id = root.id,
            place = HrPlaceDto(
                id = root.place.id,
                name = root.place.name,
                deleted = root.place.deleted
            ),
            totalPlace = root.totalPlace?.let {
                HrPlaceDto(
                    id = it.id,
                    name = it.name,
                    deleted = it.deleted
                )
            },
            startDate = root.startDate,
            endDate = root.endDate ?: infinityDate,
            createdAt = root.createdAt,
            createdBy = root.createdBy,
            updatedAt = root.updatedAt,
            updatedBy = root.updatedBy,
            children = children
        )
    }

    private fun HrPlaceTplaceLogViewEntityDao.toDto(): HrPlacesLogGroupDto {
        val entities = hrPlaceTplaceLogViewRepo.getByPlace(place.id)
        return buildGroupDto(this, entities)
    }

    private fun getCurrentDate(): LocalDateTime =
        LocalDateTime.ofInstant(Instant.now(), ZoneId.of("Europe/Moscow"))

    private enum class HrPlaceLogColumn(
        val title: String
    ) {
        PlaceName("Название площадки"),
        TotalPlaceName("Итог. подразд."),
        StartDate("Начало"),
        EndDate("Конец"),
        UpdatedBy("Сотрудник")
    }
}
