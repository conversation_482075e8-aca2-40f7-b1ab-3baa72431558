package com.x5.logistics.service

import com.x5.logistics.rest.dto.repairplace.RepairPlaceListColumns
import com.x5.logistics.rest.dto.repairplace.RepairPlaceListItem
import com.x5.logistics.rest.dto.repairplace.RepairPlaceListReq
import com.x5.logistics.rest.exception.ExportTimeoutException
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.EXPORT_TIMEOUT
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.getLogger
import com.x5.logistics.service.settingssheet.ExportRequest
import com.x5.logistics.service.settingssheet.Filter
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.service.settingssheet.SettingsSheetService
import com.x5.logistics.service.settingssheet.SortOrder
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.springframework.stereotype.Service
import java.io.InputStream

@Service
class RepairPlaceExportService(
    val settingsSheetService: SettingsSheetService,
    val service: RepairPlaceService
) {

    private val log = getLogger()

    fun exportRepairPlaceReport(req: RepairPlaceListReq, username: String?): InputStream {
        val timeout = System.currentTimeMillis() + EXPORT_TIMEOUT
        val count = service.getRepairPlaceListReportSize(req)
        val totals = service.getRepairPlaceListReportItems(req.copy(pageNumber = 0, pageSize = 1)).first
        val ourRepairShare = (totals.totalShare ?: 0.0) / 100
        val planFulfillment = (totals.totalFulfillment ?: 0.0) / 100

        val out = streamWorkbook {
            val reportReq = ExportRequest(
                from = req.from,
                to = req.to,
                userName = username,
                columns = req.columns.map { it.title },
                filters = req.filters.map { Filter(it.name.title, it.condition.description, it.value) },
                sort = req.sort.map { SortOrder(it.column.title, it.asc) },
                isRepairPlace = true
            )
            settingsSheetService.addSettingsSheet(this, reportReq)

            sheet(ReportName.REPAIR_PLACE.title) {
                header {
                    currStyle = ExcelStyles.Header.style(this@streamWorkbook)
                    req.columns.forEach {
                        head(it.title)
                    }
                }

                for (pageNumber in 0..(count / EXPORT_BATCH_SIZE)) {
                    if (System.currentTimeMillis() > timeout) throw ExportTimeoutException("export timeout")
                    val items = service.getRepairPlaceListReportItems(
                        req.copy(
                            pageNumber = pageNumber.toInt(),
                            pageSize = EXPORT_BATCH_SIZE
                        )
                    ).second
                    log.debug("generate page {} of total pages {}", pageNumber, count / EXPORT_BATCH_SIZE)
                    items.forEach { item ->
                        row {
                            req.columns.forEach { column -> cellByColumnType(this@streamWorkbook, this, column, item) }
                        }
                    }
                }

                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(count)

                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Доля работ на РЗ:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(ourRepairShare).cellStyle = ExcelStyles.RoundedPercent.style(this@streamWorkbook)

                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Вып. плана выработки РЗ:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(planFulfillment).cellStyle = ExcelStyles.RoundedPercent.style(this@streamWorkbook)
                }
                (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
            }.autosize()
            setActiveSheet(ReportName.REPAIR_PLACE.title)
            (wb.getSheetAt(0) as SXSSFSheet).let { sheet ->
                for (colIdx in 0..35) {
                    sheet.autoSizeColumn(colIdx, true)
                }
            }

        }.toInputStream()

        return out
    }

    private fun cellByColumnType(
        wb: WbBuilder,
        rb: RowBuilder,
        column: RepairPlaceListColumns,
        item: RepairPlaceListItem
    ) {
        when (column) {
            RepairPlaceListColumns.REPAIR_PLACE -> {
                rb.cell(item.repairPlace).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.TERRITORY -> {
                rb.cell(item.terName).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.MACRORIGION -> {
                rb.cell(item.mrName).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.SERVICE_TER -> {
                rb.cell(item.serviceTer).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.SERVICE_MR -> {
                rb.cell(item.serviceMr).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.SERVICE_ATP -> {
                rb.cell(item.serviceAtp).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.SERVICE_MVZ -> {
                rb.cell(item.serviceMvz).cellStyle = ExcelStyles.General.style(wb)
            }

            RepairPlaceListColumns.TS_NUMBER -> {
                rb.cell(item.tsNumber).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.FACT_TS_NUMBER -> {
                rb.cell(item.factTsNumber).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.ATP_REQUIREMENT -> {
                rb.cell(item.atpRequirement).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.GOAL_POST_PRODUCTION -> {
                rb.cell(item.goalPostProduction).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.POSTS_NUMBER_PER_DAY -> {
                rb.cell(item.postsNumberPerDay).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.POSTS_NUMBER_PER_NIGHT -> {
                rb.cell(item.postsNumberPerNight).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.TOTAL_POSTS_NUMBER -> {
                rb.cell(item.totalPostsNumber).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.REPAIR_PLACE_WORKING_POWER -> {
                rb.cell(item.repairPlaceWorkingPower).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.REPAIR_PLACE_TOTAL_POWER -> {
                rb.cell(item.repairPlaceTotalPower).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.GOAL_REPAIR_PLACE_PRODUCTION -> {
                rb.cell(item.goalRepairPlaceProduction).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.REPAIR_PLACE_PROVISION -> {
                rb.cell(item.repairPlaceProvision?.div(100)).cellStyle = generateStyle(
                    wb,
                    item.repairPlaceProvision?.div(100),
                    RepairPlaceListColumns.REPAIR_PLACE_PROVISION
                )
            }

            RepairPlaceListColumns.WORK_PERCENT -> {
                rb.cell(item.workPercent?.div(100)).cellStyle =
                    generateStyle(wb, item.workPercent?.div(100), RepairPlaceListColumns.WORK_PERCENT)
            }

            RepairPlaceListColumns.FACT_REPAIR_PLACE_PRODUCTION -> {
                rb.cell(item.factRepairPlaceProduction).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.REPAIR_PLACE_PRODUCTION_PLAN_PERFORMANCE -> {
                rb.cell(item.repairPlaceProductionPlanPerformance?.div(100)).cellStyle = generateStyle(
                    wb,
                    item.repairPlaceProductionPlanPerformance?.div(100),
                    RepairPlaceListColumns.REPAIR_PLACE_PRODUCTION_PLAN_PERFORMANCE
                )
            }

            RepairPlaceListColumns.FACT_POST_PRODUCTION -> {
                rb.cell(item.factPostProduction).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.GOAL_MECHANIC_PRODUCTION -> {
                rb.cell(item.goalMechanicProduction).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.GOAL_MECHANIC_REQUIREMENT -> {
                rb.cell(item.goalMechanicRequirement).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.FACT_MECHANIC_SSCH -> {
                rb.cell(item.factMechanicSsch).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.FACT_MECHANIC_NUMBER -> {
                rb.cell(item.factMechanicNumber).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.FACT_MECHANIC_PRODUCTION -> {
                rb.cell(item.factMechanicProduction).cellStyle = ExcelStyles.Float.style(wb)
            }

            RepairPlaceListColumns.MECHANIC_PRODUCTION_PLAN_PERFORMANCE -> {
                rb.cell(item.mechanicProductionPlanPerformance?.div(100)).cellStyle = generateStyle(
                    wb,
                    item.mechanicProductionPlanPerformance?.div(100),
                    RepairPlaceListColumns.MECHANIC_PRODUCTION_PLAN_PERFORMANCE
                )
            }

            RepairPlaceListColumns.MECHANIC_PROVISION -> {
                rb.cell(item.mechanicProvision?.div(100)).cellStyle = generateStyle(
                    wb,
                    item.mechanicProvision?.div(100),
                    RepairPlaceListColumns.MECHANIC_PROVISION
                )
            }
        }
    }

    private fun generateStyle(wb: WbBuilder, value: Double?, column: RepairPlaceListColumns): CellStyle {
        return ExcelStyles.Percent.style(wb).apply {
            if (value != null) {
                val customColor = wb.wb.creationHelper.createExtendedColor()
                val highlightings = column.highlightings
                val color = highlightings.firstOrNull { it.threshold <= value }?.argb
                color?.let {
                    customColor.argbHex = color
                    setFillForegroundColor(customColor)
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                }
            }
        }
    }
}
