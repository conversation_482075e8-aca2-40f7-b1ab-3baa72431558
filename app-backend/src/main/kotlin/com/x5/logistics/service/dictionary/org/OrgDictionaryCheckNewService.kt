package com.x5.logistics.service.dictionary.org

import com.x5.logistics.data.dictionary.org.AtpLogTable
import com.x5.logistics.data.dictionary.org.AtpTable
import com.x5.logistics.data.dictionary.org.MrLogTable
import com.x5.logistics.data.dictionary.org.MrTable
import com.x5.logistics.data.dictionary.org.MvzCodesTable
import com.x5.logistics.data.dictionary.org.MvzLogTable
import com.x5.logistics.rest.dto.dictionary.org.OrgDictionaryCheckNewResp
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Min
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.javatime.JavaLocalDateColumnType
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.transactions.transaction
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class OrgDictionaryCheckNewService {

    val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    fun checkNewOrg() = transaction {
        if (logQuery) addLogger(StdOutSqlLogger)
        OrgDictionaryCheckNewResp(
            mvz = getNewMvz(),
            atp = getNewAtp(),
            mr = getNewMr(),
        )
    }

    private fun getNewMvz(): List<String> {
        val mvzLogMinDate = Min(MvzLogTable.startDate, JavaLocalDateColumnType()).alias("mvz_min_date")
        val mvzLogSubquery = MvzLogTable.select(MvzLogTable.uid, mvzLogMinDate)
            .where { MvzLogTable.deleted eq false }
            .groupBy(MvzLogTable.uid)
            .alias("mvzLog")
        return MvzCodesTable
            .join(mvzLogSubquery, JoinType.LEFT, MvzCodesTable.id, mvzLogSubquery[MvzLogTable.uid])
            .select(MvzCodesTable.id)
            .where {
                mvzLogSubquery[MvzLogTable.uid].isNull() or
                        (MvzCodesTable.startDate less mvzLogSubquery[mvzLogMinDate])
            }
            .orderBy(MvzCodesTable.id, SortOrder.ASC)
            .map { it[MvzCodesTable.id].value }
    }

    private fun getNewAtp(): List<String> {
        val mvzLogMinDate = Min(MvzLogTable.startDate, JavaLocalDateColumnType()).alias("mvz_min_date")
        val mvzLogSubquery = MvzLogTable.select(MvzLogTable.atpId, mvzLogMinDate)
            .where { MvzLogTable.deleted eq false }
            .groupBy(MvzLogTable.atpId)
            .alias("mvzLog")
        val atpLogMinDate = Min(AtpLogTable.startDate, JavaLocalDateColumnType()).alias("atp_min_date")
        val atpLogSubquery = AtpLogTable.select(AtpLogTable.atpId, atpLogMinDate)
            .where { AtpLogTable.deleted eq false }
            .groupBy(AtpLogTable.atpId)
            .alias("atpLog")
        return AtpTable
            .join(mvzLogSubquery, JoinType.LEFT, AtpTable.id, mvzLogSubquery[MvzLogTable.atpId])
            .join(atpLogSubquery, JoinType.LEFT, AtpTable.id, atpLogSubquery[AtpLogTable.atpId])
            .select(AtpTable.name)
            .where {
                AtpTable.deleted eq false and
                        (atpLogSubquery[AtpLogTable.atpId].isNull() or
                                (mvzLogSubquery[mvzLogMinDate] less atpLogSubquery[atpLogMinDate]))
            }
            .orderBy(AtpTable.name, SortOrder.ASC)
            .map { it[AtpTable.name] }
    }

    private fun getNewMr(): List<String> {
        val atpLogMinDate = Min(AtpLogTable.startDate, JavaLocalDateColumnType()).alias("atp_min_date")
        val atpLogSubquery = AtpLogTable.select(AtpLogTable.mrId, atpLogMinDate)
            .where { AtpLogTable.deleted eq false }
            .groupBy(AtpLogTable.mrId)
            .alias("atpLog")
        val mrLogMinDate = Min(MrLogTable.startDate, JavaLocalDateColumnType()).alias("mr_min_date")
        val mrLogSubquery = MrLogTable.select(MrLogTable.mrId, mrLogMinDate)
            .where { MrLogTable.deleted eq false }
            .groupBy(MrLogTable.mrId)
            .alias("mrLog")
        return MrTable
            .join(atpLogSubquery, JoinType.LEFT, MrTable.id, atpLogSubquery[AtpLogTable.mrId])
            .join(mrLogSubquery, JoinType.LEFT, MrTable.id, mrLogSubquery[MrLogTable.mrId])
            .select(MrTable.name)
            .where {
                MrTable.deleted eq false and
                        (mrLogSubquery[MrLogTable.mrId].isNull() or
                                (atpLogSubquery[atpLogMinDate] less mrLogSubquery[mrLogMinDate]))
            }
            .orderBy(MrTable.name, SortOrder.ASC)
            .map { it[MrTable.name] }
    }
}
