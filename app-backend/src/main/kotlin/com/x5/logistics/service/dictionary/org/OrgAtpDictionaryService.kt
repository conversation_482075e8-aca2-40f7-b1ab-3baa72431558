package com.x5.logistics.service.dictionary.org

import com.x5.logistics.data.dictionary.org.AtpEntity
import com.x5.logistics.data.dictionary.org.AtpLogEntity
import com.x5.logistics.data.dictionary.org.AtpLogTable
import com.x5.logistics.data.dictionary.org.AtpLogTable.atpLogSubqueryAlias
import com.x5.logistics.data.dictionary.org.AtpLogTable.atpName
import com.x5.logistics.data.dictionary.org.AtpLogTable.atpTableAtpId
import com.x5.logistics.data.dictionary.org.AtpLogTable.atpType
import com.x5.logistics.data.dictionary.org.AtpLogTable.endDate
import com.x5.logistics.data.dictionary.org.AtpLogTable.isCurrent
import com.x5.logistics.data.dictionary.org.AtpLogTable.minStartDate
import com.x5.logistics.data.dictionary.org.AtpLogTable.mrDeleted
import com.x5.logistics.data.dictionary.org.AtpLogTable.mrName
import com.x5.logistics.data.dictionary.org.AtpLogTable.mrTableMrId
import com.x5.logistics.data.dictionary.org.AtpLogTable.retailNetwork
import com.x5.logistics.data.dictionary.org.AtpTable
import com.x5.logistics.data.dictionary.org.AtpTypeEntity
import com.x5.logistics.data.dictionary.org.MrEntity
import com.x5.logistics.data.dictionary.org.MrTable
import com.x5.logistics.data.dictionary.org.MvzLogTable
import com.x5.logistics.data.dictionary.org.MvzLogTable.atpLifeStart
import com.x5.logistics.repository.aliasOnlyExpression
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.castToLong
import com.x5.logistics.repository.refreshOrgUnit
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryColumn
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryColumnFilter
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryCreateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryCreateRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryDeleteLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryFilters
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryItemDto
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryListRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryPagedResponse
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionarySelects
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryUpdateLogEntryRequest
import com.x5.logistics.rest.dto.dictionary.org.atp.OrgAtpDictionaryUpdateRequest
import com.x5.logistics.service.RowBuilder
import com.x5.logistics.service.getStyles
import com.x5.logistics.service.streamWorkbook
import com.x5.logistics.util.EXPORT_BATCH_SIZE
import com.x5.logistics.util.getLogger
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.xssf.streaming.SXSSFSheet
import org.jetbrains.exposed.sql.AbstractQuery
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.StdOutSqlLogger
import org.jetbrains.exposed.sql.addLogger
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.notExists
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.InputStream
import java.time.Instant

@Service
class OrgAtpDictionaryService {

    val log = getLogger()

    @Value("\${x5.logistics.logging.queries}")
    var logQuery: Boolean = false

    suspend fun getList(req: OrgAtpDictionaryListRequest): OrgAtpDictionaryPagedResponse = newSuspendedTransaction {
        if (logQuery) addLogger(StdOutSqlLogger)

        val mvzLogSubquery = MvzLogTable.mvzLogAtpMinDateSubquery
        val queryWithFilters = atpLogSubqueryAlias
            .join(mvzLogSubquery, JoinType.LEFT, atpLogSubqueryAlias[AtpLogTable.atpId], mvzLogSubquery[MvzLogTable.atpId])
            .select(atpTableAtpId.aliasOnlyExpression())
            .applyFiltersAndSort(req)
            .andWhere { isCurrent.aliasOnlyExpression() eq booleanLiteral(true) }

        val count = queryWithFilters.count()

        val actualAtpIds = queryWithFilters
            .limit(req.pageSize, (req.pageNumber * req.pageSize).toLong())
            .map { it[atpTableAtpId.aliasOnlyExpression()].value }

        val query = atpLogSubqueryAlias
            .join(mvzLogSubquery, JoinType.LEFT, atpLogSubqueryAlias[AtpLogTable.atpId], mvzLogSubquery[MvzLogTable.atpId])
            .select(
                atpLogSubqueryAlias.columns
                    + endDate.aliasOnlyExpression()
                    + minStartDate.aliasOnlyExpression()
                    + isCurrent.aliasOnlyExpression()
                    + atpTableAtpId.aliasOnlyExpression()
                    + atpName.aliasOnlyExpression()
                    + atpType.aliasOnlyExpression()
                    + retailNetwork.aliasOnlyExpression()
                    + mrTableMrId.aliasOnlyExpression()
                    + mrName.aliasOnlyExpression()
                    + mrDeleted.aliasOnlyExpression()
                    + atpLifeStart
            )
            .where {
                (atpLogSubqueryAlias[atpTableAtpId].castToLong() inList actualAtpIds)
            }
        val items = query
            .toDto(atpLogSubqueryAlias)
            .groupBy { it.atpId }
            .mapValues { entry ->
                entry.value
                    .first { it.isCurrent }
                    .copy(children = entry.value.map { OrgAtpDictionaryItemDto.Child.of(it) }
                        .filter { it.id != null }
                        .sortedBy { it.startDate }
                        .reversed())
            }

        log.info("Found ${items.size} items")
        OrgAtpDictionaryPagedResponse(
            count = count,
            pageNumber = req.pageNumber,
            pageSize = req.pageSize,
            items = actualAtpIds.mapNotNull { items[it] }
        )
    }

    suspend fun createAtp(req: OrgAtpDictionaryCreateRequest, author: String): OrgAtpDictionaryItemDto {
        validateCreateUpdateRequest(
            atpName = req.atpName,
            mrId = req.mrId,
            atpType = req.atpType,
        )
        val atpEntity = newSuspendedTransaction {
            AtpEntity.new {
                name = req.atpName
                type = req.atpType
                retailNetwork = req.retailNetwork
                createdBy = author
                createdAt = Instant.now()
                updatedBy = author
                updatedAt = Instant.now()
            }
        }
        return createLogEntry(
            OrgAtpDictionaryCreateLogEntryRequest(
                atpId = atpEntity.id.value,
                mrId = req.mrId,
                startDate = req.startDate
            ),
            author
        )
    }

    suspend fun updateAtp(req: OrgAtpDictionaryUpdateRequest, name: String): OrgAtpDictionaryItemDto {
        newSuspendedTransaction {
            validateCreateUpdateRequest(
                atpName = req.atpName,
                atpId = req.atpId,
                atpType = req.atpType,
            )
            AtpEntity.findByIdAndUpdate(req.atpId) {
                it.name = req.atpName
                it.type = req.atpType
                it.retailNetwork = req.retailNetwork
                it.updatedBy = name
                it.updatedAt = Instant.now()
            }

        }
        return getItemById(req.atpId)
    }

    suspend fun createLogEntry(req: OrgAtpDictionaryCreateLogEntryRequest, name: String): OrgAtpDictionaryItemDto {
        newSuspendedTransaction {
            validateCreateLogEntryRequest(req)

            AtpLogEntity.new {
                this.atp = AtpEntity.findById(req.atpId)!!
                this.macroRegion = MrEntity.findById(req.mrId)!!
                this.startDate = req.startDate
                this.createdBy = name
                this.createdAt = Instant.now()
                this.updatedBy = name
                this.updatedAt = Instant.now()
            }

        }
        return getItemById(req.atpId)
    }

    suspend fun updateLogEntry(req: OrgAtpDictionaryUpdateLogEntryRequest, name: String): OrgAtpDictionaryItemDto {
        newSuspendedTransaction {
            validateUpdateLogEntryRequest(req)

            val logEntry = AtpLogEntity.find {
                (AtpLogTable.deleted eq false) and
                        (AtpLogTable.id eq req.id) and
                        (AtpLogTable.atpId eq req.atpId)
            }.firstOrNull()
                ?: throw IllegalArgumentException("Связка АТП-МР с id=${req.id} не соответствует АТП с id=${req.atpId}")

            logEntry.macroRegion = MrEntity.findById(req.mrId)!!
            logEntry.startDate = req.startDate
            logEntry.updatedBy = name
            logEntry.updatedAt = Instant.now()

        }
        return getItemById(req.atpId)
    }

    suspend fun deleteLogEntry(req: OrgAtpDictionaryDeleteLogEntryRequest, name: String): OrgAtpDictionaryItemDto {
        val logEntry = newSuspendedTransaction {
            checkAtpLog(req.id)

            AtpLogEntity.findById(req.id)!!.apply {
                deleted = true
                updatedBy = name
                updatedAt = Instant.now()
            }
        }
        return getItemById(logEntry.atp.id.value)
    }

    suspend fun getFilters(): OrgAtpDictionaryFilters {
        suspend fun <T> getDistinctSimpleValuesAsync(column: Column<T>): Deferred<List<T>> = coroutineScope {
            async(Dispatchers.IO) {
                newSuspendedTransaction {
                    val query = AtpTable
                        .select(column)
                        .where { AtpTable.deleted eq false }
                        .withDistinct()
                        .orderBy(column)
                    if (logQuery) log.info(query.prepareSQL(QueryBuilder(false)))
                    query.mapNotNull { it[column] }
                }
            }
        }
        return newSuspendedTransaction {
            val atpNameDeferred = getDistinctSimpleValuesAsync(AtpTable.name)
            val atpTypeDeferred = getDistinctSimpleValuesAsync(AtpTable.type)
            val retailNetworkDeferred = getDistinctSimpleValuesAsync(AtpTable.retailNetwork)
            val mrNameDeferred = coroutineScope {
                async(Dispatchers.IO) {
                    newSuspendedTransaction {
                        val query = atpLogSubqueryAlias
                            .select(mrName.aliasOnlyExpression())
                            .where {
                                (isCurrent.aliasOnlyExpression() eq booleanLiteral(true)) and
                                        (AtpLogTable.deleted.aliasOnlyExpression() eq booleanLiteral(false) and
                                                (mrDeleted.aliasOnlyExpression() eq booleanLiteral(false)))
                            }
                            .withDistinct()
                            .orderBy(mrName)
                        if (logQuery) log.info(query.prepareSQL(QueryBuilder(false)))
                        query.mapNotNull { it[mrName.aliasOnlyExpression()] }
                    }
                }
            }
            OrgAtpDictionaryFilters(
                atpName = atpNameDeferred.await(),
                atpType = atpTypeDeferred.await(),
                retailNetwork = retailNetworkDeferred.await(),
                mrName = mrNameDeferred.await()
            )
        }
    }

    suspend fun getSelects(): OrgAtpDictionarySelects {
        val typeDeferred = coroutineScope {
            async(Dispatchers.IO) {
                newSuspendedTransaction {
                    AtpTypeEntity.all()
                }
            }
        }
        val retailNetworks = listOf(
            "Не назначена",
            "Перекресток",
            "Пятерочка",
            "Чижик")
        val mrDeferred = coroutineScope {
            async(Dispatchers.IO) {
                newSuspendedTransaction {
                    MrEntity.find { MrTable.deleted eq false }
                }
            }
        }

        return newSuspendedTransaction {
            OrgAtpDictionarySelects(
                atpType = typeDeferred.await().map { it.id.value },
                retailNetwork = retailNetworks,
                mrName = mrDeferred.await().map {
                    OrgAtpDictionarySelects.Mr(
                        id = it.id.value,
                        name = it.name
                    )
                }
            )
        }
    }

    suspend fun export(req: OrgAtpDictionaryListRequest): InputStream {
        val column = OrgAtpDictionaryColumn.entries
        var page: List<OrgAtpDictionaryItemDto>
        val size = getList(req.copy(pageNumber = 0, pageSize = 1)).count.toInt()
        val out = streamWorkbook {
            val styles = getStyles()
            sheet("Справочник АТП") {
                style {
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                }
                header {
                    column.forEach { column ->
                        head(column.columnTitle)
                    }
                }
                for (pageNumber in 0..(size / EXPORT_BATCH_SIZE)) {
                    runBlocking {
                        page = getList(req.copy(pageNumber = pageNumber, pageSize = EXPORT_BATCH_SIZE)).items
                    }
                    page.forEach { item ->
                        row {
                            column.forEach {
                                cellByColumnType(this, styles, it, item)
                            }
                        }
                    }
                    (wb.getSheetAt(0) as SXSSFSheet).flushRows(10)
                }
            }.autosize(column.size)
        }.toInputStream()

        return out
    }

    fun cellByColumnType(
        rb: RowBuilder,
        styles: Map<String, CellStyle>,
        column: OrgAtpDictionaryColumn,
        item: OrgAtpDictionaryItemDto
    ) = when (column) {
        OrgAtpDictionaryColumn.atpId -> rb.cell(item.atpId).cellStyle = styles["general"]
        OrgAtpDictionaryColumn.atpName -> rb.cell(item.atpName).cellStyle = styles["general"]
        OrgAtpDictionaryColumn.atpType -> rb.cell(item.atpType).cellStyle = styles["general"]
        OrgAtpDictionaryColumn.retailNetwork -> rb.cell(item.retailNetwork).cellStyle = styles["general"]
        OrgAtpDictionaryColumn.startDate -> rb.cell(item.startDate).cellStyle = styles["date"]
        OrgAtpDictionaryColumn.endDate -> rb.cell(item.endDate).cellStyle = styles["date"]
        OrgAtpDictionaryColumn.updatedBy -> rb.cell(item.updatedBy).cellStyle = styles["general"]
        OrgAtpDictionaryColumn.mrName -> rb.cell(item.mrName).cellStyle = styles["general"]
    }

    private suspend fun validateCreateLogEntryRequest(req: OrgAtpDictionaryCreateLogEntryRequest) {
        checkAtp(req.atpId)
        checkMacroRegion(req.mrId)
        if (AtpLogEntity.find {
                (AtpLogTable.atpId eq req.atpId) and
                        (AtpLogTable.deleted eq false) and
                        (AtpLogTable.startDate eq req.startDate)
            }.count() > 0L) {
            throw IllegalArgumentException("Для АТП с id=${req.atpId} уже создана связь от ${req.startDate}")
        }
    }

    private suspend fun validateUpdateLogEntryRequest(req: OrgAtpDictionaryUpdateLogEntryRequest) {
        checkAtpLog(req.id)
        checkMacroRegion(req.mrId)
        if (AtpLogEntity.find {
                (AtpLogTable.deleted eq false) and
                        (AtpLogTable.atpId eq req.atpId) and
                        (AtpLogTable.startDate eq req.startDate) and
                        (AtpLogTable.id neq req.id)
            }.count() > 0
        ) {
            throw IllegalArgumentException("АТП с id=${req.atpId} уже создана связь от ${req.startDate}")
        }
    }

    private suspend fun getItemById(id: Long): OrgAtpDictionaryItemDto {
        refreshOrgUnit()
        return getList(
            OrgAtpDictionaryListRequest(
                pageNumber = 0,
                pageSize = 1,
                filters = listOf(
                    OrgAtpDictionaryColumnFilter(
                        name = OrgAtpDictionaryColumn.atpId,
                        condition = FilterCondition.equal,
                        value = listOf(id)
                    )
                ),
                sort = listOf()
            )
        ).items.first()
    }

    private suspend fun validateCreateUpdateRequest(
        atpName: String,
        atpType: String,
        mrId: Long? = null,
        atpId: Long? = null,
    ) {
        if (AtpEntity.find {
                (AtpTable.name.lowerCase() eq atpName.lowercase()) and
                        (AtpTable.deleted eq false) and
                        (AtpTable.id neq atpId)
            }.count() > 0L
        ) {
            log.error("АТП с именем $atpName уже существует")
            throw IllegalArgumentException("АТП с именем $atpName уже существует")
        }
        checkAtpType(atpType)
        if (mrId != null) checkMacroRegion(mrId)
    }

    private suspend fun checkAtp(atpId: Long) {
        if (AtpEntity.find { (AtpTable.id eq atpId) and (AtpTable.deleted eq false) }.count() == 0L) {
            throw IllegalArgumentException("Не найдено АТП с id=${atpId}")
        }
    }

    private suspend fun checkAtpType(atpType: String) {
        if (AtpTypeEntity.find { AtpTypeEntity.table.id eq atpType }.count() == 0L) {
            throw IllegalArgumentException("Не найден Вид деят. АТП = $atpType")
        }
    }

    private suspend fun checkMacroRegion(mrId: Long) {
        if (MrEntity.find { (MrTable.id eq mrId) and (MrTable.deleted eq false) }.count() == 0L) {
            throw IllegalArgumentException("Не найден Макрорегион с id=${mrId}")
        }
    }

    private suspend fun checkAtpLog(id: Long) {
        if (AtpLogEntity.find { (AtpLogTable.id eq id) and (AtpLogTable.deleted eq false) }.count() == 0L) {
            throw IllegalArgumentException("Не найдена связь АТП-МР с id=${id}")
        }
    }

    private fun Query.applyFiltersAndSort(req: OrgAtpDictionaryListRequest): Query = copy()
        .apply {
            req.filters.forEach { filter ->
                andWhere {
                    buildFilterPredicate(
                        condition = filter.condition,
                        values = filter.value,
                        type = filter.name.type,
                        exposedExpression = filter.name.exposedExpression,
                        strictFilter = false
                    )
                }
            }
            if (req.sort.isEmpty()) {
                applyDefaultSort()
            } else {
                applySort(req)
            }
        }

    private fun Query.applySort(req: OrgAtpDictionaryListRequest): Query = apply {
        orderBy(
            *req.sort.map {
                it.column.exposedExpression to if (it.asc) SortOrder.ASC else SortOrder.DESC
            }.toTypedArray()
        )
    }

    private fun Query.applyDefaultSort() = orderBy(
        // Сначала сортировка по группам
        Case()
            .When(
                // Группа 1: АТП без связанных записей в atp_log
                notExists(
                    AtpLogTable
                        .select(intLiteral(1))
                        .where { (AtpLogTable.atpId.castToLong() eq atpTableAtpId.aliasOnlyExpression()) and
                                (AtpLogTable.deleted eq false) }
                ),
                intLiteral(1)
            )
            .When(
                // Группа 2: АТП где MIN(start_date) > atp_table.start_date
                AtpLogTable.minStartDate.aliasOnlyExpression() greater atpLifeStart.delegate,
                intLiteral(2)
            )
            .Else(intLiteral(3)) to SortOrder.ASC,
        // Затем внутри каждой группы сортировка по id
        atpTableAtpId.aliasOnlyExpression() to SortOrder.ASC
    )

    private fun AbstractQuery<*>.toDto(atpLogSubquery: QueryAlias): List<OrgAtpDictionaryItemDto> =
        with(AtpLogTable) {
            map {
                OrgAtpDictionaryItemDto(
                    atpId = it[atpTableAtpId.aliasOnlyExpression()].value,
                    atpName = it[atpName.aliasOnlyExpression()],
                    atpType = it[atpType.aliasOnlyExpression()],
                    retailNetwork = it[retailNetwork.aliasOnlyExpression()],
                    logId = it[atpLogSubquery[id]]?.value,
                    mrId = it[mrTableMrId.aliasOnlyExpression()]?.value,
                    mrName = it[mrName.aliasOnlyExpression()],
                    atpStartDate = it[atpLifeStart],
                    startDate = it[atpLogSubquery[startDate]],
                    endDate = it[endDate.aliasOnlyExpression()],
                    updatedBy = it[atpLogSubquery[updatedBy]],
                    updatedAt = it[atpLogSubquery[updatedAt]],
                    children = listOf(),
                    isCurrent = it[isCurrent.aliasOnlyExpression()]
                )
            }
        }

}