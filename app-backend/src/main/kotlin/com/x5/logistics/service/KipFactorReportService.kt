package com.x5.logistics.service

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.dictionary.KipTable
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.TsReadyHistTable
import com.x5.logistics.data.dictionary.UnitTable
import com.x5.logistics.repository.ColumnType
import com.x5.logistics.repository.CountStar
import com.x5.logistics.repository.Granularity
import com.x5.logistics.repository.Greatest
import com.x5.logistics.repository.Least
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.repository.buildFilterPredicate
import com.x5.logistics.repository.castToDoubleDep
import com.x5.logistics.repository.castToString
import com.x5.logistics.repository.countWithFilter
import com.x5.logistics.repository.dateLiteral
import com.x5.logistics.repository.daysBetween
import com.x5.logistics.repository.getGranularityFrom
import com.x5.logistics.repository.getGranularityLabel
import com.x5.logistics.repository.getGranularityPeriodsQuery
import com.x5.logistics.repository.getGranularityTo
import com.x5.logistics.repository.getTupleCondition
import com.x5.logistics.repository.nullable
import com.x5.logistics.repository.safeDiv
import com.x5.logistics.repository.sumWithFilter
import com.x5.logistics.repository.toDays
import com.x5.logistics.repository.toHours
import com.x5.logistics.repository.toNullable
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.LabeledValue
import com.x5.logistics.rest.dto.isEmpty
import com.x5.logistics.rest.dto.kipfactor.GetKipFactorReportFilterValuesReq
import com.x5.logistics.rest.dto.kipfactor.GetKipFactorReportReq
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportColumn
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportColumnFilter
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportColumnItem
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportItem
import com.x5.logistics.rest.dto.kipfactor.KipFactorReportItemGranularityItem
import com.x5.logistics.rest.exception.ExportTimeoutException
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.rest.util.FilterGroup
import com.x5.logistics.service.settingssheet.ExportRequest
import com.x5.logistics.service.settingssheet.Filter
import com.x5.logistics.service.settingssheet.ReportName
import com.x5.logistics.service.settingssheet.SettingsSheetService
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.debug
import com.x5.logistics.util.getLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.apache.poi.hssf.util.HSSFColor
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.jetbrains.exposed.sql.AbstractQuery
import org.jetbrains.exposed.sql.ColumnSet
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.ExpressionAlias
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.div
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.minus
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.plus
import org.jetbrains.exposed.sql.SqlExpressionBuilder.times
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andHaving
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.sum
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.jetbrains.exposed.sql.unionAll
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.InputStream
import java.time.LocalDate
import java.util.*
import java.util.Collections.synchronizedList
import kotlin.math.roundToLong
import kotlin.reflect.KMutableProperty1
import kotlin.reflect.jvm.isAccessible

private typealias GroupKey = List<Any?>

private class Group(val factor: KipFactorReportFactor, val index: Int)

@Service
class KipFactorReportService(
    val settingsSheetService: SettingsSheetService
) {
    @Value("\${x5.logistics.kipfactor.export-page-size}")
    var EXPORT_PAGE_SIZE: Int = 0
    @Value("\${x5.logistics.kipfactor.parallel-threads}")
    var PARALLEL_THREADS = 4
    @Value("\${x5.logistics.kipfactor.export-timeout}")
    val EXPORT_TIMEOUT = 15 * 60 * 1000

    private companion object {
        private val log = getLogger()
    }

    fun getKipFactorReportColumns(): List<KipFactorReportColumnItem> =
        KipFactorReportColumn.entries.map { column ->
            KipFactorReportColumnItem(
                name = column.label,
                label = column,
                group = column.group,
                filterable = column.filterable
            )
        }

    @ExposedTransactional
    fun getKipFactorReportFilterValues(req: GetKipFactorReportFilterValuesReq): List<LabeledValue> {
        val column = req.request.name
        val requestedFilter = if (req.request.value.isNotEmpty()) {
            listOf(
                KipFactorReportColumnFilter(
                    name = req.request.name,
                    condition = FilterCondition.contain,
                    value = req.request.value
                )
            )
        } else {
            emptyList()
        }
        if (column.group != FilterGroup.string) {
            throw WrongRequestDataException("request.name must be of type string")
        }
        if (!column.filterable) {
            throw WrongRequestDataException("request.name must be filterable")
        }
        val (query, cache) = getQuery(
            req = GetKipFactorReportReq(
                pageSize = Int.MAX_VALUE,
                pageNumber = 0,
                from = req.from,
                to = req.to,
                columns = listOf(column),
                filters = req.filters + requestedFilter,
                geoFilter = req.geoFilter
            ),
            count = false,
            strictFilters = false
        )
        query.withDistinct()
        log.debug {
            "SQL filter query: ${query.prepareSQL(QueryBuilder(false))}"
        }
        return query.asSequence()
            .map { row ->
                row[getExpressionAlias(column, cache)] as String?
            }
            .map {
                LabeledValue(
                    label = it.toString(),
                    value = it
                )
            }
            .toList()
    }

    @ExposedTransactional
    fun getKipFactorReportItems(req: GetKipFactorReportReq): List<KipFactorReportItem> {
        val (query, cache) = getQuery(req, count = false, strictFilters = true)
        req.sort.forEach { sort ->
            val expression = sort.column.getSortExpression(cache)
            query.orderBy(expression, if (sort.asc) SortOrder.ASC else SortOrder.DESC)
        }
        req.columns.asSequence()
            .filter { column ->
                req.sort.none { it.column == column }
            }
            .sorted()
            .forEach { column ->
                query.orderBy(column.getSortExpression(cache))
            }
        query.limit(req.pageSize, req.pageSize.toLong() * req.pageNumber)
        val calcFactorGranularity = KipFactorReportColumn.FACTOR in req.columns && req.granularity != null
        val groupColumns = if (calcFactorGranularity) {
            groupingColumns.asSequence()
                .filter { it in req.columns }
                .toList()
        } else {
            emptyList()
        }
        val groups = mutableMapOf<GroupKey, MutableList<Group>>()

        var index = 0
        log.debug {
            "SQL query: ${query.prepareSQL(QueryBuilder(false))}"
        }
        val items = query.map { row ->
            val factor = cache.factorName?.let { KipFactorReportFactor.valueOf(row[it]) }
            val factorValue = if (factor != null) {
                row[getExpressionAlias(factor, cache)]
            } else {
                null
            }
            if (calcFactorGranularity) {
                val groupKey = groupColumns.map { row[getExpressionAlias(it, cache)] }
                val group = Group(factor!!, index)
                groups.computeIfAbsent(groupKey) { mutableListOf() }.add(group)
            }
            index++
            KipFactorReportItem(
                mr = cache.mrAlias?.let { row[it] },
                atp = cache.atpAlias?.let { row[it] },
                mvz = cache.mvzAlias?.let { row[it] },
                mvzName = cache.mvzNameAlias?.let { row[it] },
                retailNetwork = cache.retailNetworkAlias?.let { row[it] },
                terName = cache.territoryNameAlias?.let { row[it] },
                atpType = cache.atpTypeAlias?.let { row[it] },
                mvzType = cache.mvzTypeAlias?.let { row[it] },
                vehicleId = cache.vehicleIdAlias?.let { row[it] },
                vehicleLicense = cache.vehicleLicenseAlias?.let { row[it] },
                vehicleGroup = cache.vehicleGroupAlias?.let { row[it] },
                vehicleType = cache.vehicleTypeAlias?.let { row[it] },
                vehicleBrand = cache.vehicleBrandAlias?.let { row[it] },
                vehicleModel = cache.vehicleModelAlias?.let { row[it] },
                vehicleTonnage = cache.vehicleTonnageAlias?.let { row[it] },
                vehicleVin = cache.vehicleVinAlias?.let { row[it] },
                vehicleCreateYear = cache.vehicleCreateYearAlias?.let { row[it] },
                vehicleCount = cache.vehicleCountAlias?.let { row[it] },
                ktgShare = cache.ktgShareAlias?.let { row[it] },
                rgShare = cache.rgShareAlias?.let { row[it] },
                kipShare = cache.kipShareAlias?.let { row[it] },
                kipPlan = cache.kipPlanAlias?.let { row[it] },
                ktgHours = cache.ktgHoursAlias?.let { row[it] },
                rgHours = cache.rgHoursAlias?.let { row[it] },
                kipHours = cache.kipHoursAlias?.let { row[it] },
                factorName = factor?.title,
                factorValue = factorValue,
                granularityFactorValue = null
            )
        }
        if (calcFactorGranularity) {
            getAllGranularityPeriods(
                from = req.from,
                to = req.to,
                granularity = req.granularity!!
            ).let { periods ->
                items.forEach { item ->
                    item.granularityFactorValue = periods.map {
                        KipFactorReportItemGranularityItem(
                            label = it.title,
                            partPeriod = it.isPartPeriod,
                            value = null
                        )
                    }
                }
            }
            val (granularityQuery, granularityCache) = getGranularityQuery(req, groupColumns, groups.keys.toList())
            log.debug {
                "SQL granularityQuery: ${granularityQuery.prepareSQL(QueryBuilder(false))}"
            }
            granularityQuery.forEach { row ->
                val groupKey = groupColumns.map { row[getExpressionAlias(it, granularityCache)] }
                groups[groupKey].orEmpty().forEach { group ->
                    items[group.index].granularityFactorValue!!.find {
                        it.label == row[granularityCache.granularityAlias!!]
                    }!!.value = row[getExpressionAlias(group.factor, granularityCache)]
                }
            }
        }
        return items
    }

    @ExposedTransactional
    fun getKipFactorReportCount(req: GetKipFactorReportReq): Long {
        val (query, _) = getQuery(req, count = true, strictFilters = true)
        log.debug {
            "SQL count query: ${query.prepareSQL(QueryBuilder(false))}"
        }
        return query.count()
    }

    @ExposedTransactional
    fun exportKipFactorReportItems(req: GetKipFactorReportReq, username: String?): InputStream {
        val columns = req.columns.asSequence().distinct().toList()
        val startTime = System.currentTimeMillis()
        val timeout = startTime + EXPORT_TIMEOUT

        var totalRows = 0L

        val count = getKipFactorReportCount(req)
        val totalPages = (count + EXPORT_PAGE_SIZE - 1) / EXPORT_PAGE_SIZE

        val out = streamWorkbook {
            val headerStyle = ExcelStyles.Header.style(this)
            val strStyle = ExcelStyles.Text.style(this)
            val numberStyle = ExcelStyles.General.style(this)
            val percentStyle = ExcelStyles.Percent.style(this)
            val coloredStyles = createColoredStyles(this)

            val reportReq = ExportRequest(
                from = req.from,
                to = req.to,
                granularitySupported = true,
                granularityValue = req.granularity?.description,
                userName = username,
                geoFilter = req.geoFilter,
                columns = req.columns.map { it.label },
                filters = req.filters.map { Filter(it.name.label, it.condition.description, it.value) },
                sort = req.sort.map { com.x5.logistics.service.settingssheet.SortOrder(it.column.label, it.asc) }
            )
            settingsSheetService.addSettingsSheet(this, reportReq)
            sheet(ReportName.KIP_FACTOR.title) {
                val greyFont = wb.createFont()
                greyFont.color = HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.index
                val blackFont = wb.createFont()
                blackFont.color = HSSFColor.HSSFColorPredefined.BLACK.index

                val pageNumber = 0
                val items = getKipFactorReportItems(
                    req.copy(
                        pageSize = EXPORT_PAGE_SIZE,
                        pageNumber = pageNumber
                    )
                )
                totalRows += items.size

                val totalItem = getKipFactorReportItems(
                    req.copy(
                        pageSize = 1,
                        pageNumber = 0,
                        granularity = null,
                        columns = listOf(
                            KipFactorReportColumn.VEHICLE_COUNT,
                            KipFactorReportColumn.KTG_SHARE,
                            KipFactorReportColumn.KIP_SHARE
                        ),
                        sort = emptyList()
                    )
                ).firstOrNull()

                header {
                    currStyle = headerStyle
                    columns.forEach { column ->
                        if (column == KipFactorReportColumn.FACTOR) {
                            if (KipFactorReportColumn.FACTOR in req.columns) {
                                if (items.isNotEmpty() && !items[0].granularityFactorValue.isNullOrEmpty()) {
                                    head("Значение фактора по гранулярности") {
                                        items[0].granularityFactorValue!!.forEach {
                                            style {
                                                if (it.partPeriod) {
                                                    setFont(greyFont)
                                                } else {
                                                    setFont(blackFont)
                                                }
                                            }
                                            head(it.label)
                                            style { setFont(blackFont) }
                                        }
                                    }
                                }
                                head("Название фактора")
                                head("Значение фактора")
                            }
                        } else {
                            head(column.label)
                        }
                    }
                }
                val pageQueue = (0 until totalPages).toMutableList()
                val processedRows = synchronizedList(mutableListOf<List<KipFactorReportItem>>())
                runBlocking {
                    val jobs = (1..PARALLEL_THREADS).map { threadNum ->
                        async(Dispatchers.IO) {
                            while (true) {
                                val pageNumber = synchronized(pageQueue) {
                                    if (pageQueue.isEmpty()) null else pageQueue.removeAt(0)
                                } ?: break

                                if (System.currentTimeMillis() > timeout) {
                                    throw ExportTimeoutException("export timeout")
                                }

                                val items = newSuspendedTransaction {
                                    getKipFactorReportItems(
                                        req.copy(
                                            pageSize = EXPORT_PAGE_SIZE,
                                            pageNumber = pageNumber.toInt()
                                        )
                                    )
                                }

                                if (items.isNotEmpty()) {
                                    processedRows.add(items)
                                }

                            }
                        }
                    }
                    jobs.awaitAll()
                }

                processedRows.flatten().forEach { item ->
                    row {
                        columns.forEach { column ->
                            if (column == KipFactorReportColumn.FACTOR) {
                                item.granularityFactorValue?.forEach {
                                    currStyle = numberStyle
                                    cell(it.value)
                                }
                                currStyle = strStyle
                                cell(item.factorName)
                                currStyle = numberStyle
                                cell(item.factorValue)
                            } else {
                                currStyle = when (column.group) {
                                    FilterGroup.string -> strStyle
                                    FilterGroup.stringSearch -> strStyle
                                    FilterGroup.number -> numberStyle
                                    else -> error("column.group = ${column.group}")
                                }
                                cell(
                                    when (column) {
                                        KipFactorReportColumn.MR -> item.mr
                                        KipFactorReportColumn.ATP -> item.atp
                                        KipFactorReportColumn.MVZ -> item.mvz
                                        KipFactorReportColumn.MVZ_NAME -> item.mvzName
                                        KipFactorReportColumn.RETAIL_NETWORK -> item.retailNetwork
                                        KipFactorReportColumn.ATP_TYPE -> item.atpType
                                        KipFactorReportColumn.MVZ_TYPE -> item.mvzType
                                        KipFactorReportColumn.VEHICLE_ID -> item.vehicleId
                                        KipFactorReportColumn.VEHICLE_LICENSE -> item.vehicleLicense
                                        KipFactorReportColumn.VEHICLE_VIN -> item.vehicleVin
                                        KipFactorReportColumn.VEHICLE_GROUP -> item.vehicleGroup
                                        KipFactorReportColumn.VEHICLE_TYPE -> item.vehicleType
                                        KipFactorReportColumn.VEHICLE_BRAND -> item.vehicleBrand
                                        KipFactorReportColumn.VEHICLE_MODEL -> item.vehicleModel
                                        KipFactorReportColumn.VEHICLE_TONNAGE -> item.vehicleTonnage
                                        KipFactorReportColumn.VEHICLE_CREATE_YEAR -> item.vehicleCreateYear
                                        KipFactorReportColumn.VEHICLE_COUNT -> item.vehicleCount
                                        KipFactorReportColumn.KTG_SHARE -> {
                                            currStyle =
                                                coloredStyles.findColoredStyle(item.ktgShare?.roundTo2Points)
                                            item.ktgShare?.roundTo2Points
                                        }

                                        KipFactorReportColumn.RG_SHARE -> {
                                            currStyle = coloredStyles.findColoredStyle(item.rgShare?.roundTo2Points)
                                            item.rgShare?.roundTo2Points
                                        }

                                        KipFactorReportColumn.KIP_SHARE -> {
                                            currStyle =
                                                coloredStyles.findColoredStyle(item.kipShare?.roundTo2Points)
                                            item.kipShare?.roundTo2Points
                                        }

                                        KipFactorReportColumn.KIP_PLAN -> item.kipPlan
                                        KipFactorReportColumn.KTG_HOURS -> item.ktgHours
                                        KipFactorReportColumn.RG_HOURS -> item.rgHours
                                        KipFactorReportColumn.KIP_HOURS -> item.kipHours
                                        KipFactorReportColumn.TERRITORY_NAME -> item.terName
                                        else -> error("column: $column")
                                    }
                                )
                            }
                        }
                    }
                    totalRows++
                }

                row {
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Всего строк:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(count)
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("Ср. кол-во ТС:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalItem?.vehicleCount ?: 0.0)
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("КТГ:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalItem?.ktgShare?.div(100.0) ?: 0.0).cellStyle = percentStyle
                    style { alignment = HorizontalAlignment.RIGHT }
                    cell("КИП:")
                    style { alignment = HorizontalAlignment.LEFT }
                    cell(totalItem?.kipShare?.div(100.0) ?: 0.0).cellStyle = percentStyle
                }
            }.autosize()
            setActiveSheet(ReportName.KIP_FACTOR.title)
        }
        val endTime = System.currentTimeMillis()
        val duration = (endTime - startTime) / 1000.0 // в секундах

        log.info(
            "KIP Factor report export completed. " +
                    "Rows: {}. Duration: {} seconds. " +
                    "Speed: {} rows/second. Username: {}",
            totalRows,
            duration,
            totalRows / duration,
            username ?: "unknown"
        )

        return out.toInputStream()
    }

    private fun Map<Double?, CellStyle>.findColoredStyle(value: Double?): CellStyle {
        if (value == null) return getValue(null)
        return getValue(keys.asSequence().filterNotNull().filter { value >= it }.max())
    }

    private fun createColoredStyles(wb: WbBuilder) =
        buildMap(colorBoundaries.size + 1) {
            colorBoundaries.forEach { (boundary, color) ->
                put(boundary, ExcelStyles.General.style(wb).apply {
                    val customColor = wb.wb.creationHelper.createExtendedColor()
                    customColor.argbHex = color
                    setFillForegroundColor(customColor)
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                })
            }
            put(null, ExcelStyles.General.style(wb))
        }

    private val colorBoundaries = mapOf(
        90.0 to "FFFFFFFF",
        80.0 to "FFFFD2D2",
        70.0 to "FFFFA49E",
        60.0 to "FFFB7B6F",
        Double.NEGATIVE_INFINITY to "FFF45948"
    )

    private fun getAllGranularityPeriods(
        from: LocalDate,
        to: LocalDate,
        granularity: Granularity
    ): List<GranularityPeriod> {
        val query = getGranularityPeriodsQuery(
            granularity = granularity,
            from = from,
            to = to
        )
        return query.query.map {
            GranularityPeriod(
                title = it[query.label],
                isPartPeriod = it[query.partPeriod]
            )
        }
    }

    private data class GranularityPeriod(val title: String, val isPartPeriod: Boolean)

    private fun getGranularityQuery(
        req: GetKipFactorReportReq,
        groupColumns: List<KipFactorReportColumn>,
        groupKeys: List<GroupKey>
    ): Pair<Query, AliasesCache> {
        val cache = AliasesCache(
            from = Greatest(dateLiteral(req.from), getGranularityFrom(req.granularity!!, KipTable.vehicleDate)),
            to = Least(dateLiteral(req.to), getGranularityTo(req.granularity, KipTable.vehicleDate))
        )
        cache.granularityAlias = getGranularityLabel(req.granularity, KipTable.vehicleDate).alias("granularity")
        val selectColumns = (groupColumns.asSequence() + KipFactorReportColumn.FACTOR).toSet()
        val query = cache.slice(getTables(req), selectColumns)
        query.andWhere { KipTable.vehicleDate.between(req.from, req.to) }
        query.applyGeoFilter(req.geoFilter)
        req.filters.whereFilters
            .filter { it.name !in groupColumns }
            .forEach { filter ->
                query.andWhere {
                    buildFilterPredicate(
                        condition = filter.condition,
                        values = filter.value,
                        type = getExpressionType(filter.name),
                        exposedExpression = getExpression(filter.name, cache)
                    )
                }
            }
        query.andWhere {
            getTupleCondition(groupColumns.map { getExpression(it, cache) }, groupKeys)
        }
        req.columns.asSequence()
            .filter { it in groupingColumns }
            .distinct()
            .map { getExpression(it, cache) }
            .forEach { query.groupBy(it) }
        query.groupBy(
            cache.granularityAlias!!.delegate,
            getGranularityFrom(req.granularity, KipTable.vehicleDate),
            getGranularityTo(req.granularity, KipTable.vehicleDate)
        )
        cache.cutColumns(selectColumns)
        return query to cache
    }

    private fun getTables(req: GetKipFactorReportReq): ColumnSet {
        val allColumns = (
                req.columns.asSequence() + req.sort.asSequence().map { it.column } +
                        req.filters.asSequence().map { it.name } +
                        if (req.geoFilter.isEmpty) emptySequence() else sequenceOf(KipFactorReportColumn.MR)
                ).toSet()
        var tables: ColumnSet = KipTable
        if (orgUnitColumns.any { it in allColumns }) {
            tables = tables.join(
                otherTable = OrganizationalUnitsTimelineTable,
                joinType = JoinType.INNER,
                additionalConstraint = {
                    (KipTable.mvzId eq OrganizationalUnitsTimelineTable.mvzId) and
                            (KipTable.vehicleDate greaterEq OrganizationalUnitsTimelineTable.startDate) and
                            (KipTable.vehicleDate less OrganizationalUnitsTimelineTable.endDate)
                }
            )
        }
        if (KipFactorReportColumn.FACTOR in req.columns) {
            tables = tables.join(
                otherTable = TsReadyHistTable,
                joinType = JoinType.LEFT,
                additionalConstraint = {
                    (KipTable.equnr eq TsReadyHistTable.equnr) and
                            (KipTable.vehicleDate greaterEq TsReadyHistTable.startDate) and
                            (KipTable.vehicleDate less TsReadyHistTable.endDate)
                }
            )
        }
        return tables
    }

    private fun getQuery(
        req: GetKipFactorReportReq,
        count: Boolean,
        strictFilters: Boolean
    ): Pair<Query, AliasesCache> {
        val cache = AliasesCache(
            from = dateLiteral(req.from),
            to = dateLiteral(req.to)
        )
        val tables = getTables(req)
        var query = if (count && KipFactorReportColumn.FACTOR !in req.columns) {
            tables.select(intLiteral(1))
        } else {
            cache.slice(tables, req.columns)
        }
        query.andWhere { KipTable.vehicleDate.between(req.from, req.to) }
        query.applyGeoFilter(req.geoFilter)
        req.filters.whereFilters
            .forEach { filter ->
                query.andWhere {
                    buildFilterPredicate(
                        condition = filter.condition,
                        values = filter.value,
                        type = getExpressionType(filter.name),
                        exposedExpression = getExpression(filter.name, cache),
                        strictFilter = strictFilters
                    )
                }
            }
        req.columns.asSequence()
            .filter { it in groupingColumns }
            .distinct()
            .map { getExpression(it, cache) }
            .forEach { query.groupBy(it) }
        req.filters.havingFilters
            .forEach { filter ->
                query.andHaving {
                    buildFilterPredicate(
                        condition = filter.condition,
                        values = filter.value,
                        type = getExpressionType(filter.name),
                        exposedExpression = getExpression(filter.name, cache)
                    )
                }
            }
        cache.cutColumns(req.columns)
        if (KipFactorReportColumn.FACTOR in req.columns) {
            val mainQuery = query.alias("main")
            cache.wrap(mainQuery)
            var factorName: ExpressionAlias<String>? = null
            var factorTitle: ExpressionAlias<String>? = null
            var factorOrdinal: ExpressionAlias<Int>? = null
            val factorsSubQuery = KipFactorReportFactor.entries.asSequence()
                .mapIndexed { index, factor ->
                    val name = stringLiteral(factor.name).alias("namef")
                    val title = stringLiteral(factor.title).alias("title")
                    val ordinal = intLiteral(index).alias("ordinal")
                    if (index == 0) {
                        factorName = name
                        factorTitle = title
                        factorOrdinal = ordinal
                    }
                    UnitTable.select(name, title, ordinal)
                }
                .reduce(AbstractQuery<*>::unionAll).alias("factors1").let { factors1 ->
                    factorName = factors1[factorName!!].alias("namef")
                    factorTitle = factors1[factorTitle!!].alias("title")
                    factorOrdinal = factors1[factorOrdinal!!].alias("ordinal")
                    factors1.select(factorName!!, factorTitle!!, factorOrdinal!!)
                }
            req.filters.factorFilters.forEach { filter ->
                factorsSubQuery.andWhere {
                    buildFilterPredicate(
                        condition = filter.condition,
                        values = filter.value,
                        type = ColumnType.STRING,
                        exposedExpression = factorTitle!!.delegate,
                        strictFilter = strictFilters
                    )
                }
            }
            val factorsQuery = factorsSubQuery.alias("factor")
            val factorNameExp = factorsQuery[factorName!!]
            val factorTitleExp = factorsQuery[factorTitle!!]
            val factorOrdinalExp = factorsQuery[factorOrdinal!!]
            cache.factorName = factorNameExp.alias("factorName")
            cache.factorTitle = factorTitleExp.alias("factorTitle")
            cache.factorOrdinal = factorOrdinalExp.alias("factorOrdinal")
            val newTables = mainQuery.join(
                otherTable = factorsQuery,
                joinType = JoinType.INNER,
                additionalConstraint = {
                    KipFactorReportFactor.entries.asSequence()
                        .map { factor ->
                            val factorCondition = factorOrdinalExp eq intLiteral(factor.ordinal)
                            if (factor.required) {
                                factorCondition
                            } else {
                                val factorValueExp = getExpressionAlias(factor, cache).delegate.castToDoubleDep()
                                factorCondition and factorValueExp.isNotNull() and (factorValueExp neq doubleLiteral(0.0))
                            }
                        }
                        .reduce { op1, op2 -> op1 or op2 }
                }
            )
            query = if (count) {
                newTables.select(intLiteral(1))
            } else {
                cache.slice(newTables, req.columns)
            }
        }
        return query to cache
    }
}

private fun KipFactorReportColumn.getSortExpression(cache: AliasesCache): Expression<out Any?> =
    if (this == KipFactorReportColumn.FACTOR) {
        cache.factorOrdinal?.delegate ?: throw WrongRequestDataException("column factor is not in the request")
    } else {
        getExpression(this, cache)
    }

private class AliasesCache(
    from: Expression<LocalDate>,
    to: Expression<LocalDate>
) {
    var mrAlias = OrganizationalUnitsTimelineTable.mrName.alias("mr").nullable
    var atpAlias = OrganizationalUnitsTimelineTable.atpName.alias("atp").nullable
    var mvzAlias = OrganizationalUnitsTimelineTable.mvzId.alias("mvz").nullable
    var mvzNameAlias = OrganizationalUnitsTimelineTable.mvzName.alias("mvzName").nullable
    var retailNetworkAlias = OrganizationalUnitsTimelineTable.retailNetwork.alias("retailNetwork").nullable
    var atpTypeAlias = OrganizationalUnitsTimelineTable.atpType.alias("atpType").nullable
    var mvzTypeAlias = OrganizationalUnitsTimelineTable.mvzType.alias("mvzType").nullable
    var territoryNameAlias = OrganizationalUnitsTimelineTable.territoryName.alias("terName").nullable

    var vehicleIdAlias = KipTable.equnr.castToString().alias("vehicleId").nullable
    var vehicleLicenseAlias = KipTable.vehicleLicense.alias("vehicleLicense").nullable
    var vehicleGroupAlias = KipTable.vehicleGroup.alias("vehicleGroup").nullable
    var vehicleTypeAlias = KipTable.vehicleType.alias("vehicleType").nullable
    var vehicleBrandAlias = KipTable.vehicleBrand.alias("vehicleBrand").nullable
    var vehicleModelAlias = KipTable.vehicleModel.alias("vehicleModel").nullable
    var vehicleTonnageAlias = KipTable.vehicleTonnage.alias("vehicleTonnage").nullable
    var vehicleVinAlias = KipTable.vehicleVin.alias("vehicleVin").nullable
    var vehicleCreateYearAlias = KipTable.vehicleCreateYear.alias("vehicleCreateYear").nullable

    var vehicleCountAlias = (CountStar.castToDoubleDep() / (daysBetween(from, to) + 1).castToDoubleDep())
        .alias("vehicleCount").nullable
    var ktgShareAlias =
        ((countWithFilter(KipTable.ktg).castToDoubleDep() safeDiv CountStar.castToDoubleDep()) * 100.0).alias("ktgShare")
            .nullable
    var rgShareAlias =
        ((countWithFilter(KipTable.rg).castToDoubleDep() safeDiv CountStar.castToDoubleDep()) * 100.0).alias("rgShare")
            .nullable
    var kipShareAlias =
        ((KipTable.kip.sum().toDays() safeDiv CountStar.castToDoubleDep()) * 100.0).alias("kipShare").nullable
    var kipPlanAlias = (CountStar * 24).alias("kipPlan").nullable
    var ktgHoursAlias = (countWithFilter(KipTable.ktg) * 24).alias("ktgHours").nullable
    var rgHoursAlias = (countWithFilter(KipTable.rg) * 24).alias("rgHours").nullable
    var kipHoursAlias = KipTable.kip.sum().toHours().alias("kipHours").nullable

    var avgVehicleCountFactorAlias = (CountStar.castToDoubleDep() / (daysBetween(from, to) + 1).castToDoubleDep())
        .alias("avgVehicleCountFactor").nullable
    var kipPlanFactorAlias = (CountStar * 24).alias("kipPlanFactor").nullable
    var notTgFactorAlias = (countWithFilter(KipTable.vehicleStatus.isNull()) * -24).alias("notTgFactor").nullable
    var repair1dFactorAlias = (
            countWithFilter(
                (KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 2) and
                        (daysBetween(TsReadyHistTable.startDate, TsReadyHistTable.endDate) less 2)
            ) * -24
            ).alias("repair1dFactor").nullable
    var repair7dFactorAlias = (
            countWithFilter(
                (KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 2) and
                        (daysBetween(TsReadyHistTable.startDate, TsReadyHistTable.endDate) less 7) and
                        (daysBetween(TsReadyHistTable.startDate, TsReadyHistTable.endDate) greaterEq 2)
            ) * -24
            ).alias("repair7dFactor").nullable
    var repairMore7dFactorAlias = (
            countWithFilter(
                (KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 2) and
                        (daysBetween(TsReadyHistTable.startDate, TsReadyHistTable.endDate) greaterEq 7)
            ) * -24
            ).alias("repairMore7dFactor").nullable
    var repairAccidentFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 3)) * -24
            ).alias("repairAccidentFactor").nullable
    var ktgFactorAlias = (countWithFilter(KipTable.ktg) * 24).alias("ktgFactor").nullable
    var noDriverFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 19)) * -24
            ).alias("noDriverFactor").nullable
    var commerceFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 20)) * -24
            ).alias("commerceFactor").nullable
    var downtime50_50FactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 18)) * -24
            ).alias("downtime50_50Factor").nullable
    var reserveFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 9)) * -24
            ).alias("reserveFactor").nullable
    var passIsBlockedFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 4)) * -24
            ).alias("passIsBlockedFactor").nullable
    var otherFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 5)) * -24
            ).alias("otherFactor").nullable
    var passIsOutdatedFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 6)) * -24
            ).alias("passIsOutdatedFactor").nullable
    var policyOsagoIsInvalidFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 7)) * -24
            ).alias("policyOsagoIsInvalidFactor").nullable
    var policyCascoIsInvalidFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 8)) * -24
            ).alias("policyCascoIsInvalidFactor").nullable
    var incidentBeforeRegistrationFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 10)) * -24
            ).alias("incidentBeforeRegistrationFactor").nullable
    var incidentServiceFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 11)) * -24
            ).alias("incidentServiceFactor").nullable
    var businessTripFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 12)) * -24
            ).alias("businessTripFactor").nullable
    var incorrectLinkageFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 13)) * -24
            ).alias("incorrectLinkageFactor").nullable
    var tachographIsInvalidFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 14)) * -24
            ).alias("tachographIsInvalidFactor").nullable
    var tachographIsOutdatedFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 15)) * -24
            ).alias("tachographIsOutdatedFactor").nullable
    var platonIsInvalidFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 16)) * -24
            ).alias("platonIsInvalidFactor").nullable
    var platonIsOutdatedFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 17)) * -24
            ).alias("platonIsOutdatedFactor").nullable
    var overPlanFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 21)) * -24
            ).alias("overPlanFactor").nullable
    var rgFactorAlias = (countWithFilter(KipTable.rg) * 24).alias("rgFactor").nullable
    var notReadyFactorAlias = (
            countWithFilter(
                (KipTable.vehicleStatus eq "5C") and (
                        (KipTable.vehicleReason neq 1) or KipTable.vehicleReason.isNull()
                        )
            ) * -24
            ).alias("notReadyFactor").nullable
    var readyIncludeRaceFactorAlias = (
            countWithFilter(
                (KipTable.vehicleStatus eq "5B") or (
                        (KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 1)
                        )
            ) * 24
            ).alias("readyIncludeRaceFactor").nullable
    var inRaceFactorAlias = (
            countWithFilter((KipTable.vehicleStatus eq "5C") and (KipTable.vehicleReason eq 1)) * 24
            ).alias("inRaceFactor").nullable
    var readyNoPlFactorAlias = ((
            (countWithFilter(KipTable.vehicleStatus eq "5B") * 24).castToDoubleDep().toNullable() -
                    sumWithFilter(KipTable.vehicleStatus eq "5B", KipTable.kip.toHours()) -
                    sumWithFilter(KipTable.vehicleStatus eq "5B", KipTable.repairWb.toHours())
            ) * -1.0).alias("readyNoPlFactor").nullable
    var notReadyPlIsFactorAlias = sumWithFilter(
        KipTable.vehicleStatus.isNull() or
                (KipTable.vehicleStatus eq "5C" and
                        (KipTable.vehicleReason.isNull() or (KipTable.vehicleReason neq 1))),
        KipTable.kip.toHours()
    )
        .alias("notReadyPlIsFactor").nullable
    var kipFactFactorAlias = KipTable.kip.toHours().sum().alias("kipFactFactor").nullable
    var repairPlFactorAlias = (KipTable.repairWb.toHours().sum() * -1.0).alias("repairPlFactor").nullable

    var factorName: ExpressionAlias<String>? = null
    var factorTitle: ExpressionAlias<String>? = null
    var factorOrdinal: ExpressionAlias<Int>? = null
    var granularityAlias: ExpressionAlias<String>? = null

    @Suppress("UNCHECKED_CAST")
    fun wrap(subquery: QueryAlias) {
        KipFactorReportColumn.entries.asSequence()
            .flatMap(::getExpressionProperties)
            .forEach { property ->
                property.isAccessible = true
                val value = property.get(this)
                if (value != null) {
                    val newValue = subquery[value].alias(value.alias)
                    (property as KMutableProperty1<AliasesCache, ExpressionAlias<*>?>).set(this, newValue)
                }
            }
    }

    fun slice(tables: ColumnSet, columns: Collection<KipFactorReportColumn>): Query =
        tables.select(
            columns.asSequence()
                .distinct()
                .flatMap(::getExpressionProperties)
                .mapNotNull { it.get(this) }
                .toList()
        )

    @Suppress("UNCHECKED_CAST")
    fun cutColumns(columns: Collection<KipFactorReportColumn>) {
        KipFactorReportColumn.entries.asSequence()
            .filter { it !in columns }
            .flatMap(::getExpressionProperties)
            .forEach { property ->
                property.isAccessible = true
                (property as KMutableProperty1<AliasesCache, ExpressionAlias<*>?>).set(this, null)
            }
    }
}

private enum class KipFactorReportFactor(val title: String, val required: Boolean = false) {
    AVG_VEHICLE_COUNT("Ср. кол-во ТС, шт.", true),
    KIP_PLAN("КИП план, мч", true),
    NOT_TG("Не подано в ТГ, мч"),
    REPAIR_1D("Ремонт 1 день, мч"),
    REPAIR_7D("Ремонт до 7 дней, мч"),
    REPAIR_MORE_7D("Долгострой (7 и более дней), мч"),
    REPAIR_ACCIDENT("Ремонт ДТП, мч"),
    KTG("КТГ, мч", true),
    NO_DRIVER("Нет водителя, мч"),
    COMMERCE("Коммерция, мч"),
    DOWNTIME_50_50("Простой 50/50, мч"),
    RESERVE("Резерв, мч"),
    PASS_IS_BLOCKED("Пропуск блокирован, мч"),
    OTHER("Прочее, мч"),
    PASS_IS_OUTDATED("Пропуск просрочен, мч"),
    POLICY_OSAGO_IS_INVALID("Полис ОСАГО недействителен, мч"),
    POLICY_CASCO_IS_INVALID("Полис КАСКО недействителен, мч"),
    INCIDENT_BEFORE_REGISTRATION("ДТП до оформления, мч"),
    INCIDENT_SERVICE("ДТП заявка в сервис, мч"),
    BUSINESS_TRIP("Командировка, мч"),
    INCORRECT_LINKAGE("Некорректная сцепка, мч"),
    TACHOGRAPH_IS_INVALID("Тахограф недействителен, мч"),
    TACHOGRAPH_IS_OUTDATED("Истекает срок действия тахографа, мч"),
    PLATON_IS_INVALID("БУ Платон недействителен, требуется оформл. маршрутной карты, мч"),
    PLATON_IS_OUTDATED("Истекает срок БУ Платон, мч"),
    OVER_PLAN("Сверх плана, мч"),
    RG("РГ, мч", true),
    NOT_READY("Не готов, мч", true),
    READY_INCLUDE_RACE("Готов, в т.ч. В рейсе, мч"),
    IN_RACE("В рейсе, мч"),
    READY_NO_PL("Готов, нет ПЛ, мч"),
    NOT_READY_PL_IS("Не готов, есть ПЛ, мч"),
    KIP_FACT("КИП факт, мч", true),
    REPAIR_PL("Рем. ПЛ, мч")
}

private val orgUnitColumns = setOf(
    KipFactorReportColumn.TERRITORY_NAME,
    KipFactorReportColumn.MR,
    KipFactorReportColumn.ATP,
    KipFactorReportColumn.MVZ,
    KipFactorReportColumn.MVZ_NAME,
    KipFactorReportColumn.RETAIL_NETWORK,
    KipFactorReportColumn.ATP_TYPE,
    KipFactorReportColumn.MVZ_TYPE
)

private val groupingColumns = setOf(
    KipFactorReportColumn.TERRITORY_NAME,
    KipFactorReportColumn.MR,
    KipFactorReportColumn.ATP,
    KipFactorReportColumn.MVZ,
    KipFactorReportColumn.MVZ_NAME,
    KipFactorReportColumn.RETAIL_NETWORK,
    KipFactorReportColumn.ATP_TYPE,
    KipFactorReportColumn.MVZ_TYPE,
    KipFactorReportColumn.VEHICLE_ID,
    KipFactorReportColumn.VEHICLE_LICENSE,
    KipFactorReportColumn.VEHICLE_VIN,
    KipFactorReportColumn.VEHICLE_GROUP,
    KipFactorReportColumn.VEHICLE_TYPE,
    KipFactorReportColumn.VEHICLE_BRAND,
    KipFactorReportColumn.VEHICLE_MODEL,
    KipFactorReportColumn.VEHICLE_TONNAGE,
    KipFactorReportColumn.VEHICLE_CREATE_YEAR,
)

private val List<KipFactorReportColumnFilter>.whereFilters: Sequence<KipFactorReportColumnFilter>
    get() = asSequence().filter { it.name in groupingColumns }

private val List<KipFactorReportColumnFilter>.havingFilters: Sequence<KipFactorReportColumnFilter>
    get() = asSequence().filter { it.name != KipFactorReportColumn.FACTOR && it.name !in groupingColumns }

private val List<KipFactorReportColumnFilter>.factorFilters: Sequence<KipFactorReportColumnFilter>
    get() = asSequence().filter { it.name == KipFactorReportColumn.FACTOR }

private fun getFactorProperty(factor: KipFactorReportFactor): KMutableProperty1<AliasesCache, out ExpressionAlias<*>?> =
    when (factor) {
        KipFactorReportFactor.AVG_VEHICLE_COUNT -> AliasesCache::avgVehicleCountFactorAlias
        KipFactorReportFactor.KIP_PLAN -> AliasesCache::kipPlanFactorAlias
        KipFactorReportFactor.NOT_TG -> AliasesCache::notTgFactorAlias
        KipFactorReportFactor.REPAIR_1D -> AliasesCache::repair1dFactorAlias
        KipFactorReportFactor.REPAIR_7D -> AliasesCache::repair7dFactorAlias
        KipFactorReportFactor.REPAIR_MORE_7D -> AliasesCache::repairMore7dFactorAlias
        KipFactorReportFactor.REPAIR_ACCIDENT -> AliasesCache::repairAccidentFactorAlias
        KipFactorReportFactor.KTG -> AliasesCache::ktgFactorAlias
        KipFactorReportFactor.NO_DRIVER -> AliasesCache::noDriverFactorAlias
        KipFactorReportFactor.COMMERCE -> AliasesCache::commerceFactorAlias
        KipFactorReportFactor.DOWNTIME_50_50 -> AliasesCache::downtime50_50FactorAlias
        KipFactorReportFactor.RESERVE -> AliasesCache::reserveFactorAlias
        KipFactorReportFactor.PASS_IS_BLOCKED -> AliasesCache::passIsBlockedFactorAlias
        KipFactorReportFactor.OTHER -> AliasesCache::otherFactorAlias
        KipFactorReportFactor.PASS_IS_OUTDATED -> AliasesCache::passIsOutdatedFactorAlias
        KipFactorReportFactor.POLICY_OSAGO_IS_INVALID -> AliasesCache::policyOsagoIsInvalidFactorAlias
        KipFactorReportFactor.POLICY_CASCO_IS_INVALID -> AliasesCache::policyCascoIsInvalidFactorAlias
        KipFactorReportFactor.INCIDENT_BEFORE_REGISTRATION -> AliasesCache::incidentBeforeRegistrationFactorAlias
        KipFactorReportFactor.INCIDENT_SERVICE -> AliasesCache::incidentServiceFactorAlias
        KipFactorReportFactor.BUSINESS_TRIP -> AliasesCache::businessTripFactorAlias
        KipFactorReportFactor.INCORRECT_LINKAGE -> AliasesCache::incorrectLinkageFactorAlias
        KipFactorReportFactor.TACHOGRAPH_IS_INVALID -> AliasesCache::tachographIsInvalidFactorAlias
        KipFactorReportFactor.TACHOGRAPH_IS_OUTDATED -> AliasesCache::tachographIsOutdatedFactorAlias
        KipFactorReportFactor.PLATON_IS_INVALID -> AliasesCache::platonIsInvalidFactorAlias
        KipFactorReportFactor.PLATON_IS_OUTDATED -> AliasesCache::platonIsOutdatedFactorAlias
        KipFactorReportFactor.OVER_PLAN -> AliasesCache::overPlanFactorAlias
        KipFactorReportFactor.RG -> AliasesCache::rgFactorAlias
        KipFactorReportFactor.NOT_READY -> AliasesCache::notReadyFactorAlias
        KipFactorReportFactor.READY_INCLUDE_RACE -> AliasesCache::readyIncludeRaceFactorAlias
        KipFactorReportFactor.IN_RACE -> AliasesCache::inRaceFactorAlias
        KipFactorReportFactor.READY_NO_PL -> AliasesCache::readyNoPlFactorAlias
        KipFactorReportFactor.NOT_READY_PL_IS -> AliasesCache::notReadyPlIsFactorAlias
        KipFactorReportFactor.KIP_FACT -> AliasesCache::kipFactFactorAlias
        KipFactorReportFactor.REPAIR_PL -> AliasesCache::repairPlFactorAlias
    }

private fun getExpressionProperties(column: KipFactorReportColumn): Sequence<KMutableProperty1<AliasesCache, out ExpressionAlias<*>?>> =
    when (column) {
        KipFactorReportColumn.MR -> sequenceOf(AliasesCache::mrAlias)
        KipFactorReportColumn.ATP -> sequenceOf(AliasesCache::atpAlias)
        KipFactorReportColumn.MVZ -> sequenceOf(AliasesCache::mvzAlias)
        KipFactorReportColumn.MVZ_NAME -> sequenceOf(AliasesCache::mvzNameAlias)
        KipFactorReportColumn.RETAIL_NETWORK -> sequenceOf(AliasesCache::retailNetworkAlias)
        KipFactorReportColumn.ATP_TYPE -> sequenceOf(AliasesCache::atpTypeAlias)
        KipFactorReportColumn.MVZ_TYPE -> sequenceOf(AliasesCache::mvzTypeAlias)
        KipFactorReportColumn.VEHICLE_ID -> sequenceOf(AliasesCache::vehicleIdAlias)
        KipFactorReportColumn.VEHICLE_LICENSE -> sequenceOf(AliasesCache::vehicleLicenseAlias)
        KipFactorReportColumn.VEHICLE_GROUP -> sequenceOf(AliasesCache::vehicleGroupAlias)
        KipFactorReportColumn.VEHICLE_BRAND -> sequenceOf(AliasesCache::vehicleBrandAlias)
        KipFactorReportColumn.VEHICLE_MODEL -> sequenceOf(AliasesCache::vehicleModelAlias)
        KipFactorReportColumn.VEHICLE_CREATE_YEAR -> sequenceOf(AliasesCache::vehicleCreateYearAlias)
        KipFactorReportColumn.VEHICLE_VIN -> sequenceOf(AliasesCache::vehicleVinAlias)
        KipFactorReportColumn.VEHICLE_TYPE -> sequenceOf(AliasesCache::vehicleTypeAlias)
        KipFactorReportColumn.VEHICLE_TONNAGE -> sequenceOf(AliasesCache::vehicleTonnageAlias)
        KipFactorReportColumn.VEHICLE_COUNT -> sequenceOf(AliasesCache::vehicleCountAlias)
        KipFactorReportColumn.KTG_SHARE -> sequenceOf(AliasesCache::ktgShareAlias)
        KipFactorReportColumn.RG_SHARE -> sequenceOf(AliasesCache::rgShareAlias)
        KipFactorReportColumn.KIP_SHARE -> sequenceOf(AliasesCache::kipShareAlias)
        KipFactorReportColumn.KIP_PLAN -> sequenceOf(AliasesCache::kipPlanAlias)
        KipFactorReportColumn.KTG_HOURS -> sequenceOf(AliasesCache::ktgHoursAlias)
        KipFactorReportColumn.RG_HOURS -> sequenceOf(AliasesCache::rgHoursAlias)
        KipFactorReportColumn.KIP_HOURS -> sequenceOf(AliasesCache::kipHoursAlias)
        KipFactorReportColumn.TERRITORY_NAME -> sequenceOf(AliasesCache::territoryNameAlias)
        KipFactorReportColumn.FACTOR -> sequenceOf(
            AliasesCache::factorTitle,
            AliasesCache::factorName,
            AliasesCache::factorOrdinal,
            AliasesCache::granularityAlias
        ) + KipFactorReportFactor.entries.asSequence().map(::getFactorProperty)
    }

private fun getExpressionAlias(column: KipFactorReportColumn, cache: AliasesCache): ExpressionAlias<*> =
    getExpressionProperties(column)
        .map {
            it.isAccessible = true
            it.get(cache)
        }.firstOrNull() ?: throw WrongRequestDataException("selected column is not found")

private fun getExpressionAlias(factor: KipFactorReportFactor, cache: AliasesCache): ExpressionAlias<*> {
    val property = getFactorProperty(factor)
    property.isAccessible = true
    return property.get(cache)!!
}

private fun getExpression(column: KipFactorReportColumn, cache: AliasesCache): Expression<*> =
    getExpressionAlias(column, cache).delegate

private fun getExpressionType(column: KipFactorReportColumn): ColumnType =
    when (column) {
        KipFactorReportColumn.VEHICLE_CREATE_YEAR -> ColumnType.NUMBER
        KipFactorReportColumn.VEHICLE_TONNAGE -> ColumnType.NUMBER
        KipFactorReportColumn.VEHICLE_COUNT -> ColumnType.NUMBER
        KipFactorReportColumn.KTG_SHARE -> ColumnType.NUMBER
        KipFactorReportColumn.RG_SHARE -> ColumnType.NUMBER
        KipFactorReportColumn.KIP_SHARE -> ColumnType.NUMBER
        KipFactorReportColumn.KIP_PLAN -> ColumnType.NUMBER
        KipFactorReportColumn.KTG_HOURS -> ColumnType.NUMBER
        KipFactorReportColumn.RG_HOURS -> ColumnType.NUMBER
        KipFactorReportColumn.KIP_HOURS -> ColumnType.NUMBER
        else -> ColumnType.STRING
    }

private val Double.roundTo2Points: Double
    get() = (this * 100.0).roundToLong() / 100.0
