package com.x5.logistics.service

import com.x5.logistics.repository.hr.StaffingReportRepo
import com.x5.logistics.rest.dto.hr.report.staffing.Granularity
import com.x5.logistics.rest.dto.hr.report.staffing.ReportEntry
import com.x5.logistics.rest.dto.hr.report.staffing.StaffingReport
import com.x5.logistics.rest.dto.hr.report.staffing.StaffingReportReq
import com.x5.logistics.util.ExcelStyles
import com.x5.logistics.util.getLogger
import org.apache.poi.ss.usermodel.FillPatternType
import org.apache.poi.ss.usermodel.Font
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.IndexedColors
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.ss.usermodel.Workbook
import org.springframework.stereotype.Service
import java.io.InputStream
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.IsoFields
import java.time.temporal.TemporalAdjusters
import kotlin.math.roundToInt
import kotlin.text.Typography.nbsp

@Service
class StaffingReportService(
    val repo: StaffingReportRepo
) {
    private val log by lazy { getLogger() }
    private lateinit var boldFont: Font
    private lateinit var settingsFont: Font
    private lateinit var headerFont: Font

    private val formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val monthFormatter = DateTimeFormatter.ofPattern("MM.yyyy")

    private val legend = listOf(
        Legend("Макрорегион", "МР из данных портала", true),
        Legend("Итоговое подразделение", "Площадки из справочника площадок и итоговых подразделений"),
        Legend("HR площадка", "Реальная локация, физическое место, где стоят ТС и работают водители."),
        Legend("Вид деятельности АТП", "Признак \"Вид деятельности\" АТП из данных портала"),
        Legend("Торговая сеть АТП", "Свойство АТП"),
        Legend("Кол-во ТС с учетом аварийности", "Формула: (Все ТС, кроме резервных и к списанию) * (План КТГ 0,95)"),
        Legend(
            "Плановое число водителей на одно ТС",
            "Количество водителей по плану в АТП, данные из справочника «План. числ. водит.»"
        ),
        Legend(
            "Кол-во водителей на одно ТС факт ",
            "Формула: Водителей на одно ТС (факт) = Факт водителей (ТД + ГПХ пост)/Кол-во ТС с уч.авар. на конец отчетного периода."
        ),
        Legend("Плановая ставка", "Формула: Кол-во ТС с уч.авар. *  Плановое число водителей на одно ТС"),
        Legend("Ставка ТД", "Данные из справочника"),
        Legend("Факт водителей ТД + ГПХ пост", "Сумма ставок ТД + ставки ГПХ пост"),
        Legend("Факт водителей ТД + ГПХ все", "Сумма ставок ТД и ставок ГПХ"),
        Legend("Ставка ГПХ", "Данные из справочника"),
        Legend("Ставки ГПХ без постоянных", "Формула: Ставки ГПХ - ставки ГПХ пост."),
        Legend("Ставки ГПХ пост более 30 дней", "ставки ГПХ >30 дней данные из справочника."),
        Legend("Укомплектованность, чел", "Формула: плановые ставки - Факт водителей (ТД + ГПХ пост)."),
        Legend("Укомплектованности, %", "Формула: (Фактическая численность / Плановая численность)*100%"),
        Legend("Показатели за период", "", true, true),
        Legend("Прием ТД за период", "Прием водителей устроенных по ТД за период."),
        Legend(
            "Увольнения ТД за период",
            "Количество водителей по ТД, с которыми были расторгнуты трудовые соглашения в течение отчетного периода. "
        ),
        Legend("Прирост ТД", "Формула: Прием ТД за период - Увольнения ТД за период."),
        Legend("Перевод ТД +", "Количество водителей по ТД, которые пришли в подразделение."),
        Legend("Перевод ТД -", "Кол-во водителей по ТД, которые ушли из подразделения"),
        Legend("Прием ГПХ за период", "Прием водителей устроенных по ГПХ за период."),
        Legend(
            "Увольнения ГПХ за период",
            "Количество водителей по ГПХ, с которыми были расторгнуты трудовые соглашения в течение отчетного периода. "
        ),
        Legend("Прирост ГПХ", "Формула: увольнения ГПХ за период - Прирост ГПХ"),
        Legend("Перевод ГПХ +", "Значение показывающее прирост водителей с ГПХ в конкретном подразделении."),
        Legend("Перевод ГПХ -", "Значение показывающее убыль водителей с ГПХ в конкретном подразделении. "),
        Legend("Справочние показатели", "", true, true),
        Legend("Средний возраст ТС", "Данные из справочника"),
        Legend("Число водителей ТД", "Данные из справочника"),
        Legend("Число водителей  ГПХ", "Данные из справочника"),
        Legend("Число водителей  ГПХ пост более 30 дней", "Данные из справочника"),
    )
    private val columns = with(ReportEntry()) {
        listOf(
            periodStart.label,
            periodEnd.label,
            regionMr.label,
            hrTotalPlaceNm.label,
            hrPlaceNm.label,
            transportType.label,
            retailNetwork.label,
            vehicleRealCount.label,
            vehicleCount.label,
            planDriversPerVehicle.label,
            factDriversPerVehicle.label,
            planRate.label,
            factRateContract.label,
            factRatePermanent.label,
            factRateAll.label,
            factRateCivil.label,
            factRateCivilWithoutPermanent.label,
            factRatePermanentCivil.label,
            demandRate.label,
            staffingLevelPercentage.label,
            hireCountContract.label,
            fireCountContract.label,
            growthCountContract.label,
            countDeltaContractPlus.label,
            countDeltaContractMinus.label,
            hireCountCivil.label,
            fireCountCivil.label,
            growthCountCivil.label,
            countDeltaCivilPlus.label,
            countDeltaCivilMinus.label,
            vehicleAvgAge.label,
            factCountContract.label,
            factCountCivil.label,
            factCountPermanentCivil.label
        )
    }

    private fun initFonts(wb: Workbook) {
        boldFont = wb.createFont()
        boldFont.bold = true
        settingsFont = wb.createFont()
        settingsFont.fontHeightInPoints = 12
        headerFont = wb.createFont()
        headerFont.fontHeightInPoints = 12
        headerFont.bold = true
    }


    fun generate(
        req: StaffingReportReq, username: String
    ): InputStream {
        val out = workbook {
            initFonts(this.wb)
            sheet("Настройки", getSettingSheet(this@workbook, req, username)).autosize()
            sheet(
                "ALL", getSheet(
                    this@workbook, repo.getReport(req), 1
                )
            )

        }.toInputStream()
        return out
    }

    @Suppress("NAME_SHADOWING")
    fun getSettingSheet(wbBuilder: WbBuilder, req: StaffingReportReq, username: String): (SheetBuilder) -> Unit {
        val settings: MutableList<List<String>> = mutableListOf(
            listOf(
                "Гранулярность", "Период", "Дата формирования выгрузки", "Сотрудник"
            ),
        )
        var firstRowFlag = true
        req.periods.forEach { period ->
            settings.add(
                if (firstRowFlag) {
                    firstRowFlag = false
                    listOf(
                        req.granularity.description,
                        getPeriodString(period, req.granularity),
                        LocalDate.now().format(formatter),
                        username
                    )
                } else {
                    listOf(req.granularity.description, getPeriodString(period, req.granularity), "", "", "")
                }
            )
        }

        val headerStyle = wbBuilder.wb.createCellStyle()
        headerStyle.fillForegroundColor = IndexedColors.GREY_25_PERCENT.index
        headerStyle.fillPattern = FillPatternType.SOLID_FOREGROUND
        headerStyle.setFont(boldFont)
        return { sheetBuilder ->
            sheetBuilder.row {
                cell("Алгоритмы расчета показателей", cols = 2, cc = {
                    it.cellStyle = headerStyle
                })
                cell(String(List(40) { nbsp }.toCharArray()))
                cell("Настройки выгрузки справочника", cols = 6, cc = {
                    it.cellStyle = headerStyle
                })
            }

            val legendIterator = legend.iterator()
            val settingsIterator = settings.iterator()
            while (legendIterator.hasNext() || settingsIterator.hasNext()) {
                val legendRow = if (legendIterator.hasNext()) legendIterator.next() else Legend("", "")
                val settingsRow = if (settingsIterator.hasNext()) settingsIterator.next()
                else listOf()
                sheetBuilder.row {
                    if (legendRow.bold) {
                        style { setFont(boldFont) }
                    } else {
                        style { setFont(settingsFont) }
                    }
                    if (legendRow.center) {
                        style {
                            alignment = HorizontalAlignment.CENTER
                            setFont(boldFont)
                        }
                        cell(legendRow.name, cols = 2)
                        style { alignment = HorizontalAlignment.LEFT }
                    } else {
                        cell(legendRow.name)
                        cell(legendRow.description)
                    }
                    cell("")
                    settingsRow.forEach {
                        cell(it)
                    }
                }
            }
        }
    }

    private fun getPeriodString(period: LocalDate, granularity: Granularity): String = when (granularity) {
        Granularity.CALENDAR_WEEK -> "${
            period.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).format(formatter)
        } - ${period.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).format(formatter)}"

        Granularity.REPORTING_WEEK -> "${
            period.with(TemporalAdjusters.previousOrSame(DayOfWeek.FRIDAY)).format(formatter)
        } - ${period.with(TemporalAdjusters.nextOrSame(DayOfWeek.THURSDAY)).format(formatter)}"

        Granularity.MONTH -> period.format(monthFormatter)

        Granularity.QUARTER -> {
            val quarter = period.get(IsoFields.QUARTER_OF_YEAR)
            val year = period.year
            "$quarter квартал $year"
        }

        Granularity.YEAR -> "${period.year}"

        Granularity.DAY -> period.format(formatter)
    }

    fun getSheet(wbBuilder: WbBuilder, report: StaffingReport, sheetIndex: Int): (SheetBuilder) -> Unit {
        val data = report.entries
        val boldFont = wbBuilder.wb.createFont()
        boldFont.bold = true
        val headerStyle = ExcelStyles.Header.style(wbBuilder)
        val numberStyle = ExcelStyles.General.style(wbBuilder)
        val dateStyle = ExcelStyles.Date.style(wbBuilder)
        val generalStyle = ExcelStyles.General.style(wbBuilder)
        val percentStyle = ExcelStyles.Percent.style(wbBuilder)
        val floatStyle = ExcelStyles.Float.style(wbBuilder)

        return { builder: SheetBuilder ->
            builder.header {
                style {
                    cloneStyleFrom(headerStyle)
                    wrapText = true
                    alignment = HorizontalAlignment.CENTER
                    verticalAlignment = VerticalAlignment.CENTER
                }
                columns.forEach { head(it) }

            }
            var rowNumber = 0
            val mrCollapse: MutableList<Pair<Int, Int>> = mutableListOf()
            val transportTypeCollapse: MutableList<Pair<Int, Int>> = mutableListOf()
            val placeCollapse: MutableList<Pair<Int, Int>> = mutableListOf()
            data.forEach { item ->
                builder.row {
                    rowNumber++
                    currStyle = dateStyle
                    cell(item.periodStart.value)
                    cell(item.periodEnd.value?.minusDays(1))
                    currStyle = generalStyle
                    cell(item.regionMr.value)
                    cell(item.hrTotalPlaceNm.value)
                    cell(item.hrPlaceNm.value)
                    cell(item.transportType.value)
                    cell(item.retailNetwork.value)
                    currStyle = numberStyle
                    cell(item.vehicleRealCount.value)
                    cell(item.vehicleCount.value)
                    currStyle = floatStyle
                    cell(item.planDriversPerVehicle.value)
                    cell(item.factDriversPerVehicle.value)
                    currStyle = numberStyle
                    cell(item.planRate.value)
                    currStyle = generalStyle
                    cell(item.factRateContract.value)
                    cell(item.factRatePermanent.value)
                    cell(item.factRateAll.value)
                    currStyle = numberStyle
                    cell(item.factRateCivil.value?.roundToInt())
                    cell(item.factRateCivilWithoutPermanent.value?.roundToInt())
                    cell(item.factRatePermanentCivil.value?.roundToInt())
                    currStyle = generalStyle
                    cell(item.demandRate.value)
                    currStyle = percentStyle
                    cell(item.staffingLevelPercentage.value)
                    currStyle = generalStyle
                    cell(item.hireCountContract.value)
                    cell(item.fireCountContract.value)
                    cell(item.growthCountContract.value)
                    cell(item.countDeltaContractPlus.value)
                    cell(item.countDeltaContractMinus.value)
                    currStyle = numberStyle
                    cell(item.hireCountCivil.value)
                    cell(item.fireCountCivil.value)
                    cell(item.growthCountCivil.value)
                    cell(item.countDeltaCivilPlus.value)
                    cell(item.countDeltaCivilMinus.value)
                    currStyle = floatStyle
                    cell(item.vehicleAvgAge.value)
                    currStyle = generalStyle
                    cell(item.factCountContract.value)
                    cell(item.factCountCivil.value)
                    cell(item.factCountPermanentCivil.value)

                }
            }
            val sheet = wbBuilder.wb.getSheetAt(sheetIndex)
            sheet.setColumnWidth(0, 14 * 256)
            sheet.setColumnWidth(1, 22 * 256)
            sheet.setColumnWidth(2, 12 * 256)
            sheet.setColumnWidth(3, 12 * 256)
            sheet.setColumnWidth(4, 15 * 256)
            sheet.setColumnWidth(5, 12 * 256)
            sheet.setColumnWidth(6, 12 * 256)
            sheet.setColumnWidth(7, 12 * 256)
            sheet.setColumnWidth(8, 14 * 256)
            sheet.setColumnWidth(9, 13 * 256)
            sheet.setColumnWidth(10, 13 * 256)
            sheet.setColumnWidth(11, 13 * 256)
            sheet.setColumnWidth(12, 13 * 256)
            sheet.setColumnWidth(13, 14 * 256)
            sheet.setColumnWidth(14, 14 * 256)
            sheet.setColumnWidth(15, 14 * 256)
            sheet.setColumnWidth(16, 14 * 256)
            sheet.setColumnWidth(17, 14 * 256)
            sheet.setColumnWidth(18, 14 * 256)
            sheet.setColumnWidth(19, 14 * 256)


            listOf(mrCollapse, transportTypeCollapse, placeCollapse).forEach { rows ->
                rows.forEach {
                    sheet.groupRow(it.first, it.second)
                    sheet.setRowGroupCollapsed(it.first, true)
                }
            }
        }
    }

    data class Legend(
        val name: String,
        val description: String,
        val bold: Boolean = false,
        val center: Boolean = false
    )
}
