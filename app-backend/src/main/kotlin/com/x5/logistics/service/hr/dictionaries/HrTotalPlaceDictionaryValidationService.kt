package com.x5.logistics.service.hr.dictionaries

import com.x5.logistics.repository.hr.HrTotalPlaceRepo
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceDeleteDto
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceDto
import com.x5.logistics.rest.exception.WrongRequestDataException
import org.springframework.stereotype.Service

private const val MAX_NAME_LENGTH = 50

@Service
class HrTotalPlaceDictionaryValidationService(
    val repo: HrTotalPlaceRepo
) {
    fun validateName(name: String?) {
        if (name?.isBlank() == true) throw WrongRequestDataException("Минимальная длина name: 1 символ")
        if (name?.length!! > MAX_NAME_LENGTH) throw WrongRequestDataException("Максимальная длина name: 50 символов")
        if (repo.checkExistence(name)) throw WrongRequestDataException("Значение name должно быть уникальным")
    }

    fun validatePlace(place: HrTotalPlaceDto) {
        if (place.id == null || !repo.checkExistenceById(place.id)) {
            throw WrongRequestDataException("Запись с id:${place.id} не существует")
        }
    }

    fun validateForDelete(place: HrTotalPlaceDeleteDto) {
        if (!repo.checkExistenceById(place.id)) {
            throw WrongRequestDataException("Запись с id:${place.id} не существует")
        }
        if (repo.checkDependency(place.id)) {
            val placeName = repo.findById(place.id)!!.name
            throw WrongRequestDataException("В других таблицах присутствуют связи с подразделением '${placeName}'")
        }
    }
}