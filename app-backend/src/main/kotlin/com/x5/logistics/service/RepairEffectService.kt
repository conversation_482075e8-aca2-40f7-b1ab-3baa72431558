package com.x5.logistics.service

import com.x5.logistics.repository.repair.RepairModalRubKmRepo
import com.x5.logistics.rest.dto.repair.modal.filter.RepairFilterBrandData
import com.x5.logistics.rest.dto.repair.modal.filter.RepairFilterBrandModelData
import com.x5.logistics.rest.dto.repair.modal.filter.RepairFilterBrandReq
import com.x5.logistics.rest.dto.repair.modal.filter.RepairFilterBrandResp
import com.x5.logistics.rest.dto.repair.modal.rubkm.RepairModalRubKmEffectReq
import com.x5.logistics.rest.dto.repair.modal.rubkm.RepairModalRubKmEffectResp
import org.springframework.stereotype.Service

/**
 * Implementation of JRAAVTO-40 https://wiki.x5.ru/pages/viewpage.action?pageId=280896401
 * <AUTHOR> ( <PERSON><EMAIL> )
 */

@Service
class RepairEffectService(
    val repo: RepairModalRubKmRepo
) {
    fun getRepairEffect(req: RepairModalRubKmEffectReq): RepairModalRubKmEffectResp {
        return repo.getRepairPercentModal(req)
    }

    fun getListBrand(req: RepairFilterBrandReq): RepairFilterBrandResp {
        val result = repo.getBrandByWeight(req)
        val groupByName: Map<String?, List<RepairFilterBrandMidData>> =
            result
                .groupBy { it.brand }
                .mapValues { it.value.groupBy { it.model } }
                .also { byBrand ->
                    byBrand.values.map { byModel ->
                        byModel.values.filter { it.size > 1 }.map { models ->
                            models.also {
                                it.map { data ->
                                    data.model
                                }
                            }
                        }
                    }
                }.map {
                    it.key to it.value.map {
                        it.value.map {
                            RepairFilterBrandMidData(it.model, it.loadWgt)
                        }
                    }.flatten()
                }.toMap()

        return RepairFilterBrandResp(
            "Выбрать все",
            "Выбрать все",
            children = groupByName.map {
                RepairFilterBrandData(
                    label = it.key,
                    value = it.key,
                    children = it.value.map { update ->
                        RepairFilterBrandModelData(
                            label = update.model,
                            value = update.model,
                            loadWgt = update.loadWgt
                        )
                    }
                )
            }
        )
    }
}

data class RepairFilterBrandMidData(
    val model: String?,
    val loadWgt: Number?
)