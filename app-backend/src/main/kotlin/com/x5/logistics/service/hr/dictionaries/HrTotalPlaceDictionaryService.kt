package com.x5.logistics.service.hr.dictionaries

import com.x5.logistics.repository.hr.HrTotalPlaceRepo
import com.x5.logistics.rest.dto.PagedResponse
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceColumn
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceDeleteDto
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceDto
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceListRequest
import com.x5.logistics.rest.dto.hr.dictionaries.HrTotalPlaceSortOrder
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class HrTotalPlaceDictionaryService(
    val repo: HrTotalPlaceRepo,
    val validationService: HrTotalPlaceDictionaryValidationService
) {
    fun getDictionary(request: HrTotalPlaceListRequest): PagedResponse<HrTotalPlaceDto> {
        return repo.search(
            sort = request.sort.ifEmpty { listOf(HrTotalPlaceSortOrder(HrTotalPlaceColumn.Name, true)) },
            page = PageRequest.of(request.pageNumber, request.pageSize),
        )
    }

    fun getAll(): Collection<HrTotalPlaceDto> {
        return repo.search(listOf(HrTotalPlaceSortOrder(HrTotalPlaceColumn.Name, true))).items
    }

    fun create(place: HrTotalPlaceDto, author: String): HrTotalPlaceDto? {
        validationService.validateName(place.name)
        return repo.create(place, author)
    }

    fun edit(place: HrTotalPlaceDto, author: String): HrTotalPlaceDto? {
        validationService.validatePlace(place)
        validationService.validateName(place.name)
        return repo.update(place, author)
    }

    fun delete(place: HrTotalPlaceDeleteDto, author: String) {
        validationService.validateForDelete(place)
        repo.delete(place.id, author)
    }
}
