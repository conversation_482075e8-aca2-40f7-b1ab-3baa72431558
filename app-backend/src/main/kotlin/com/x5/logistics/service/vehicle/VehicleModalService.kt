package com.x5.logistics.service.vehicle

import com.x5.logistics.config.ExposedTransactional
import com.x5.logistics.data.TsData
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.hr.Types
import com.x5.logistics.data.hr.VehicleRegionTimeline
import com.x5.logistics.repository.CountStar
import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.vehicle.VehicleGroup
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.not
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class VehicleModalService {
    @ExposedTransactional
    fun <T> getRating(
        day: LocalDate,
        geoFilter: GeoFilter,
        groupBy: Column<T>,
        groups: Set<VehicleGroup>
    ): Map<T, Long> {
        val tsNumberAlias = CountStar.alias("tsNumber")
        return OrganizationalUnitsTimelineTable.join(
            otherTable = VehicleRegionTimeline,
            joinType = JoinType.INNER,
            onColumn = OrganizationalUnitsTimelineTable.mvzId,
            otherColumn = VehicleRegionTimeline.mvzId
        ).join(
            otherTable = TsData,
            joinType = JoinType.INNER,
            onColumn = VehicleRegionTimeline.equnr,
            otherColumn = TsData.equnr
        ).join(
            otherTable = Types,
            joinType = JoinType.INNER,
            onColumn = TsData.tsType,
            otherColumn = Types.tsType
        ).select(
            groupBy,
            tsNumberAlias
        ).andWhere {
            (OrganizationalUnitsTimelineTable.startDate lessEq day) and (OrganizationalUnitsTimelineTable.endDate greater day)
        }.andWhere {
            (VehicleRegionTimeline.startDate lessEq day) and (VehicleRegionTimeline.endDate greater day)
        }.applyGeoFilter(geoFilter).andWhere {
            not(VehicleRegionTimeline.isInactive)
        }.andWhere {
            Types.tsGroup inList groups.map { it.title }
        }.groupBy(groupBy).orderBy(tsNumberAlias.delegate, SortOrder.DESC).asSequence()
            .map { it[groupBy] to it[tsNumberAlias] }
            .toMap()
    }
}
