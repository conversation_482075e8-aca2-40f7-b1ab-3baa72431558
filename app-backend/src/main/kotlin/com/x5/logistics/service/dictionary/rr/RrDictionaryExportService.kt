package com.x5.logistics.service.dictionary.rr

import com.x5.logistics.data.AtpEntity
import com.x5.logistics.data.dictionary.rr.Month
import com.x5.logistics.data.dictionary.rr.RrDictionaryDetailedExportRow
import com.x5.logistics.data.dictionary.rr.RrDictionaryDetailedExportRowKey
import com.x5.logistics.data.dictionary.rr.RrDictionaryEntity
import com.x5.logistics.data.dictionary.vrt.ToroWork
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtype
import com.x5.logistics.repository.dictionary.rr.RrDictionaryEntityRepo
import com.x5.logistics.repository.dictionary.vrt.ToroWorkJpaRepository
import com.x5.logistics.repository.ls
import com.x5.logistics.repository.minus
import com.x5.logistics.repository.nullable
import com.x5.logistics.repository.or
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.SortItem
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupField
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupFilterField
import com.x5.logistics.rest.dto.dictionary.rr.RrDictionaryGroupsListReq
import com.x5.logistics.rest.dto.dictionary.rr.RrExportForImportRequest
import com.x5.logistics.service.workbook
import com.x5.logistics.util.PRECISION_THRESHOLD
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.Expression
import jakarta.persistence.criteria.Order
import jakarta.persistence.criteria.Predicate
import jakarta.persistence.criteria.Root
import org.aspectj.weaver.tools.cache.SimpleCacheFactory.*
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.Instant

private const val GROUP_SHEET_NAME = "Справочник Тариф рубкм ремонтов"
private const val DETAILED_SHEET_NAME = "Справочник Тариф рубкм ремонтов с детализацией по ВТР"

@Service
class RrDictionaryExportService(
    private val dictionaryService: RrDictionaryService,
    private val detailedRepo: RrDictionaryEntityRepo,
    private val toroWorkJpaRepository: ToroWorkJpaRepository,
) {
    fun export(req: RrDictionaryGroupsListReq): ByteArray {
        val count = dictionaryService.countRrDictionaryGroups(req)
        val data = dictionaryService.getRrDictionaryGroups(req.copy(pageSize = count, pageNumber = 0))
        val months = Month.values()
        return workbook {
            val headerStyle = style {
                setFont(wb.createFont().apply { bold = true })
            }

            val generalStyle = style {
                dataFormat = wb.createDataFormat().getFormat("General")
            }

            val numberStyle = style {
                dataFormat = wb.createDataFormat().getFormat("0.00")
            }

            sheet(GROUP_SHEET_NAME) {
                header {
                    currStyle = headerStyle
                    head("АТП")
                    head("Тоннаж")
                    head("Год")
                    head("Месяц")
                    head("Руб/км")
                }
                data.forEach { item ->
                    if (item.rates.isNotEmpty()) {
                        months.forEach { month ->
                            row {
                                currStyle = generalStyle
                                cell(item.atpName)
                                cell(item.tonnage)
                                cell(item.year)
                                cell(month.ordinal + 1)
                                currStyle = numberStyle
                                cell(item.rates.find { it.month == month }?.rate)
                            }
                        }
                    }
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }

    private data class ExportKey(
        val atpName: String,
        val tonnage: BigDecimal,
        val vrtId: String,
        val year: Int
    )

    fun detailedExport(
        req: RrDictionaryGroupsListReq,
        years: List<Int>? = null,
        importReq: RrExportForImportRequest? = null
    ): ByteArray {
        val vrtsById = if (importReq != null) {
            toroWorkJpaRepository.findAll()
                .filter { it.subtype?.id != 1L && it.subtype?.id != 32L }
                .associateBy { it.id }
        } else emptyMap()

        val keys = if (importReq != null) {
            importReq.atpTonnageFilter.asSequence()
                .flatMap { filter ->
                    sequence {
                        vrtsById.forEach { (vrtId, _) ->
                            years.orEmpty().forEach { year ->
                                yield(
                                    ExportKey(
                                        atpName = filter.atpName,
                                        tonnage = filter.tonnage,
                                        vrtId = vrtId,
                                        year = year
                                    )
                                )
                            }
                        }
                    }
                }
                .toMutableSet()
        } else mutableSetOf()

        val data = getDetailedData(req, years)
        val grouped = data.groupBy { it.key }
        val months = Month.values()
        return workbook {
            val headerStyle = style {
                setFont(wb.createFont().apply { bold = true })
            }

            val strStyle = style {
                dataFormat = wb.createDataFormat().getFormat("TEXT")
            }

            val generalStyle = style {
                dataFormat = wb.createDataFormat().getFormat("General")
            }

            val numberStyle = style {
                dataFormat = wb.createDataFormat().getFormat("0.00")
            }

            sheet(DETAILED_SHEET_NAME) {
                header {
                    currStyle = headerStyle
                    head("АТП")
                    head("Тоннаж")
                    head("Код ВРТ")
                    head("Название ВРТ")
                    head("Подвид ВРТ")
                    head("Год")
                    head("Месяц")
                    head("Руб/км")
                }
                grouped.entries.forEach { vrtRow ->
                    if (importReq != null || vrtRow.value.any { it.rate != 0.0 }) {
                        keys.remove(
                            ExportKey(
                                atpName = vrtRow.key.atpName,
                                tonnage = vrtRow.key.tonnage,
                                vrtId = vrtRow.key.vrtId,
                                year = vrtRow.key.year
                            )
                        )
                        months.forEach { month ->
                            row {
                                currStyle = strStyle
                                cell(vrtRow.key.atpName)
                                currStyle = generalStyle
                                cell(vrtRow.key.tonnage)
                                currStyle = strStyle
                                cell(vrtRow.key.vrtId)
                                cell(vrtRow.key.vrtName)
                                cell(vrtRow.key.subtype)
                                currStyle = generalStyle
                                cell(vrtRow.key.year)
                                cell(month.ordinal + 1)
                                currStyle = numberStyle
                                cell(vrtRow.value.find { it.month == month }?.rate)
                            }
                        }
                    }
                }
                keys.forEach { key ->
                    months.forEach { month ->
                        row {
                            val vrt = vrtsById[key.vrtId]!!
                            currStyle = strStyle
                            cell(key.atpName)
                            currStyle = generalStyle
                            cell(key.tonnage)
                            currStyle = strStyle
                            cell(vrt.id)
                            cell(vrt.name)
                            cell(vrt.subtype?.name)
                            currStyle = generalStyle
                            cell(key.year)
                            cell(month.ordinal + 1)
                            currStyle = numberStyle
                            cell(0.0)
                        }
                    }
                }
            }.autosize()
        }.let {
            val stream = ByteArrayOutputStream()
            it.wb.write(stream)
            stream.toByteArray()
        }
    }

    private fun getDetailedData(
        req: RrDictionaryGroupsListReq,
        years: List<Int>? = null
    ): List<RrDictionaryDetailedExportRow> {
        return detailedRepo.getByJpa {
            val builder = criteriaBuilder
            val query = builder.createQuery(RrDictionaryEntity::class.java)
            val root = query.from(RrDictionaryEntity::class.java)
            query.select(root)
            req.getPredicate(builder, root, years)?.let { predicate ->
                query.where(predicate)
            }
            req.sort.ifEmpty {
                listOf(
                    SortItem(RrDictionaryGroupField.ATP_NAME),
                    SortItem(RrDictionaryGroupField.TONNAGE)
                )
            }.map { it.getOrder(builder, root) }.takeIf { it.isNotEmpty() }?.let {
                query.orderBy(it)
            }
            val typedQuery = createQuery(query)
            typedQuery.resultList.map { it.toRowDto() }
        }
    }

    private fun RrDictionaryGroupsListReq.getPredicate(
        criteriaBuilder: CriteriaBuilder,
        root: Root<RrDictionaryEntity>,
        years: List<Int>? = null,
    ): Predicate? {
        val predicates = buildList {
            if (years != null) {
                add(root.get<Int>("year").`in`(years))
            } else {
                add(criteriaBuilder.equal(root.get<Int>("year"), <EMAIL>))
            }
            filters.forEach { filter ->
                when (filter.name) {
                    RrDictionaryGroupFilterField.ATP_ID -> {
                        add(root.get<AtpEntity>("atp").get<Long>("id").`in`(filter.value))
                    }

                    RrDictionaryGroupFilterField.ATP_NAME -> {
                        add(root.get<AtpEntity>("atp").get<String>("name").`in`(filter.value))
                    }

                    RrDictionaryGroupFilterField.TONNAGE -> {
                        val values = filter.value.map { (it as Number).toDouble() }
                        val expression = root.get<Double>("tonnage")
                        add(
                            criteriaBuilder.or(
                            values.map {
                                criteriaBuilder.lessThan(
                                    criteriaBuilder.abs(criteriaBuilder.diff(expression, it)),
                                    PRECISION_THRESHOLD
                                )
                            }
                        ))
                    }

                    RrDictionaryGroupFilterField.VRT_SUBTYPE_ID -> {
                        val path = root
                            .get<ToroWork>("toroWork")
                            .get<ToroWorksSubtype>("subtype")
                            .get<Long>("id")
                        if (filter.condition == FilterCondition.notEqual) {
                            add(criteriaBuilder.not(path.`in`(filter.value)))
                        } else { // null interpreted as 'equal'
                            add(path.`in`(filter.value))
                        }
                    }
                }
            }
        }.toTypedArray()
        return if (predicates.isEmpty()) {
            null
        } else if (predicates.size == 1) {
            predicates[0]
        } else {
            criteriaBuilder.and(*predicates)
        }
    }

    private fun SortItem<RrDictionaryGroupField>.getOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<RrDictionaryEntity>
    ): Order {
        val path = when (column) {
            RrDictionaryGroupField.YEAR -> root.get<Int>("year")
            RrDictionaryGroupField.ATP_NAME -> root.get<AtpEntity>("atp").get<String>("name")
            RrDictionaryGroupField.TONNAGE -> root.get<Double>("tonnage")
            RrDictionaryGroupField.CREATED_BY -> root.get<String>("createdBy")
            RrDictionaryGroupField.CREATED_AT -> root.get<Instant>("createdAt")
        }
        return if (asc) {
            criteriaBuilder.asc(path)
        } else {
            criteriaBuilder.desc(path)
        }
    }

    private fun RrDictionaryEntity.toRowDto(): RrDictionaryDetailedExportRow =
        RrDictionaryDetailedExportRow(
            key = RrDictionaryDetailedExportRowKey(
                atpName = atp.name,
                vrtName = toroWork.name,
                subtype = toroWork.subtype?.name,
                vrtId = toroWork.id,
                tonnage = tonnage,
                year = year,
            ),
            month = month,
            rate = rate
        )
}
