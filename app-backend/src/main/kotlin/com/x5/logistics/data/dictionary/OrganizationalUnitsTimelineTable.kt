package com.x5.logistics.data.dictionary

import com.x5.logistics.repository.applyGeoFilter
import com.x5.logistics.rest.dto.GeoFilter
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.QueryAlias
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.selectAll
import java.time.LocalDate

object OrganizationalUnitsTimelineTable: Table("organizational_units_timeline") {
    val startDate = date("start_date")
    val endDate = date("end_date")
    val mrId = long("mr_id")
    val atpId = long("atp_id")
    val mvzId = text("mvz_id")
    val retailNetwork = text("retail_network")
    val atpType = text("atp_type").nullable()
    val mvzType = text("mvz_type").nullable()
    val repshopId = long("repshop_id")
    val repshopName = text("repshop_name")
    val atpName = text("atp_name")
    val mvzName = text("mvz_name")
    val mrName = text("mr_name")
    val territoryId = long("territory_id")
    val territoryName = text("territory_name")

    fun getGeoFilter(geofilter: GeoFilter?): List<Op<Boolean>> {
        val filters = mutableListOf<Op<Boolean>>()
        if (geofilter != null) {
            geofilter.mr?.let {
                if (it.isNotEmpty()) {
                    filters.add(mrId inList it)
                }
            }
            geofilter.atp?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpId inList it)
                }
            }
            geofilter.mvz?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzId inList it)
                }
            }
            geofilter.retailNetwork?.let {
                if (it.isNotEmpty()) {
                    filters.add(retailNetwork inList it)
                }
            }
            geofilter.atpType?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpType inList it)
                }
            }
            geofilter.mvzType?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzType inList it.filterNotNull())
                }
            }
            geofilter.territory?.let {
                if (it.isNotEmpty()) {
                    filters.add(territoryId inList it)
                }
            }
        }
        return filters.toList()
    }

    fun orgUnitsSubquery(from: LocalDate, to: LocalDate, geoFilter: GeoFilter): QueryAlias {
        val dateFilter = with(OrganizationalUnitsTimelineTable) {
            startDate greaterEq from and (startDate less to)
        }

        return with(OrganizationalUnitsTimelineTable) {
            selectAll()
                .where(dateFilter)
                .applyGeoFilter(geoFilter)
        }.alias("org_units")
    }
}
