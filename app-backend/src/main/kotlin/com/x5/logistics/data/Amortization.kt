package com.x5.logistics.data

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object Amortization : Table("amortization") {
    val assetId = long("asset_id").nullable()
    val amoDate = date("amo_date").nullable()
    val mvzId = text("mvz_id").nullable()
    val mvzName = text("mvz_name").nullable()
    val osClass = long("os_class").nullable()
    val be = text("be").nullable()
    val inventoryNum = text("inventory_num").nullable()
    val createDate = date("create_date").nullable()
    val activeDate = date("active_date").nullable()
    val firstFinTransactionDate = date("first_fin_transaction_date").nullable()
    val firstTripDate = date("first_trip_date").nullable()
    val inactiveDate = date("inactive_date").nullable()
    val fleetNum = text("fleet_num").nullable()
    val equnr = long("equnr").nullable()
    val tsMvzId = text("ts_mvz_id").nullable()
    val tsMvzName = text("ts_mvz_name").nullable()
    val tsClass = long("ts_class").nullable()
    val tsBe = text("ts_be").nullable()
    val tsGroup = text("ts_group").nullable()
    val tsType = text("ts_type").nullable()
    val tsBrand = text("ts_brand").nullable()
    val tsModel = text("ts_model").nullable()
    val tsTonnage = float("ts_tonnage").nullable()
    val tsGbo = text("ts_gbo_text").nullable()
    val tsCreateYear = integer("ts_create_year").nullable()
    val tsLicenseNum = text("ts_license_num").nullable()
    val tsMileage = double("ts_mileage").nullable()
    val tsOwnershipTypeId = text("ts_ownership_type_id").nullable()
    val tsOwnershipType = text("ts_ownership_type").nullable()
    val tsCreateDate = date("ts_create_date").nullable()
    val tsInactiveDate = date("ts_inactive_date").nullable()
    val startValue = double("start_value").nullable()
    val planAmo = double("plan_amo").nullable()
    val factAmo = double("fact_amo").nullable()
    val remainderCost = double("remainder_cost").nullable()
    val accumulatedAmo = double("accumulated_amo").nullable()
}
