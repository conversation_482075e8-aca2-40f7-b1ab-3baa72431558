package com.x5.logistics.data.dictionary

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object NhPlanDictionaryView: Table("nh_plan_dictionary_view") {
    val id = long("id").nullable()
    val vehicleId = long("vehicle_id")
    val type = text("type")
    val brand = text("brand")
    val tonnage = double("tonnage")
    val createYear = integer("create_year")
    val planNh = double("plan_nh").nullable()
    val startDate = date("start_date").nullable()
    val endDate = date("end_date").nullable()
    val author = text("author")
    val deleted = bool("deleted").nullable()
}
