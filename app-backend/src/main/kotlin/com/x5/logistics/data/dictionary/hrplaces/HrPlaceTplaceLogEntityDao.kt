package com.x5.logistics.data.dictionary.hrplaces

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.LocalDate
import java.time.LocalDateTime

@Entity
@Table(name = "hr_places_tplaces_log")
data class HrPlaceTplaceLogEntityDao(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long = 0,

    @ManyToOne(optional = false)
    @JoinColumn(name = "p_id")
    var place: HrPlaceDao,

    @ManyToOne
    @JoinColumn(name = "tp_id")
    var totalPlace: HrTotalPlaceDao?,

    @Column(name = "start_date", nullable = false)
    var startDate: LocalDate,

    @Column(name = "created_at")
    var createdAt: LocalDateTime?,

    @Column(name = "created_by", nullable = false)
    var createdBy: String,

    @Column(name = "updated_at")
    var updatedAt: LocalDateTime?,

    @Column(name = "updated_by")
    var updatedBy: String?,

    @Column(name = "deleted", nullable = false)
    var deleted: Boolean
)
