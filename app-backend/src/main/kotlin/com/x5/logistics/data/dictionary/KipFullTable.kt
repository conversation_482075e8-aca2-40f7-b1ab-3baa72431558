package com.x5.logistics.data.dictionary

import com.x5.logistics.repository.commaSeparatedList
import com.x5.logistics.repository.interval
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object KipFullTable: Table("kip_full") {
    val vehicleDate = date("vehicle_date")
    val equnr = long("equnr")
    val mvzId = text("mvz_id")
    val mvzName = text("mvz_name")
    val vehicleType = text("vehicle_type")
    val vehicleGroup = text("vehicle_group")
    val vehicleBrand = text("vehicle_brand")
    val vehicleModel = text("vehicle_model")
    val vehicleTonnage = double("vehicle_tonnage").nullable()
    val vehicleLicense = text("vehicle_license")
    val vehicleVin = text("vehicle_vin")
    val vehicleProductionPear = text("vehicle_production_year")
    val vehicleCreateYear = integer("vehicle_create_year")
    val vehicleGbo = bool("vehicle_gbo")
    val vehicleGboText = bool("vehicle_gbo_text")
    val vehicleNoCompart = integer("vehicle_no_compart").nullable()
    val trailerEqunr = long("trailer_equnr").nullable()
    val trailerLicense = text("trailer_license")
    val trailerType = text("trailer_type").nullable()
    val trailerGroup = text("trailer_group").nullable()
    val trailerBrand = text("trailer_brand").nullable()
    val trailerModel = text("trailer_model").nullable()
    val trailerTonnage = double("trailer_tonnage").nullable()
    val trailerFleetNum = text("trailer_fleet_num").nullable()
    val trailerCreateYear = float("trailer_create_year").nullable()
    val trailerNoCompart = integer("trailer_no_compart").nullable()
    val rcSubmitId = text("rc_submit_id").nullable()
    val rcSubmitName = text("rc_submit_name").nullable()
    val vehicleStatus = text("vehicle_status").nullable()
    val vehicleReason = integer("vehicle_reason").nullable()
    val vehicleStatusV1text = integer("vehicle_status_v1_text").nullable()
    val vehicleReasonV1 = text("vehicle_reason_v1").nullable()
    val comeOff = bool("come_off").nullable()
    val waybillsOverlap = bool("waybills_overlap").nullable()
    val ktg = bool("ktg")
    val rg = bool("rg")
    val kip = interval("kip")
    val kipNoReserve = interval("kip_no_reserve")
    val repairDay = bool("repair_day")
    val notSubmitted = bool("not_submitted")
    val notReady = bool("not_ready")
    val notKtgWithWb = interval("not_ktg_with_wb")
    val noWb = interval("no_wb")
    val repairWb = interval("repair_wb")
    val noTripsTime = interval("no_trips_time")
    val idleTime = interval("idle_time")
    val repairPointsTime = interval("repair_points_time")
    val kipReal = interval("kip_real")
    val requestFlag = bool("request_flag")
    val requestCount = long("request_count")
    val mileage = double("mileage").nullable()
    val tonnageWhole = decimal("tonnage_whole", 10, 2)
    val compart_whole = integer("compart_whole")
    val status = text("status")
    val reason = text("reason")
    val waybills = commaSeparatedList("waybills")
    val waybillsRepair = commaSeparatedList("waybills_repair")
}
