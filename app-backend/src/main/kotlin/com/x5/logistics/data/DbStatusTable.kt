package com.x5.logistics.data

import org.jetbrains.exposed.dao.LongEntity
import org.jetbrains.exposed.dao.LongEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.timestamp

object DbStatusTable : LongIdTable("db_status") {
    val switchBy = text("switch_by")
    val switchAt = timestamp("switch_at")
    val switchType = text("switch_type")
}

class DbStatusEntity(id: EntityID<Long>) : LongEntity(id) {
    companion object : LongEntityClass<DbStatusEntity>(DbStatusTable)
    var switchBy by DbStatusTable.switchBy
    var switchAt by DbStatusTable.switchAt
    var switchType by DbStatusTable.switchType
}
