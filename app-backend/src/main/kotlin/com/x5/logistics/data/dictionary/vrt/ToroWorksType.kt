package com.x5.logistics.data.dictionary.vrt

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.Instant

@Entity
@Table(name = "toro_works_types")
data class ToroWorksType(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    val id: Long,

    @Column(name = "name")
    val name: String?,

    @Column(name = "created_by", nullable = false)
    val createdBy: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: Instant,

    @Column(name = "updated_by")
    val updatedBy: String? = null,

    @Column(name = "updated_at")
    val updatedAt: Instant? = null,

    @Column(name = "deleted", nullable = false)
    val deleted: Boolean = false,
)
