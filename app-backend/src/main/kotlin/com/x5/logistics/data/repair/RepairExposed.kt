package com.x5.logistics.data.repair

import com.x5.logistics.rest.dto.GeoFilter
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.PlusOp
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.javatime.date

object RepairExposed : Table("repair") {
    val mrId = long("vehicle_mr_id")
    val atpId = long("vehicle_atp_id")
    val mvz = text("vehicle_mvz_id")
    val mvzType = text("vehicle_mvz_type")
    val atpType = text("vehicle_atp_type")
    val retailNetwork = text("vehicle_retail_network")
    val startDate = date("repair_start_date").nullable()
    val tsCreateDate = date("ts_create_date")
    val vrt = text("vrt")
    val vrtTypeId = text("repair_type_id")
    val vrtTypeName = text("repair_type_name")
    val vrtSubtypeId = integer("repair_subtype_id")
    val vrtSubtypeName = text("repair_subtype_name")
    val ourWorkshop = bool("our_workshop")
    val servicesAmount = double("services_amount")
    val servicesExpenses = double("services_expenses")
    val orderId = long("order_id").nullable()
    val repairExpenses = float("repair_expenses").nullable()
    val repairExpensesFull = float("repair_expenses_full").nullable()
    val tsModel = text("ts_model").nullable()
    val tsMarka = text("ts_marka").nullable()
    val equnr = long("equnr").nullable()
    val tsGbo = bool("ts_gbo").nullable()

    val nhSumAmount = Sum(servicesAmount, DoubleColumnType())
    val nhSumPrice = Sum(servicesExpenses, DoubleColumnType())

    val nhAmountOur =
        Sum(Case().When(ourWorkshop eq true, servicesAmount).Else(doubleLiteral(0.0)), DoubleColumnType())
    val nhSumPriceOur =
        Sum(Case().When(ourWorkshop eq true, servicesExpenses).Else(doubleLiteral(0.0)), DoubleColumnType())

    val nhAmountSto =
        Sum(Case().When(ourWorkshop eq false, servicesAmount).Else(doubleLiteral(0.0)), DoubleColumnType())
    val nhSumPriceSto =
        Sum(Case().When(ourWorkshop eq false, servicesExpenses).Else(doubleLiteral(0.0)), DoubleColumnType())

    val totalNhAmount = PlusOp(nhAmountOur, nhAmountSto, DoubleColumnType())

    fun getGeoFilter(geofilter: GeoFilter?): List<Op<Boolean>> {
        val filters = mutableListOf<Op<Boolean>>()
        if (geofilter != null) {
            geofilter.mr?.let {
                if (it.isNotEmpty()) {
                    filters.add(mrId inList it)
                }
            }
            geofilter.atp?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpId inList it)
                }
            }
            geofilter.mvz?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvz inList it)
                }
            }
            geofilter.retailNetwork?.let {
                if (it.isNotEmpty()) {
                    filters.add(retailNetwork inList it)
                }
            }
            geofilter.atpType?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpType inList it)
                }
            }
            geofilter.mvzType?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzType inList it.filterNotNull())
                }
            }
        }
        return filters.toList()
    }
}