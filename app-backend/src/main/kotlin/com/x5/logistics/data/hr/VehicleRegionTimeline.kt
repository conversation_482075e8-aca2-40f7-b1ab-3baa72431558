package com.x5.logistics.data.hr

import com.x5.logistics.rest.dto.GeoFilter
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.times
import org.jetbrains.exposed.sql.Sum
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.castTo
import org.jetbrains.exposed.sql.doubleLiteral
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.year
import java.time.LocalDate

object VehicleRegionTimeline: Table("vehicle_region_timeline") {
    val regionId = long("region_id")
    val equnr = long("equnr")
    val startDate = date("start_date")
    val endDate = date("end_date")
    val motorCompanyId = long("motor_company_id")
    val mrId = long("mr_id")
    val mrName = text("mr_name")
    val atpId = long("atp_id")
    val atpName = text("atp_name")
    val mvzId = text("mvz_id")
    val mvzName = text("mvz_name").nullable()
    val retailNetwork = text("retail_network")
    val atpType = text("atp_type")
    val mvzType = text("mvz_type")
    val repshopId = integer("repshop_id").nullable()
    val repshopName = text("repshop_name").nullable()
    val isInactive = bool("is_inactive")
    val tdNoCompart = integer("td_no_compart")
    val tdGroup = text("td_group").nullable()
    val tdType = text("td_type").nullable()
    val tdCreateDate = date("td_create_date").nullable()
    val tdGbo = bool("td_gbo").nullable()
    val commissioningYear = tdCreateDate.year()
    val tdFleetNum = text("td_fleet_num").nullable()
    val tdLicenseNum = text("td_license_num").nullable()
    val tdLoadWgt = double("td_tonnage").nullable()
    val year = short("td_baujj").nullable()
    val tdBrand = text("td_brand").nullable()
    val tdModel = text("td_model").nullable()

    fun rate(reqEndDate: LocalDate) =
        (doubleLiteral(2.0) * Sum(
            Case()
                .When(
                    (startDate lessEq reqEndDate) and (endDate greater reqEndDate),
                    KtgDictionary.ktgVehicleCount.aliasOnlyExpression()
                )
                .Else(doubleLiteral(0.0).castTo(DoubleColumnType())),
            DoubleColumnType()
        ).castTo(DoubleColumnType())).alias("rate")

    fun getGeoFilter(geofilter: GeoFilter?): List<Op<Boolean>> {
        val filters = mutableListOf<Op<Boolean>>()
        if (geofilter != null) {
            geofilter.mr?.let {
                if (it.isNotEmpty()) {
                    filters.add(mrId inList it)
                }
            }
            geofilter.atp?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpId inList it)
                }
            }
            geofilter.mvz?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzId inList it)
                }
            }
            geofilter.retailNetwork?.let {
                if (it.isNotEmpty()) {
                    filters.add(retailNetwork inList it)
                }
            }
            geofilter.atpType?.let {
                if (it.isNotEmpty()) {
                    filters.add(atpType inList it)
                }
            }
            geofilter.mvzType?.let {
                if (it.isNotEmpty()) {
                    filters.add(mvzType inList it.filterNotNull())
                }
            }
        }
        return filters.toList()
    }
}