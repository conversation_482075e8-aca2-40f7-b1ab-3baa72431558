package com.x5.logistics.data.repair

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date

object RepairRatesView : Table("repair_rates_view") {
    val atpId = long("atp_id").nullable()
    val startDate = date("start_date").nullable()
    val endDate = date("end_date").nullable()
    val tonnage = decimal("tonnage", 5, 2).nullable()
    val rate = double("rate").nullable()
    val structureName = text("structure_name").nullable()
}