package com.x5.logistics.data.dictionary

import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.timestamp

object AtpRepairShopLog : Table("atp_repshops_log") {
    val id = integer("id")
    val atpId = integer("atp_id")
    val rsId = integer("rs_id")
    val startDate = date("start_date")
    val createdAt = timestamp("created_at")
    val createdBy = text("created_by")
    val updatedAt = timestamp("updated_at")
    val updatedBy = text("updated_by")
    val deleted = bool("deleted")
}
