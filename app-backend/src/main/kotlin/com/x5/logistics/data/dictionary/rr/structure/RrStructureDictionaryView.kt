package com.x5.logistics.data.dictionary.rr.structure

import com.x5.logistics.data.dictionary.org.AtpTable
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.javatime.datetime

object RrStructureDictionaryView : Table("rr_structure_dictionary_view") {
    val atpId = reference("atp_id", AtpTable)
    val type = text("type")
    val month = text("month")
    val name = text("name")
    val year = integer("year")
    val rate = double("rate").nullable()
    val updatedAt = datetime("updated_at")
    val updatedBy = text("updated_by")
}
