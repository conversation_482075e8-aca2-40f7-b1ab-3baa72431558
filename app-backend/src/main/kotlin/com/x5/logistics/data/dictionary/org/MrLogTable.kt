package com.x5.logistics.data.dictionary.org

import com.x5.logistics.repository.castToInt
import com.x5.logistics.repository.minusDays
import org.jetbrains.exposed.dao.IntEntity
import org.jetbrains.exposed.dao.IntEntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IntIdTable
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.IsNotNullOp
import org.jetbrains.exposed.sql.IsNullOp
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Lead
import org.jetbrains.exposed.sql.Max
import org.jetbrains.exposed.sql.Min
import org.jetbrains.exposed.sql.NotExists
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.javatime.JavaLocalDateColumnType
import org.jetbrains.exposed.sql.javatime.date
import org.jetbrains.exposed.sql.javatime.dateLiteral
import org.jetbrains.exposed.sql.javatime.timestamp
import org.jetbrains.exposed.sql.min
import org.jetbrains.exposed.sql.or
import java.time.LocalDate

object MrLogTable : IntIdTable("mr_log") {
    val mrId = reference("mr_id", MrTable)
    val terId = reference("ter_id", TerritoryTable)
    val startDate = date("start_date")
    val createdAt = timestamp("created_at")
    val createdBy = text("created_by")
    val updatedAt = timestamp("updated_at")
    val updatedBy = text("updated_by")
    val deleted = bool("deleted").default(false)

    val endDate = Case()
        .When(
            IsNotNullOp(mrId),
            Lead(startDate.minusDays(1), defaultValue = dateLiteral(LocalDate.of(2099, 12, 31)))
                .over().partitionBy(mrId).orderBy(startDate)
        ).Else(Op.nullOp()).alias("end_date")
    val minStartDate = Min(startDate, JavaLocalDateColumnType()).over().partitionBy(mrId).alias("min_start_date")

    private val currentDate = dateLiteral(LocalDate.now())
    private val child = MrLogTable.alias("child")
    private val maxValidDate = Max(
        Case().When(startDate lessEq currentDate, startDate)
            .Else(dateLiteral(LocalDate.of(1800, 1, 1))),
        JavaLocalDateColumnType()
    ).over().partitionBy(mrId)
    private val minDate = startDate.min().over().partitionBy(mrId)
    private val noValidDatesExist = NotExists(
        child
            .select(intLiteral(1))
            .where { child[deleted] eq false and
                (child[mrId].castToInt() eq mrId.castToInt()) and
                        (child[startDate] lessEq currentDate)
            }
    )

    val isCurrent = (IsNullOp(mrId) or (startDate eq maxValidDate) or
            (noValidDatesExist and (startDate eq minDate)))
        .alias("is_current")

    val mrTableMrId = MrTable.id.alias("mr_table_mr_id")
    val mrName = MrTable.name.alias("mr_name")
    val terTableTerId = TerritoryTable.id.alias("ter_table_ter_id")
    val terName = TerritoryTable.name.alias("ter_name")

    val mrLogSubquery = with(MrLogTable) {
        join(MrTable, JoinType.RIGHT, mrId, MrTable.id) { (MrTable.deleted eq false) and (MrLogTable.deleted eq false) }
            .join(TerritoryTable, JoinType.LEFT, terId, TerritoryTable.id)
            .select(
                columns
                        + listOf(endDate, minStartDate, isCurrent, mrTableMrId, mrName, terTableTerId, terName)
            )
            .where { (deleted eq booleanLiteral(false) or IsNullOp(deleted)) and (MrTable.deleted eq false) }
    }

    val mrLogSubqueryAlias = mrLogSubquery.alias("mr_log_subquery")


}

class MrLogEntity(id: EntityID<Int>) : IntEntity(id) {
    companion object : IntEntityClass<MrLogEntity>(MrLogTable) {
        override fun searchQuery(op: Op<Boolean>): Query {
            return super.searchQuery(op).adjustSelect {
                select(
                    columns + listOf(
                        MrLogTable.endDate,
                        MrLogTable.isCurrent
                    )
                )
            }
        }
    }

    var mr by MrEntity referencedOn MrLogTable.mrId
    var territory by TerritoryEntity referencedOn MrLogTable.terId
    var startDate by MrLogTable.startDate
    var createdAt by MrLogTable.createdAt
    var createdBy by MrLogTable.createdBy
    var updatedAt by MrLogTable.updatedAt
    var updatedBy by MrLogTable.updatedBy
    var deleted by MrLogTable.deleted
}