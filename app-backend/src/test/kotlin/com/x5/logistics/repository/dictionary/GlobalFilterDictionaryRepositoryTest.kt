package com.x5.logistics.repository.dictionary

import com.x5.logistics.TestApplication
import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.rest.dto.LabeledValue
import jakarta.persistence.EntityManager
import jakarta.persistence.Tuple
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers

@SpringBootTest(classes = [TestApplication::class])
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class GlobalFilterDictionaryRepositoryTest : TestPostgresqlConfig() {
    
    @Autowired
    private lateinit var entityManager: EntityManager
    
    private lateinit var repository: GlobalFilterDictionaryRepository
    
    @BeforeEach
    fun setUp() {
        repository = GlobalFilterDictionaryRepository(entityManager)
        
        // Create mock data for the queries
        val mockTuple1 = mock(Tuple::class.java)
        `when`(mockTuple1["values"]).thenReturn(1)
        `when`(mockTuple1["labels"]).thenReturn("Test Macro Region")
        
        val mockTuple2 = mock(Tuple::class.java)
        `when`(mockTuple2["values"]).thenReturn(2)
        `when`(mockTuple2["labels"]).thenReturn("Test ATP")
        
        val mockTuple3 = mock(Tuple::class.java)
        `when`(mockTuple3["values"]).thenReturn(3)
        `when`(mockTuple3["labels"]).thenReturn("Test MVZ")
        
        val mockTuple4 = mock(Tuple::class.java)
        `when`(mockTuple4["values"]).thenReturn("Test Retail Network")
        `when`(mockTuple4["labels"]).thenReturn("Test Retail Network")
        
        val mockTuple5 = mock(Tuple::class.java)
        `when`(mockTuple5["values"]).thenReturn("Test ATP Type")
        `when`(mockTuple5["labels"]).thenReturn("Test ATP Type")
        
        val mockTuple6 = mock(Tuple::class.java)
        `when`(mockTuple6["values"]).thenReturn("Test MVZ Type")
        `when`(mockTuple6["labels"]).thenReturn("Test MVZ Type")
        
        val mockTuple7 = mock(Tuple::class.java)
        `when`(mockTuple7["values"]).thenReturn(4)
        `when`(mockTuple7["labels"]).thenReturn("Test Territory")
        
        // Mock the EntityManager to return our mock data
        val mockEntityManager = mock(EntityManager::class.java)
        val mockQuery = mock(jakarta.persistence.Query::class.java)
        
        // Mock the createNativeQuery method to return our mock query
        `when`(mockEntityManager.createNativeQuery(org.mockito.ArgumentMatchers.anyString(), org.mockito.ArgumentMatchers.eq(Tuple::class.java))).thenReturn(mockQuery)
        
        // Mock the resultList to return our mock tuples
        `when`(mockQuery.resultList).thenReturn(listOf(mockTuple1), listOf(mockTuple2), listOf(mockTuple3), 
            listOf(mockTuple4), listOf(mockTuple5), listOf(mockTuple6), listOf(mockTuple7))
        
        // Use the mock EntityManager for our repository
        repository = GlobalFilterDictionaryRepository(mockEntityManager)
    }
    
    @Test
    fun `test getGlobalFilterDictionary returns dictionary values`() {
        // Execute the function being tested
        val result = repository.getGlobalFilterDictionary()

        // Verify the content of the results
        // Check mr (Macro Region)
        assert(result.mr.label == "Макрорегион") { "Label for mr should be 'Макрорегион'" }
        assert(result.mr.value == "") { "Value for mr should be empty string" }
        assert(result.mr.children.size == 1) { "mr should have 1 child" }
        assert(result.mr.children[0].label == "Test Macro Region") { "Label for mr child should be 'Test Macro Region'" }
        assert(result.mr.children[0].value == 1) { "Value for mr child should be 1" }
        
        // Check atp
        assert(result.atp.label == "АТП") { "Label for atp should be 'АТП'" }
        assert(result.atp.value == "") { "Value for atp should be empty string" }
        assert(result.atp.children.size == 1) { "atp should have 1 child" }
        assert(result.atp.children[0].label == "Test ATP") { "Label for atp child should be 'Test ATP'" }
        assert(result.atp.children[0].value == 2) { "Value for atp child should be 2" }
        
        // Check mvz
        assert(result.mvz.label == "МВЗ") { "Label for mvz should be 'МВЗ'" }
        assert(result.mvz.value == "") { "Value for mvz should be empty string" }
        assert(result.mvz.children.size == 1) { "mvz should have 1 child" }
        assert(result.mvz.children[0].label == "Test MVZ") { "Label for mvz child should be 'Test MVZ'" }
        assert(result.mvz.children[0].value == 3) { "Value for mvz child should be 3" }
        
        // Check retailNetwork
        assert(result.retailNetwork.label == "Торговая сеть АТП") { "Label for retailNetwork should be 'Торговая сеть АТП'" }
        assert(result.retailNetwork.value == "") { "Value for retailNetwork should be empty string" }
        assert(result.retailNetwork.children.size == 1) { "retailNetwork should have 1 child" }
        assert(result.retailNetwork.children[0].label == "Test Retail Network") { "Label for retailNetwork child should be 'Test Retail Network'" }
        assert(result.retailNetwork.children[0].value == "Test Retail Network") { "Value for retailNetwork child should be 'Test Retail Network'" }
        
        // Check atpType
        assert(result.atpType.label == "Вид деятельности АТП ∙ ВД") { "Label for atpType should be 'Вид деятельности АТП ∙ ВД'" }
        assert(result.atpType.value == "") { "Value for atpType should be empty string" }
        assert(result.atpType.children.size == 1) { "atpType should have 1 child" }
        assert(result.atpType.children[0].label == "Test ATP Type") { "Label for atpType child should be 'Test ATP Type'" }
        assert(result.atpType.children[0].value == "Test ATP Type") { "Value for atpType child should be 'Test ATP Type'" }
        
        // Check mvzType
        assert(result.mvzType.label == "Тип МВЗ") { "Label for mvzType should be 'Тип МВЗ'" }
        assert(result.mvzType.value == "") { "Value for mvzType should be empty string" }
        assert(result.mvzType.children.size == 1) { "mvzType should have 1 child" }
        assert(result.mvzType.children[0].label == "Test MVZ Type") { "Label for mvzType child should be 'Test MVZ Type'" }
        assert(result.mvzType.children[0].value == "Test MVZ Type") { "Value for mvzType child should be 'Test MVZ Type'" }
        
        // Check territory
        assert(result.territory.label == "Территория") { "Label for territory should be 'Территория'" }
        assert(result.territory.value == "") { "Value for territory should be empty string" }
        assert(result.territory.children.size == 1) { "territory should have 1 child" }
        assert(result.territory.children[0].label == "Test Territory") { "Label for territory child should be 'Test Territory'" }
        assert(result.territory.children[0].value == 4) { "Value for territory child should be 4" }
    }
    
    @Test
    fun `test prepareLabel handles null and special values`() {
        // Create a repository with a real EntityManager to test the prepareLabel function
        val realRepository = GlobalFilterDictionaryRepository(entityManager)
        
        // Create mock tuples with different label values
        val mockTuple1 = mock(Tuple::class.java)
        `when`(mockTuple1["values"]).thenReturn(1)
        `when`(mockTuple1["labels"]).thenReturn(null)
        
        val mockTuple2 = mock(Tuple::class.java)
        `when`(mockTuple2["values"]).thenReturn(2)
        `when`(mockTuple2["labels"]).thenReturn("null")
        
        val mockTuple3 = mock(Tuple::class.java)
        `when`(mockTuple3["values"]).thenReturn(3)
        `when`(mockTuple3["labels"]).thenReturn("нет")
        
        val mockTuple4 = mock(Tuple::class.java)
        `when`(mockTuple4["values"]).thenReturn(4)
        `when`(mockTuple4["labels"]).thenReturn("Normal Value")
        
        // Test the toLabeledValue function which uses prepareLabel
        val result1 = realRepository.javaClass.getDeclaredMethod("toLabeledValue", Tuple::class.java)
            .apply { isAccessible = true }
            .invoke(realRepository, mockTuple1) as LabeledValue
        
        val result2 = realRepository.javaClass.getDeclaredMethod("toLabeledValue", Tuple::class.java)
            .apply { isAccessible = true }
            .invoke(realRepository, mockTuple2) as LabeledValue
        
        val result3 = realRepository.javaClass.getDeclaredMethod("toLabeledValue", Tuple::class.java)
            .apply { isAccessible = true }
            .invoke(realRepository, mockTuple3) as LabeledValue
        
        val result4 = realRepository.javaClass.getDeclaredMethod("toLabeledValue", Tuple::class.java)
            .apply { isAccessible = true }
            .invoke(realRepository, mockTuple4) as LabeledValue
        
        // Verify the results
        assert(result1.label == "Не указано") { "Label for null should be 'Не указано'" }
        assert(result1.value == 1) { "Value should be preserved" }
        
        assert(result2.label == "Не указано") { "Label for 'null' should be 'Не указано'" }
        assert(result2.value == 2) { "Value should be preserved" }
        
        assert(result3.label == "Не указано") { "Label for 'нет' should be 'Не указано'" }
        assert(result3.value == 3) { "Value should be preserved" }
        
        assert(result4.label == "Normal Value") { "Label for normal value should be preserved" }
        assert(result4.value == 4) { "Value should be preserved" }
    }
}