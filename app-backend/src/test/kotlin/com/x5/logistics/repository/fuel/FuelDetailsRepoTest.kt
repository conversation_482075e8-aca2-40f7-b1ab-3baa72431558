package com.x5.logistics.repository.fuel

import com.x5.logistics.TestApplication
import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.fuel.details.FuelDetailsColumn
import com.x5.logistics.rest.dto.fuel.details.FuelDetailsReq
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.math.BigDecimal
import java.time.LocalDate

@SpringBootTest(classes = [TestApplication::class])
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class FuelDetailsRepoTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var repo: FuelDetailsRepo

    private val baseRequest = FuelDetailsReq(
        pageNumber = 0,
        pageSize = 25,
        from = LocalDate.of(2022, 1, 1),
        to = LocalDate.of(2024, 1, 1),
        geoFilter = GeoFilter(
            mr = null,
            atp = null,
            mvz = null,
            retailNetwork = null,
            atpType = null,
            mvzType = null,
            territory = null
        ),
        columns = FuelDetailsColumn.entries,
        sort = emptyList(),
        filters = emptyList(),
        granularity = null
    )

    @Test
    fun testGetDetailsReturnNonEmptyResponse() {
        transaction {
            val resp = repo.getFuelDetailsData(baseRequest)
            assert(resp.second.isNotEmpty())
        }
    }

    @Test
    fun testFuelTypeFirstColumn() = transaction {
        val resp = repo.getFuelDetailsData(
            baseRequest.copy(
                columns = listOf(
                    FuelDetailsColumn.fuelTypeFirst
                )
            )
        )
        assert(resp.second.any { it.fuelTypeFirst == "Diesel" })
    }

    @Test
    fun testProbegWithGas() = transaction {
        val resp = repo.getFuelDetailsData(
            baseRequest.copy(
                columns = listOf(
                    FuelDetailsColumn.probegWithGas
                )
            )
        )
        assert(resp.second.size == 1)
        assert(resp.second.first().probegWithGas == BigDecimal.valueOf(204.0))
    }
}
