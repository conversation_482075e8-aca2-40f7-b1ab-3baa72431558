package com.x5.logistics.service.chart

import com.x5.logistics.TestApplication
import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.data.charting.ChartCompositeAggregateTable
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateCreateRequest
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateSettingsDto
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateSettingsParameterDto
import com.x5.logistics.rest.dto.charting.ChartCompositeAggregateUpdateRequest
import com.x5.logistics.rest.dto.charting.ChartValueType
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.service.charting.ChartCompositeAggregatesService
import com.x5.logistics.util.getLogger
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.insertAndGetId
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.LocalDateTime

@SpringBootTest(classes = [TestApplication::class])
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class ChartCompositeAggregatesServiceTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var service: ChartCompositeAggregatesService

    @Autowired
    private lateinit var db: Database
    val settingsDto = ChartCompositeAggregateSettingsDto(
        expressions = "test",
        valueType = ChartValueType.value,
        parameters = listOf(
            ChartCompositeAggregateSettingsParameterDto(
                name = "test",
                functions = "test",
                filters = emptyList()
            )
        )
    )

    @BeforeEach
    fun setUp() {
        transaction(db) {
            SchemaUtils.create(ChartCompositeAggregateTable)
            // Create chart_widget table for the SQL query in getUserAggregates
            exec(
                """
                create table if not exists chart_widget
                (
                    id                    bigint,
                    desk_id               bigint,
                    date_from             date,
                    date_to               date,
                    date_span             varchar,
                    mvz                   varchar,
                    settings              varchar                                                 not null,
                    create_date           date                                                    not null,
                    update_date           date    default CURRENT_DATE,
                    location              varchar                                                 not null,
                    name                  varchar                                                 not null,
                    type                  varchar                                                 not null,
                    geo_filter            varchar default '{}'::character varying,
                    global_filters_source jsonb   default '{}'::jsonb
                );
            """.trimIndent()
            )
        }
    }

    @AfterEach
    fun deleteAll() {
        transaction(db) {
            exec("drop table if exists chart_widget;")
            SchemaUtils.drop(ChartCompositeAggregateTable)
        }
    }

    @Test
    fun testGetAggregates() = transaction {
        ChartCompositeAggregateTable.insert {
            it[name] = "test"
            it[settings] = settingsDto
            it[deleted] = false
            it[createdBy] = "test"
            it[createdAt] = LocalDateTime.now()
        }.also { getLogger().debug(it.prepareSQL(this@transaction)) }
        ChartCompositeAggregateTable.insert {
            it[name] = "test1"
            it[settings] = settingsDto
            it[deleted] = false
            it[createdBy] = "test1"
            it[createdAt] = LocalDateTime.now()
        }
        val result = service.getUserAggregates("test")
        assertNotNull(result)
        assert(result.isNotEmpty())
        assert(result.all { it.name == "test" })
    }

    @Test
    fun testCreateAggregates() = transaction {
        val name = "testNew"
        val user = "testNew"
        val request = ChartCompositeAggregateCreateRequest(
            name = name,
            settings = settingsDto
        )
        val id = service.create(user, request)
        val fromDb = ChartCompositeAggregateTable.selectAll()
            .where { ChartCompositeAggregateTable.id eq id }
            .firstOrNull()
        assertNotNull(fromDb)
        assert(fromDb?.get(ChartCompositeAggregateTable.name) == name)
        assert(fromDb?.get(ChartCompositeAggregateTable.createdBy) == user)
    }

    @Test
    fun testUpdateAggregates() = transaction {
        val name = "testUpdate"
        val user = "testUpdate"
        val id = ChartCompositeAggregateTable.insertAndGetId {
            it[ChartCompositeAggregateTable.name] = name
            it[settings] = settingsDto
            it[deleted] = false
            it[createdBy] = user
            it[createdAt] = LocalDateTime.now()
        }.value
        val newName = name + "new"
        val longName = "test".repeat(20)
        val request = ChartCompositeAggregateUpdateRequest(
            id = id,
            name = newName,
            settings = settingsDto
        )
        assertThrows<WrongRequestDataException> { service.edit("not-$user", request) }
        assertThrows<WrongRequestDataException> { service.edit(user, request.copy(name = longName)) }
        assertDoesNotThrow { service.edit(user, request) }
        val fromDb = ChartCompositeAggregateTable.selectAll()
            .where { ChartCompositeAggregateTable.id eq id }
            .firstOrNull()
        assertNotNull(fromDb)
        assert(fromDb?.get(ChartCompositeAggregateTable.name) == newName)
    }


    @Test
    fun testDeleteAggregates() = transaction {
        val name = "testToDelete"
        val user = "testToDelete"
        val id = ChartCompositeAggregateTable.insertAndGetId {
            it[ChartCompositeAggregateTable.name] = name
            it[settings] = settingsDto
            it[deleted] = false
            it[createdBy] = user
            it[createdAt] = LocalDateTime.now()
        }.value
        assertThrows<WrongRequestDataException> { service.delete("not-$user", id) }
        assertDoesNotThrow { service.delete(user, id) }
        val fromDb =
            ChartCompositeAggregateTable.selectAll().where { ChartCompositeAggregateTable.id eq id }.firstOrNull()
        assert(fromDb?.get(ChartCompositeAggregateTable.deleted) == true)
    }
}