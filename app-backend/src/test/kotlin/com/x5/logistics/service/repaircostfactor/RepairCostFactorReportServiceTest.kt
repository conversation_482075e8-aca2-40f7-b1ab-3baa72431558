package com.x5.logistics.service.repaircostfactor

import com.x5.logistics.TestApplication
import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.data.PlsByDay
import com.x5.logistics.data.PlsByDayWithRate
import com.x5.logistics.data.RepairStructures
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtypesTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTypesTable
import com.x5.logistics.repository.Granularity
import com.x5.logistics.rest.dto.FilterCondition
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportColumn
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportColumnFilter
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportMassFilterReq
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportMassFilterReq.*
import com.x5.logistics.rest.dto.repaircostfactor.RepairCostFactorReportReq
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Instant
import java.time.LocalDate

@SpringBootTest(classes = [TestApplication::class])
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class RepairCostFactorReportServiceTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var service: RepairCostFactorReportService

    @Autowired
    private lateinit var db: Database

    @BeforeEach
    fun setUp() {
        transaction(db) {
            SchemaUtils.createMissingTablesAndColumns(
                ToroWorksTypesTable,
                ToroWorksSubtypesTable,
                ToroWorksTable,
                RepairStructures,
                PlsByDay,
                PlsByDayWithRate
            )

            PlsByDay.insert {
                it[vehicleDate] = LocalDate.of(2024, 6, 1)
                it[mvz] = "MVZ1"
                it[vin] = "VIN2"
                it[mileage] = 1.0
            }

            PlsByDay.insert {
                it[vehicleDate] = LocalDate.of(2024, 7, 1)
                it[mvz] = "MVZ1"
                it[vin] = "VIN2"
                it[mileage] = 1.0
            }

            PlsByDay.insert {
                it[vehicleDate] = LocalDate.of(2024, 6, 1)
                it[mvz] = "MVZ1"
                it[vin] = "VIN1"
                it[mileage] = 1.0
            }

            PlsByDay.insert {
                it[vehicleDate] = LocalDate.of(2024, 7, 1)
                it[mvz] = "MVZ1"
                it[vin] = "VIN1"
                it[mileage] = 1.0
            }

            PlsByDayWithRate.insert {
                it[vehicleDate] = LocalDate.of(2024, 6, 1)
                it[mvz] = "MVZ1"
                it[vin] = "VIN1"
                it[mileage] = 1.0
                it[rate] = 1.0
            }

            PlsByDayWithRate.insert {
                it[vehicleDate] = LocalDate.of(2024, 7, 1)
                it[mvz] = "MVZ1"
                it[vin] = "VIN1"
                it[mileage] = 1.0
                it[rate] = 1.0
            }

            RepairStructures.insert {
                it[repairStartDate] = LocalDate.of(2024, 6, 1)
                it[vehicleMvz] = "MVZ1"
                it[eventId] = "EVENT1"
                it[headFleetNum] = "VIN1"
                it[structureExpense] = 2.0
                it[vrt] = "Work1"
            }

            RepairStructures.insert {
                it[repairStartDate] = LocalDate.of(2024, 7, 1)
                it[vehicleMvz] = "MVZ1"
                it[eventId] = "EVENT2"
                it[headFleetNum] = "VIN1"
                it[structureExpense] = 2.0
                it[vrt] = "Work1"
            }

            ToroWorksTypesTable.insert {
                it[id] = 1
                it[name] = "Type1"
                it[createdBy] = "User1"
                it[updatedBy] = "User1"
                it[createdAt] = Instant.now()
                it[updatedAt] = Instant.now()
                it[deleted] = false
            }

            ToroWorksSubtypesTable.insert {
                it[id] = 1
                it[name] = "Subtype1"
                it[color] = "Color1"
                it[createdBy] = "User1"
                it[updatedBy] = "User1"
                it[createdAt] = Instant.now()
                it[updatedAt] = Instant.now()
                it[deleted] = false
            }

            ToroWorksTable.insert {
                it[id] = "Work1"
                it[name] = "Work1"
                it[subtypeId] = 1
                it[typeId] = 1
                it[updatedBy] = "User1"
                it[updatedAt] = Instant.now().atOffset(java.time.ZoneOffset.UTC)
                it[useNh] = false
            }
        }
    }

    @AfterEach
    fun tearDown() {
        transaction(db) {
            // Clear data after each test to ensure isolation
            try {
                PlsByDayWithRate.deleteAll()
                PlsByDay.deleteAll()
                ToroWorksTable.deleteAll()
                RepairStructures.deleteAll()
                ToroWorksSubtypesTable.deleteAll()
                ToroWorksTypesTable.deleteAll()
            } catch (e: Exception) {
                // Ignore errors during cleanup
            }
        }
    }

    @Test
    fun `test getReport works with granularity`() {
        val req = RepairCostFactorReportReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(RepairCostFactorReportColumn.repairExpensesFullPlan),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            granularity = Granularity.MONTH,
            sort = emptyList(),
            filters = emptyList(),
            pageNumber = 0,
            pageSize = 1
        )
        val resp = runBlocking {
            service.getReport(req)
        }

        with(resp.items.first()) {
            assert(granularityRepairExpensesFullPlan?.isNotEmpty() == true)
        }
    }

    @Test
    fun `test getReport works with grouping and granularity`() {
        val req = RepairCostFactorReportReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(
                RepairCostFactorReportColumn.vehicleVin,
                RepairCostFactorReportColumn.repairExpensesFullPlan
            ),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            granularity = Granularity.MONTH,
            sort = emptyList(),
            filters = emptyList(),
            pageNumber = 0,
            pageSize = 1
        )
        val resp = runBlocking {
            service.getReport(req)
        }

        with(resp.items.first()) {
            assert(granularityRepairExpensesFullPlan?.isNotEmpty() == true)
            assert(vehicleVin.equals("VIN1"))
        }
    }

    @Test
    fun `test getReport works with grouping pl and rate`() {
        val req = RepairCostFactorReportReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(
                RepairCostFactorReportColumn.vehicleVin,
                RepairCostFactorReportColumn.mileage,
                RepairCostFactorReportColumn.repairExpensesFullPlan
            ),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            granularity = null,
            sort = emptyList(),
            filters = emptyList(),
            pageNumber = 0,
            pageSize = 25
        )
        val resp = runBlocking {
            service.getReport(req)
        }

        with(resp.items) {
            assert(size == 2)
            assert(find { it.vehicleVin == "VIN2" }?.mileage != null)
            assert(find { it.vehicleVin == "VIN1" }?.repairExpensesFullPlan != null)
        }
    }

    @Test
    fun `test getReport works with grouping pl and rate with granularity`() {
        val req = RepairCostFactorReportReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(
                RepairCostFactorReportColumn.vehicleVin,
                RepairCostFactorReportColumn.mileage,
                RepairCostFactorReportColumn.repairExpensesFullPlan
            ),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            granularity = Granularity.REPORTING_WEEK,
            sort = emptyList(),
            filters = listOf(
                RepairCostFactorReportColumnFilter(
                    name = RepairCostFactorReportColumn.vehicleVin,
                    condition = FilterCondition.equal,
                    value = listOf("VIN1")
                )
            ),
            pageNumber = 0,
            pageSize = 25
        )
        val resp = runBlocking {
            service.getReport(req)
        }

        with(resp.items.first()) {
            assert(mileage != null)
            assert(vehicleVin == "VIN1")
            assert(repairExpensesFullPlan != null)
            assert(granularityRepairExpensesFullPlan?.isNotEmpty() == true)
        }
    }

    @Test
    fun `test getReport works with grouping pl and rate and repair`() {
        val req = RepairCostFactorReportReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(
                RepairCostFactorReportColumn.vehicleVin,
                RepairCostFactorReportColumn.eventId,
                RepairCostFactorReportColumn.mileage,
                RepairCostFactorReportColumn.repairRubDeviation,
                RepairCostFactorReportColumn.repairExpensesFullPlan
            ),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            granularity = null,
            sort = emptyList(),
            filters = listOf(
                RepairCostFactorReportColumnFilter(
                    name = RepairCostFactorReportColumn.vehicleVin,
                    condition = FilterCondition.equal,
                    value = listOf("VIN1")
                ),
                RepairCostFactorReportColumnFilter(
                    name = RepairCostFactorReportColumn.eventId,
                    condition = FilterCondition.equal,
                    value = listOf("EVENT1")
                )
            ),
            pageNumber = 0,
            pageSize = 25
        )
        val resp = runBlocking {
            service.getReport(req)
        }

        with(resp.items) {
            assert(size == 1)
            assert(first().vehicleVin == "VIN1")
            assert(first().eventId == "EVENT1")
            assert(first().mileage != null)
            assert(first().repairRubDeviation != 0.0)
            assert(first().repairExpensesFullPlan != null)
        }
    }

    @Test
    fun `test getReport works with grouping pl and rate and repair with granularity`() {
        val req = RepairCostFactorReportReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(
                RepairCostFactorReportColumn.vehicleVin,
                RepairCostFactorReportColumn.eventId,
                RepairCostFactorReportColumn.mileage,
                RepairCostFactorReportColumn.repairRubDeviation,
                RepairCostFactorReportColumn.repairExpensesFullDeviation,
                RepairCostFactorReportColumn.repairExpensesFullPlan
            ),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            granularity = Granularity.DAY,
            sort = emptyList(),
            filters = listOf(
                RepairCostFactorReportColumnFilter(
                    name = RepairCostFactorReportColumn.vehicleVin,
                    condition = FilterCondition.equal,
                    value = listOf("VIN1")
                ),
                RepairCostFactorReportColumnFilter(
                    name = RepairCostFactorReportColumn.eventId,
                    condition = FilterCondition.equal,
                    value = listOf("EVENT1")
                )
            ),
            pageNumber = 0,
            pageSize = 25
        )
        val resp = runBlocking {
            service.getReport(req)
        }

        with(resp.items) {
            assert(size == 1)
            assert(first().vehicleVin == "VIN1")
            assert(first().eventId == "EVENT1")
            assert(first().mileage != null)
            assert(first().repairRubDeviation != 0.0)
            assert(first().repairExpensesFullPlan != null)
            assert(first().repairExpensesFullDeviation != null)
            assert(first().granularityRepairExpensesFullPlan?.isNotEmpty() == true)
            assert(first().granularityRepairExpensesFullDeviation?.isNotEmpty() == true)
            assert(first().granularityRepairRubDeviation?.isNotEmpty() == true)
        }
    }

    @Test
    fun `test getFilterValues works`() {
        val req = RepairCostFactorReportMassFilterReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(RepairCostFactorReportColumn.vehicleVin),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            filters = emptyList(),
            request = Request(
                name = RepairCostFactorReportColumn.vehicleVin,
                value = listOf()
            )
        )
        val resp = runBlocking {
            service.getFilterValues(req)
        }

        with(resp) {
            assert(map { it.value }.any { it in listOf("VIN1", "VIN2") })
            assert(map { it.label }.any { it in listOf("VIN1", "VIN2") })
        }
    }

    @Test
    fun `test getMassFilter works`() {
        val req = RepairCostFactorReportMassFilterReq(
            from = LocalDate.of(2024, 6, 1),
            to = LocalDate.of(2024, 7, 1),
            columns = listOf(RepairCostFactorReportColumn.vehicleVin),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            filters = emptyList(),
            request = Request(
                name = RepairCostFactorReportColumn.vehicleVin,
                value = listOf("VIN1")
            )
        )
        val resp = runBlocking {
            service.getMassFilter(req)
        }

        with(resp) {
            assert(userValuesCount == 1)
            assert(foundValuesCount == 1)
            assert(notFoundValuesCount == 0)
            assert(duplicateValuesCount == 0)
            assert(valueResult.size == 1)
            assert(valueResult.first().value == "VIN1")
        }
    }
}