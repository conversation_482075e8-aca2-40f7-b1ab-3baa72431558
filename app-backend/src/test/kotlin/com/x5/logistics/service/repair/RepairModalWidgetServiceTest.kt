package com.x5.logistics.service.repair

import com.x5.logistics.TestApplication
import com.x5.logistics.TestPostgresqlConfig
import com.x5.logistics.config.TestDatabaseConfig
import com.x5.logistics.data.dictionary.OrganizationalUnitsTimelineTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksSubtypesTable
import com.x5.logistics.data.dictionary.vrt.ToroWorksTable
import com.x5.logistics.data.repair.PlsByDayGroup
import com.x5.logistics.data.repair.RepairRatesView
import com.x5.logistics.data.repair.RepairStructures
import com.x5.logistics.rest.dto.GeoFilter
import com.x5.logistics.rest.dto.repair.modal.RepairModalWidgetReq
import com.x5.logistics.rest.dto.repair.modal.RepairType
import com.x5.logistics.util.PRECISION_THRESHOLD
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.transactions.transaction
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime

@SpringBootTest(classes = [TestApplication::class])
@Testcontainers
@ActiveProfiles("test")
@Import(TestDatabaseConfig::class)
class RepairModalWidgetServiceTest : TestPostgresqlConfig() {
    @Autowired
    private lateinit var service: RepairModalWidgetService

    @Autowired
    private lateinit var db: Database


    @BeforeEach
    fun setUp() {
        transaction(db) {
            SchemaUtils.drop(
                OrganizationalUnitsTimelineTable,
                RepairStructures,
                PlsByDayGroup,
                ToroWorksTable,
                ToroWorksSubtypesTable,
                RepairRatesView,
                OrganizationalUnitsTimelineTable,
            )
            SchemaUtils.create(
                OrganizationalUnitsTimelineTable,
                RepairStructures,
                PlsByDayGroup,
                ToroWorksTable,
                ToroWorksSubtypesTable,
                RepairRatesView,
                OrganizationalUnitsTimelineTable,
            )
            OrganizationalUnitsTimelineTable.insert {
                it[mvzId] = "MVZ1"
                it[startDate] = LocalDate.of(2023, 1, 1)
                it[endDate] = LocalDate.of(2024, 1, 1)
                it[mrId] = 1001L
                it[atpId] = 2001L
                it[retailNetwork] = "RetailNetwork1"
                it[atpType] = "ATPType1"
                it[mvzType] = "MVZType1"
                it[repshopId] = 3001L
                it[repshopName] = "RepshopName1"
                it[atpName] = "ATPName1"
                it[mvzName] = "MVZName1"
                it[mrName] = "MRName1"
                it[territoryId] = 4001L
                it[territoryName] = "TerritoryName1"
            }
            OrganizationalUnitsTimelineTable.insert {
                it[mvzId] = "MVZ2"
                it[startDate] = LocalDate.of(2023, 1, 1)
                it[endDate] = LocalDate.of(2024, 1, 1)
                it[mrId] = 1002L
                it[atpId] = 2002L
                it[retailNetwork] = "RetailNetwork2"
                it[atpType] = "ATPType2"
                it[mvzType] = "MVZType2"
                it[repshopId] = 3001L
                it[repshopName] = "RepshopName2"
                it[atpName] = "ATPName2"
                it[mvzName] = "MVZName2"
                it[mrName] = "MRName2"
                it[territoryId] = 4002L
                it[territoryName] = "TerritoryName2"
            }
            OrganizationalUnitsTimelineTable.insert {
                it[mvzId] = "MVZ3"
                it[startDate] = LocalDate.of(2023, 1, 1)
                it[endDate] = LocalDate.of(2024, 1, 1)
                it[mrId] = 1003L
                it[atpId] = 2003L
                it[retailNetwork] = "RetailNetwork3"
                it[atpType] = "ATPType3"
                it[mvzType] = "MVZType3"
                it[repshopId] = 3003L
                it[repshopName] = "RepshopName3"
                it[atpName] = "ATPName3"
                it[mvzName] = "MVZName3"
                it[mrName] = "MRName3"
                it[territoryId] = 4003L
                it[territoryName] = "TerritoryName3"
            }

            RepairStructures.insert {
                it[mvzId] = "MVZ1"
                it[startDate] = LocalDate.of(2023, 6, 1)
                it[structureExpense] = 1000.0
                it[marka] = "Toyota"
                it[tonnage] = 10.5.toBigDecimal()
                it[tsGroup] = "Основное"
                it[structureName] = "Запчасти"
                it[orderId] = 12345L
                it[equnr] = 67890L
                it[tiresExpenses] = 300.0
                it[tiresPartsExpenses] = 150.0
                it[tiresServicesExpenses] = 450.0
                it[vrt] = "VRT2"
            }
            RepairStructures.insert {
                it[mvzId] = "MVZ2"
                it[startDate] = LocalDate.of(2023, 6, 1)
                it[structureExpense] = 2000.0
                it[marka] = "Toyota"
                it[tonnage] = 10.5.toBigDecimal()
                it[tsGroup] = "Основное"
                it[structureName] = "Шины"
                it[orderId] = 12345L
                it[equnr] = 67890L
                it[tiresExpenses] = 300.0
                it[tiresPartsExpenses] = 150.0
                it[tiresServicesExpenses] = 450.0
                it[vrt] = "VRT2"
            }
            RepairStructures.insert {
                it[mvzId] = "MVZ3"
                it[startDate] = LocalDate.of(2023, 6, 1)
                it[structureExpense] = 300000.0
                it[marka] = "Toyota"
                it[tonnage] = 10.5.toBigDecimal()
                it[tsGroup] = "Вспомогательное"
                it[structureName] = "Шины"
                it[orderId] = 12345L
                it[equnr] = 67890L
                it[tiresExpenses] = 300.0
                it[tiresPartsExpenses] = 150.0
                it[tiresServicesExpenses] = 450.0
                it[vrt] = "VRT3"
            }

            PlsByDayGroup.insert {
                it[mvzId] = "MVZ1"
                it[vehicleDate] = LocalDate.of(2023, 6, 1)
                it[mvzName] = "MVZ1"
                it[marka] = "Toyota"
                it[tsGroup] = "Group1"
                it[plTonnage] = 10.5.toBigDecimal()
                it[qmnumCount] = 5
                it[equnrCount] = 3
                it[bequiCount] = 2
                it[mileage] = 1000.0
            }
            PlsByDayGroup.insert {
                it[mvzId] = "MVZ2"
                it[vehicleDate] = LocalDate.of(2023, 6, 1)
                it[mvzName] = "MVZ2"
                it[marka] = "Toyota"
                it[tsGroup] = "Group2"
                it[plTonnage] = 10.5.toBigDecimal()
                it[qmnumCount] = 5
                it[equnrCount] = 3
                it[bequiCount] = 2
                it[mileage] = 1000.0
            }
            PlsByDayGroup.insert {
                it[mvzId] = "MVZ3"
                it[vehicleDate] = LocalDate.of(2023, 6, 1)
                it[mvzName] = "MVZ3"
                it[marka] = "Toyota"
                it[tsGroup] = "Group3"
                it[plTonnage] = 10.5.toBigDecimal()
                it[qmnumCount] = 5
                it[equnrCount] = 3
                it[bequiCount] = 2
                it[mileage] = 1000.0
            }

            ToroWorksTable.insert {
                it[id] = "Work1"
                it[name] = "Work1"
                it[subtypeId] = 1
                it[typeId] = 101
                it[updatedBy] = "Admin"
                it[updatedAt] = OffsetDateTime.now()
                it[useNh] = true
            }
            ToroWorksTable.insert {
                it[id] = "Work2"
                it[name] = "Work2"
                it[subtypeId] = 2
                it[typeId] = 102
                it[updatedBy] = "User"
                it[updatedAt] = OffsetDateTime.now()
                it[useNh] = false
            }
            ToroWorksTable.insert {
                it[id] = "Work3"
                it[name] = "Work3"
                it[subtypeId] = 3
                it[typeId] = 103
                it[updatedBy] = "Manager"
                it[updatedAt] = OffsetDateTime.now()
                it[useNh] = true
            }

            ToroWorksSubtypesTable.insert {
                it[id] = 1
                it[name] = "Subtype1"
                it[color] = "Red"
                it[createdBy] = "Admin"
                it[createdAt] = Instant.now()
                it[updatedBy] = "Admin"
                it[updatedAt] = Instant.now()
                it[deleted] = false
            }
            ToroWorksSubtypesTable.insert {
                it[id] = 2
                it[name] = "Subtype2"
                it[color] = "Blue"
                it[createdBy] = "Admin"
                it[createdAt] = Instant.now()
                it[updatedBy] = "Admin"
                it[updatedAt] = Instant.now()
                it[deleted] = false
            }
            ToroWorksSubtypesTable.insert {
                it[id] = 3
                it[name] = "Subtype3"
                it[color] = "Green"
                it[createdBy] = "Admin"
                it[createdAt] = Instant.now()
                it[updatedBy] = "Admin"
                it[updatedAt] = Instant.now()
                it[deleted] = false
            }

            RepairRatesView.insert {
                it[atpId] = 1
                it[startDate] = LocalDate.of(2023, 1, 1)
                it[endDate] = LocalDate.of(2024, 1, 1)
                it[tonnage] = 10.5.toBigDecimal()
                it[rate] = 1.5
                it[structureName] = "Repair Facility 1"
            }
            RepairRatesView.insert {
                it[atpId] = 2
                it[startDate] = LocalDate.of(2023, 1, 1)
                it[endDate] = LocalDate.of(2024, 1, 1)
                it[tonnage] = 10.5.toBigDecimal()
                it[rate] = 1.5
                it[structureName] = "Repair Facility 2"
            }
            RepairRatesView.insert {
                it[atpId] = 3
                it[startDate] = LocalDate.of(2023, 1, 1)
                it[endDate] = LocalDate.of(2024, 1, 1)
                it[tonnage] = 10.5.toBigDecimal()
                it[rate] = 1.5
                it[structureName] = "Repair Facility 3"
            }
        }
    }

    @Test
    fun `test getData works`() {
        val req = RepairModalWidgetReq(
            from = LocalDate.of(2023, 1, 1),
            to = LocalDate.of(2024, 1, 1),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            repairType = RepairType.TOTAL
        )
        val resp = runBlocking {
            service.getData(req)
        }

        with(resp) {
            assert(totalRepairInfo.repairSum != 0.0)
            assert(vehicleGroupMain.isNotEmpty())
            assert(vehicleGroupSub.isNotEmpty())
            assert(typeRepair.isNotEmpty())
        }
    }

    @Test
    fun `test total expenses eq sum of main and sub expenses works`() {
        val req = RepairModalWidgetReq(
            from = LocalDate.of(2023, 1, 1),
            to = LocalDate.of(2024, 1, 1),
            geoFilter = GeoFilter(
                mr = null,
                atp = null,
                mvz = null,
                retailNetwork = null,
                atpType = null,
                mvzType = null,
                territory = null
            ),
            repairType = RepairType.TOTAL
        )
        val resp = runBlocking {
            service.getData(req)
        }

        with(resp) {
            println(totalRepairInfoGroupSub.repairSum)
            println(totalRepairInfoGroupSub.repairSum)
            println(totalRepairInfoGroupMain.repairSum + totalRepairInfoGroupSub.repairSum)
            println(totalRepairInfo.repairSum)
            assert((totalRepairInfoGroupMain.repairSum + totalRepairInfoGroupSub.repairSum) - totalRepairInfo.repairSum < PRECISION_THRESHOLD)
        }
    }
}