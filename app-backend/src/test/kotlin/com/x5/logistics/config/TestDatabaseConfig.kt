package com.x5.logistics.config

import com.x5.logistics.repository.AtpLogRepo
import com.x5.logistics.repository.DictionaryRepository
import jakarta.persistence.EntityManagerFactory
import org.jetbrains.exposed.spring.autoconfigure.ExposedAutoConfiguration
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.DatabaseConfig
import org.springframework.boot.SpringBootConfiguration
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter
import org.springframework.transaction.TransactionManager
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.util.*
import javax.sql.DataSource

@TestConfiguration(proxyBeanMethods = false)
@SpringBootConfiguration
@ImportAutoConfiguration(
    value = [ExposedAutoConfiguration::class, HibernateJpaAutoConfiguration::class],
    exclude = [DataSourceTransactionManagerAutoConfiguration::class]
)
@EnableTransactionManagement
class TestDatabaseConfig {


    @Bean("database")
    @Primary
    fun testDatabase(dataSource: DataSource): Database {
        return Database.connect(dataSource)
    }

    @Bean
    fun databaseConfig() = DatabaseConfig {
        useNestedTransactions = true
    }

    @Bean
    @Primary
    fun entityManagerFactory(dataSource: DataSource): LocalContainerEntityManagerFactoryBean {
        val em = LocalContainerEntityManagerFactoryBean()
        em.dataSource = dataSource
        em.setPackagesToScan("com.x5.logistics")
        em.jpaVendorAdapter = HibernateJpaVendorAdapter()

        val properties = Properties()
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect")
        properties.setProperty("hibernate.hbm2ddl.auto", "none")
        properties.setProperty("hibernate.show_sql", "false")
        em.setJpaProperties(properties)

        return em
    }

    @Bean
    @Primary
    fun transactionManager(entityManagerFactory: EntityManagerFactory): TransactionManager {
        val transactionManager = JpaTransactionManager()
        transactionManager.entityManagerFactory = entityManagerFactory
        return transactionManager
    }

    @Bean("monthLocale")
    fun monthLocale(): Locale {
        return Locale("ru")
    }

    @MockBean
    lateinit var dictionaryRepository: DictionaryRepository

    @MockBean
    lateinit var atpLogRepo: AtpLogRepo
}