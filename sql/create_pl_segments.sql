--drop table temp_pl_segments;
create table temp_pl_segments as
with q_ttn as (
	select --DISTINCT 
		pass_number AS qmnum -- ПЛ из ТТН
		, 'ttn' AS lable
		, cast(COALESCE(TRIPKEY, -1) as bigint) AS TRIP_NUM_OTD
		, cast(ID_ROUTE as bigint) AS ID_ROUTE -- Рейс №
		, cast(null as bigint) AS ordnum
		, werks AS point_from -- откуда
		, werks_name AS point_from_name
		, werks_type AS point_from_type
		, kunnr AS point_to -- куда
		, kunnr_name AS point_to_name
		, kunnr_type AS point_to_type
		, ship_date --дата начала рейса    
		, ship_time --время начала рейса
		/*
		*/
	from public.vttk_ttn_batch ttn 
)
, q_werks as (
	SELECT --distinct
		PL_NUM AS qmnum
		, 'vttk' AS lable
		, COALESCE(TRIP_NUM_OTD, -1) AS TRIP_NUM_OTD
		, ID_ROUTE -- Рейс №
		, ordnum
		, point_from -- откуда
		, point_from_name
		, point_from_type
		, point_to -- куда
		, point_to_name
		, point_to_type
		, STRMN as ship_date --дата начала рейса (план)
		, STRUR as ship_time --время начала рейса (план)
		/*
		*/
	FROM public.vttk_werks_batch
)
, q_pl as (
	select qmnum
		, equnr
		, user_stat
		, msaus 
		, CAST(strmn AS timestamp without time zone) 
			+ CAST(strur AS time) as start_date
		, CAST((CASE WHEN user_stat <> 'ЧЗАК' AND user_stat <> 'ЗАКР' THEN ltrmn ELSE bezdt END) AS timestamp without time zone) 
			+ CAST((CASE WHEN user_stat <> 'ЧЗАК' AND user_stat <> 'ЗАКР' THEN ltrur ELSE bezur END) AS time) as end_date
	from pl_dt pl 
	WHERE pl_deleted = false
	  AND pl_otl = false
	  AND user_stat <> 'СТРН'
)
, q_trip_points as (
	select u.qmnum
		, q_pl.equnr
		, u.lable
		, u.trip_num_otd
		, u.id_route
		, u.ordnum
		, u.point_from
		, u.point_from_name
		, u.point_from_type
		, u.point_to
		, u.point_to_name
		, u.point_to_type
		, cast( u.ship_date as timestamp) + cast( u.ship_time as time) as ship_date
		, q_pl.start_date as pl_start_date
		, q_pl.end_date as pl_end_date
	from q_pl 
		join (
			select * from q_ttn
			union all
			select * from q_werks) u on u.qmnum = q_pl.qmnum
)
, q_trips as (
	select tp.qmnum
		, tp.equnr
		, tp.trip_num_otd 
		, tp.id_route 
		, tp.remont_flag
		, rank() over (partition by tp.qmnum order by tp.ship_date) as rank
		, tp.ship_date as start_date
		, lag(ship_date, -1, pl_end_date) over (partition by tp.qmnum order by tp.ship_date) end_date
		, pl_start_date
		, pl_end_date
	from (
		select distinct tp.qmnum
			, tp.equnr
			, tp.trip_num_otd 
			, tp.id_route 
			, first_value( cast( tp.ship_date as timestamp)) over (partition by tp.qmnum, tp.trip_num_otd, tp.id_route order by tp.ship_date rows between unbounded preceding and unbounded following) as ship_date
			, bool_or( point_from_type='02') over (partition by tp.qmnum, tp.trip_num_otd, tp.id_route order by tp.ship_date rows between unbounded preceding and unbounded following)
				or bool_or( point_to_type='02') over (partition by tp.qmnum, tp.trip_num_otd, tp.id_route order by tp.ship_date rows between unbounded preceding and unbounded following) as remont_flag
			, pl_start_date
			, pl_end_date
		from q_trip_points tp) tp 
	)
, q_trips_and_idles as (
	select qmnum 
		, equnr
		, trip_num_otd
		, remont_flag
		, rank
		, false as idle_flag
		, start_date
		, end_date
	from q_trips
	union
	select qmnum
		, equnr
		, null as trip_num_otd
		, false as remont_flag
		, 0 as rank
		, start_date - pl_start_date > cast('12 hour' as interval) as idle_flag
		, pl_start_date as start_date
		, start_date as end_date
	from q_trips
	where rank = 1 and start_date <> pl_start_date
)
, q_pl_segments as (
	select distinct qmnum
		, equnr
		, remont_flag
		, idle_flag
		, min( rank) over (partition by qmnum, remont_flag, idle_flag) as rank
		, min( start_date) over (partition by qmnum, remont_flag, idle_flag) as start_date
		, max( end_date) over (partition by qmnum, remont_flag, idle_flag) as end_date
	from q_trips_and_idles
)
select * from q_pl_segments

create index if not exists idx_temp_pl_segments_1 on temp_pl_segments(equnr, qmnum);

