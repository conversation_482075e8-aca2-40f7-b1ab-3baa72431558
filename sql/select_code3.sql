with q_series as (
	select granularity_period 
		, period_number
		, period_start_date
		, period_end_date
		, max(cast(period_end_date as date)) over () as whole_period_end_date
		, extract( 'epoch' from cast(period_end_date as timestamp) - cast(period_start_date as timestamp)) as ts_quantity_denom
		, extract( 'epoch' from max(cast(period_end_date as timestamp)) over () - min(cast(period_start_date as timestamp)) over()) as ts_quantity_denom_whole_period
	from (
		select :interval as granularity_period
			, row_number() over (order by series) as period_number
			, date(series) as period_start_date 
			, date( lead( cast(series as date), 1) over (order by series)) as period_end_date
		from generate_series( cast(:start_date as date), cast(:end_date as date), cast(case when :interval='whole period' then ((cast(:end_date as date)-cast(:start_date as date))||' days') else :interval end as interval)) as series
	) _
	where period_end_date is not null -- исключить "хвост" между последним целым периодом и :end_date
)
, q_ground_truth as (
	select s.granularity_period
		, s.period_number
		, s.period_start_date
		, s.period_end_date
		, s.ts_quantity_denom
		, s.ts_quantity_denom_whole_period
		, s.whole_period_end_date
		, t.mr_id 
		, t.atp_id
		, t.mvz_id
		, t.equnr
		, case when t.ts_end_date > s.period_end_date then 1 else 0 end as ts_quantity_eod
		, extract( 'epoch' from least(cast(s.period_end_date as timestamp), t.end_date) - greatest(cast(s.period_start_date as timestamp), t.start_date)) 
			/
		  count(1) over (partition by t.start_date, t.end_date, t.mvz_id, t.atp_id, t.mr_id, t.equnr, s.period_start_date, s.period_end_date) as ts_quantity_nom
		, kip_flag
		, ktg_flag
		, remont_flag
		, idle_flag
		, no_trips_flag
	from ts_usage_timeline t
		join q_series s on t.start_date < s.period_end_date and s.period_start_date < t.end_date and (t.mr_id, t.atp_id, t.mvz_id) in (:mvz_triples)
	where t.start_date < cast(:end_date as date) and cast(:start_date as date) < t.end_date 
)
, q_uniq_ts as (
	select * from q_series
	cross join (
		select distinct t.mr_id 
			, t.atp_id
			, t.mvz_id
			, t.equnr
		from ts_usage_timeline t
		where t.start_date < cast(:end_date as date) and cast(:start_date as date) < t.end_date and (t.mr_id, t.atp_id, t.mvz_id) in (:mvz_triples)) _
)
, q_details as (
	select granularity_period
		, period_number
		, period_start_date
		, period_end_date
		, ts_quantity_denom
		, ts_quantity_denom_whole_period
		, whole_period_end_date
		, mr_id 
		, atp_id
		, mvz_id
		, equnr 
		, max( ts_quantity_eod) as ts_quantity_eod
		, sum( ts_quantity_nom) as ts_quantity_nom
		, sum( ts_quantity_nom * kip_flag) as ts_kip_nom
		, sum( ts_quantity_nom * remont_flag) as ts_kip_nom_remont
		, sum( ts_quantity_nom * idle_flag) as ts_kip_nom_idle
		, sum( ts_quantity_nom * no_trips_flag) as ts_kip_nom_no_trips
		, sum( ts_quantity_nom * ktg_flag) as ts_ktg_nom
	from q_ground_truth
	group by granularity_period
		, period_number
		, period_start_date
		, period_end_date
		, ts_quantity_denom
		, ts_quantity_denom_whole_period
		, whole_period_end_date
		, mr_id 
		, atp_id
		, mvz_id
		, equnr 
)
, q_details_with_names as (
	select uts.granularity_period
		, uts.period_number
		, uts.period_start_date
		, uts.period_end_date
		, t.ts_group
		, t.ts_type 
		, mr.name as mr_name
		, atp.name as atp_name
		, mvz.uid as mvz_id
		, mvz.name as mvz_name
		, tsd.marka 
		, tsd.model 
		, tsd.gbo 
		, tsd.baujj as model_year
		, tsd.load_wgt 
		, tsd.license_num 
		, tsd.equnr 
		, tsd.chassis_num 
		, coalesce( d.ts_quantity_eod, 0) as ts_quantity_eod
		, coalesce( d.ts_quantity_nom, 0) as ts_quantity_nom
		, uts.ts_quantity_denom
		, uts.ts_quantity_denom_whole_period
		, uts.whole_period_end_date
		, d.ts_kip_nom
		, d.ts_kip_nom_remont
		, d.ts_kip_nom_idle
		, d.ts_kip_nom_no_trips
		, d.ts_ktg_nom
	from q_uniq_ts uts 
		join macro_region mr on mr.id = uts.mr_id
		join atp on atp.id = uts.atp_id
		join (select distinct uid, name from mvz_log) mvz on mvz.uid = uts.mvz_id
		join ts_data tsd on tsd.equnr = uts.equnr
		join types as t on t.ts_type = tsd.ts_type 
		left join q_details d on d.equnr = uts.equnr and d.period_number = uts.period_number and (d.mr_id, d.atp_id, d.mvz_id) = (uts.mr_id, uts.atp_id, uts.mvz_id)
)
, q_report as (
	select distinct granularity_period 
		, period_number
		, period_start_date
		, period_end_date
		, md5(concat_ws('|' :columns)) as guid
		:columns
		, ts_quantity_avg
		, ts_quantity_eod
		, ts_kip
		, ts_ktg
		, ts_quantity_avg_whole_period
		, ts_quantity_eod_whole_period
		, ts_kip_whole_period
		, ts_ktg_whole_period
		, ts_quantity_avg_total
		, ts_quantity_eod_total
		, ts_kip_total
		, ts_ktg_total
	from (
		select granularity_period
			, period_number
			, period_start_date
			, period_end_date
			, ts_group
			, ts_type 
			, mr_name
			, atp_name
			, mvz_id
			, mvz_name
			, marka 
			, model 
			, gbo 
			, model_year
			, load_wgt 
			, license_num 
			, equnr 
			, chassis_num 
			, sum( ts_quantity_nom) over (partition by granularity_period
					:columns
					, period_number
				) 
				/ ts_quantity_denom as ts_quantity_avg
			, sum( ts_quantity_eod) over (partition by granularity_period
					:columns
					, period_number
				) as ts_quantity_eod
			, sum( ts_kip_nom) over (partition by granularity_period
					:columns
					, period_number
				) 
				/
				sum( ts_quantity_nom) over (partition by granularity_period
					:columns
					, period_number
				) as ts_kip
			, sum( ts_ktg_nom) over (partition by granularity_period
					:columns
					, period_number
				) 
				/
				sum( ts_quantity_nom) over (partition by granularity_period
					:columns
					, period_number
				) as ts_ktg
			, sum( ts_quantity_nom) over (partition by granularity_period
				:columns
				) 
				/ ts_quantity_denom_whole_period as ts_quantity_avg_whole_period
			, sum( ts_quantity_eod) over (partition by granularity_period
					:columns
				)
				/ max(period_number) over() 
				as ts_quantity_eod_whole_period
			, sum( ts_kip_nom) over (partition by granularity_period
				:columns
				) 
				/
				sum( ts_quantity_nom) over (partition by granularity_period
					:columns
				) as ts_kip_whole_period
			, sum( ts_ktg_nom) over (partition by granularity_period
				:columns
				) 
				/
				sum( ts_quantity_nom) over (partition by granularity_period
					:columns
				) as ts_ktg_whole_period
			, sum( ts_quantity_nom / ts_quantity_denom) over() / max(period_number) over() as ts_quantity_avg_total
			, sum( case when period_end_date = whole_period_end_date then ts_quantity_eod else 0 end) over () as ts_quantity_eod_total
            , sum( ts_kip_nom) over() / sum( ts_quantity_nom) over () as ts_kip_total
            , sum( ts_ktg_nom) over() / sum( ts_quantity_nom) over () as ts_ktg_total
		from q_details_with_names ) _
	order by granularity_period
		:order_by
		, guid
		, period_number
)
, q_report_agg as (
	select
		  sum( ts_quantity_nom / ts_quantity_denom) / max(period_number) as ts_quantity_avg
		, sum( case when period_end_date = whole_period_end_date then ts_quantity_eod else 0 end) as ts_quantity_eod
		, sum( ts_kip_nom) / sum( ts_quantity_nom) as ts_kip
		, sum( ts_kip_nom_remont) / sum( ts_quantity_nom) as ts_kip_remont
		, sum( ts_kip_nom_idle) / sum( ts_quantity_nom) as ts_kip_idle
		, sum( ts_kip_nom_no_trips) / sum( ts_quantity_nom) as ts_kip_no_trips
		, sum( ts_ktg_nom) / sum( ts_quantity_nom) as ts_ktg
	from q_details_with_names
)
select * from q_report_agg --5870.*************	0.5872896037680858	0.0031164352933229014	0.00972891268796614	0.4226873055810874	0.2767433623999977
--select * from q_report
where 1=1
--	and equnr in (**********)

/*
, ts_group, ts_type , mr_name, atp_name, mvz_id, mvz_name, marka, model, gbo, model_year, load_wgt, license_num, equnr, chassis_num
, ts_group, ts_type
, ts_type, ts_group
*/

	-- '2021-05-30' '2021-06-02'
--2TCTAIN0
--(3, 7, '23CTACSB'), (3, 7, '20CTACSA'), (3, 7, '20CTACS5'), (3, 49, '20CTAIN7'), (3, 51, '20CHACSA'), (3, 51, '20CHACSB'), (3, 51, '20CHACSD'), (3, 57, '2TCTACS6'), (3, 57, '20CTACS8'), (3, 57, '20CTACS1'), (3, 58, '2TCTAIN0'), (3, 61, '20CTAIN1'), (3, 61, '20CTAINI'), (3, 68, '20CTAGZD'), (3, 71, '20CTACSH'), (3, 71, '20CTACS2'), (3, 112, '2TCTACS4'), (3, 113, '2TCHACS0'), (3, 114, '2TCTACS2'), (3, 115, '2TCTACS3'), (3, 124, '20CTAGZB')
--('2','82','20SZ0206'),('4','74','20SZ0219'),('2','74','20SZ0219'),('4','74','20SZAGZ5'),('2','74','20SZAGZ5'),('4','82','20SZAHM3'),('2','82','20SZAHM3'),('4','82','20SZAHM5'),('2','82','20SZAHM5'),('4','74','20SZAHMG'),('2','74','20SZAHMG'),('4','74','20SZAHM1'),('2','74','20SZAHM1'),('4','74','20SZAHMF'),('2','74','20SZAHMF'),('4','72','20CTAHMX'),('2','72','20CTAHMX'),('4','72','20CTAHMY'),('2','72','20CTAHMY'),('4','72','20CTAHMZ'),('2','72','20CTAHMZ'),('4','74','20SZ0263'),('2','74','20SZ0263'),('4','72','20CTAIN0'),('2','72','20CTAIN0'),('4','73','20CTAINH'),('2','73','20CTAINH'),('4','72','20CTAINB'),('2','72','20CTAINB'),('2','11','20TAAHM7'),('2','12','20TAACS0'),('2','12','20TAAGY1'),('2','12','20TAAGZ0'),('2','12','20TAAGZ1'),('2','12','20TAAHM4')

--('2','11','20TAAHM7'),('2','12','20TAACS0'),('2','12','20TAAGY1'),('2','12','20TAAGZ0'),('2','12','20TAAGZ1'),('2','12','20TAAHM4')

('10','111','2TUGACS6'),('1','103','20CTAIND'),('1','103','20SZAGZ3'),('11','125','20HOACS1'),('11','125','20HOACS4'),('11','125','Z0HOACS8'),('11','125','Z0HOACS9'),('11','125','Z0HOACSA'),('11','125','Z0HOACSB'),('11','125','Z0HOACSC'),('11','125','Z0HOAGZ0'),('11','125','Z0HOAGZ1'),('11','125','Z0HOAGZ2'),('11','125','Z0HOAGZ3'),('1','1','20CTAGZ9'),('2','100','20NG0288'),('2','100','20NGACSG'),('2','100','20NGAHMB'),('2','101','20NG0238'),('2','101','20NGAGZ3'),('2','101','20NGAHM9'),('2','104','20NGACSO'),('2','11','20TAAHM7'),('2','11','20TAAHM8'),('2','11','20TAAHM9'),('2','11','20TAAHMA'),('2','11','20TAAHMB'),('2','116','20NGACSK'),('2','12','20TAACS0'),('2','12','20TAAGY1'),('2','12','20TAAGZ0'),('2','12','20TAAGZ1'),('2','12','20TAAHM4'),('2','13','20NG0201'),('2','13','20NGACS7'),('2','13','20NGACS9'),('2','13','20NGACSA'),('2','13','20NGACSB'),('2','13','20NGACSC'),('2','13','20NGAHM1'),('2','17','20SA0235'),('2','17','20SAACSB'),('2','17','20SAACSD'),('2','17','20SAACSE'),('2','17','20SAACSF'),('2','17','20SAACSG'),('2','17','20SAAHMJ'),('2','39','20NGACS0'),('2','39','20NGAGY1'),('2','39','20NGAGZ0'),('2','39','20NGAGZ1'),('2','39','20NGAHM6'),('2','40','20NGAHMJ'),('2','41','20NGAHMF'),('2','41','20NGAHMG'),('2','41','20NGAHMH'),('2','52','20SAACS0'),('2','52','20SAACS2'),('2','52','20SAAGY1'),('2','52','20SAAGZ0'),('2','52','20SAAGZ1'),('2','52','20SAAHMH'),('2','53','20SAAHML'),('2','53','20SAAHMM'),('2','53','20SAAHMN'),('2','53','20SAAHMO'),('2','72','20CTAHMX'),('2','72','20CTAHMY'),('2','72','20CTAHMZ'),('2','72','20CTAIN0'),('2','72','20CTAINB'),('2','73','20CTAINH'),('2','74','20SZ0219'),('2','74','20SZ0263'),('2','74','20SZAGZ5'),('2','74','20SZAHM1'),('2','74','20SZAHMF'),('2','74','20SZAHMG'),('2','78','20NG0279'),('2','78','20NGACSD'),('2','78','20NGAHMA'),('2','79','20SAACS8'),('2','79','20SAAHM9'),('2','79','20SAAHMA'),('2','79','20TA0211'),('2','79','20TA0319'),('2','79','20TAACS7'),('2','80','20TA0327'),('2','80','20TAAHM5'),('2','80','20TAAHM6'),('2','82','20SZ0206'),('2','82','20SZAHM3'),('2','82','20SZAHM5'),('2','91','20NG0249'),('2','91','20NGAHM5'),('2','91','20NGAHM8'),('2','95','20NG0302'),('2','95','20NGACSI'),('2','95','20NGAHMC'),('2','99','20NG0190'),('2','99','20NG0255'),('2','99','20NG0280'),('2','99','20NGACS8'),('2','99','20NGACSE'),('2','99','20NGAGZ2'),('3','105','20CTAHM8'),('3','105','20CTAHMP'),('3','105','20CTAHMQ'),('3','105','20CTAHMR'),('3','112','2TCTACS4'),('3','113','2TCHACS0'),('3','114','2TCTACS2'),('3','115','2TCTACS3'),('3','124','20CTAGZB'),('3','49','20CTAIN7'),('3','51','20CHACSA'),('3','51','20CHACSB'),('3','51','20CHACSC'),('3','51','20CHACSD'),('3','51','20CHACSE'),('3','57','20CTACS1'),('3','57','20CTACS8'),('3','57','20CTAGY2'),('3','57','20CTAGZ2'),('3','57','20CTAGZ5'),('3','57','2TCTACS6'),('3','58','2TCTAIN0'),('3','61','20CTAIN1'),('3','61','20CTAIN2'),('3','61','20CTAIN3'),('3','61','20CTAINI'),('3','68','20CTAGZD'),('3','71','20CTACS2'),('3','71','20CTACSH'),('3','71','20CTAGY3'),('3','71','20CTAGZ3'),('3','71','20CTAGZ6'),('3','7','20CTACS5'),('3','7','20CTACSA'),('3','7','20CTAGY4'),('3','7','20CTAGZ4'),('3','7','20CTAGZ7'),('3','7','23CTACSB'),('4','109','20SZACS7'),('4','117','20SZACSR'),('4','128','20SZ0401'),('4','128','2TCTAIN3'),('4','128','2TSZACS9'),('4','2','20SZACSS'),('4','2','20SZACST'),('4','2','20SZACSU'),('4','2','20SZACSV'),('4','2','20SZAHMX'),('4','3','20SZAHMW'),('4','3','20URAHMX'),('4','47','20SZ0192'),('4','47','20SZACS4'),('4','47','20SZACS8'),('4','47','20SZACS9'),('4','47','20SZACSA'),('4','47','20SZACSB'),('4','47','20SZAHMB'),('4','47','20SZAHMD'),('4','47','20SZAHME'),('4','47','20SZAHMK'),('4','48','20SZAHMV'),('4','48','2TSZACS1'),('4','54','20SZACS0'),('4','54','20SZAGY1'),('4','54','20SZAGZ0'),('4','54','20SZAGZ1'),('4','54','20SZAHMH'),('4','54','20SZAHMU'),('4','55','20SZAHMQ'),('4','55','20SZAHMR'),('4','55','20SZAHMS'),('4','59','20SZACSM'),('4','62','20SZAIO2'),('4','72','20CTAHMX'),('4','72','20CTAHMY'),('4','72','20CTAHMZ'),('4','72','20CTAIN0'),('4','72','20CTAINB'),('4','73','20CTAINH'),('4','74','20SZ0219'),('4','74','20SZ0263'),('4','74','20SZAGZ5'),('4','74','20SZAHM1'),('4','74','20SZAHMF'),('4','74','20SZAHMG'),('4','77','20SZ0204'),('4','77','20SZACS2'),('4','81','20SZ0345'),('4','81','20SZ0347'),('4','81','20SZAHMO'),('4','81','2TSZACS2'),('4','82','20SZ0206'),('4','82','20SZAHM3'),('4','82','20SZAHM5'),('4','84','20SZAHMM'),('4','84','2TSZACS0'),('4','85','20SZAHMP'),('4','85','2TCTACS5'),('4','86','20SZACSG'),('4','86','20SZACSJ'),('4','86','20SZAGZ4'),('4','86','20SZAHMN'),('4','87','20SZACSQ'),('4','87','2TSZACS3'),('4','88','20SZ0203'),('4','88','20SZAHM7'),('4','88','20SZAHM8'),('4','88','20SZAHM9'),('4','88','20SZAHMA'),('5','106','20SBACS6'),('5','127','20SBACSD'),('5','42','2TSBACS0'),('5','42','2TSBACS1'),('5','43','20SB0214'),('5','43','20SB0226'),('5','43','20SBACS0'),('5','43','20SBACS2'),('5','43','20SBACS3'),('5','43','20SBACS4'),('5','43','20SBACS5'),('5','43','20SBAHM0'),('5','43','20SBAHM8'),('5','43','20SBAHMB'),('5','60','20SBAHMG'),('5','83','20SB0314'),('5','83','20SBACS9'),('5','83','20SBACSA'),('5','83','20SBACSC'),('5','83','20SBAHMD'),('5','83','2TSBACS2'),('5','90','20SB0215'),('5','90','20SBACS1'),('5','90','20SBAHM4'),('5','90','20SBAHM5'),('5','90','20SBAHM6'),('5','90','20SBAHM7'),('6','10','20URACS0'),('6','10','20URAGY1'),('6','10','20URAGZ0'),('6','10','20URAGZ3'),('6','10','20URAHM9'),('6','10','20URAHMG'),('6','102','20UR0287'),('6','102','20URACSH'),('6','102','20URACSN'),('6','102','20URACSO'),('6','102','20URAHMF'),('6','107','20URACSL'),('6','118','20URAHM7'),('6','45','20SA0181'),('6','45','20SA0189'),('6','45','20SAACS7'),('6','45','20SAAHM0'),('6','45','20SAAHM1'),('6','45','20SAAHM2'),('6','45','20SAAHM3'),('6','45','20SAAHM5'),('6','45','20SAAHM8'),('6','45','20SAAHMI'),('6','46','20URACS3'),('6','46','20URAGY3'),('6','46','20URAGZ2'),('6','46','20URAGZ5'),('6','46','20URAHMB'),('6','63','20URACS9'),('6','63','20URACSA'),('6','63','20URACSB'),('6','63','20URACSC'),('6','63','20URAHMD'),('6','64','20URAIN1'),('6','64','20URAIN2'),('6','65','20URAHM8'),('6','66','20TA0213'),('6','66','20TAACS3'),('6','66','20TAAHM0'),('6','66','20URACSD'),('6','66','20URACSE'),('6','66','20URACSF'),('6','66','20URACSG'),('6','66','20URAHME'),('6','67','20URAHMN'),('6','67','20URAHMO'),('6','69','20URACS1'),('6','69','20URAGY2'),('6','69','20URAGZ1'),('6','69','20URAGZ4'),('6','69','20URAHMA'),('6','70','2TURAHM0'),('6','70','2TURAHM1'),('6','70','2TURAHM2'),('6','8','20URAHMH'),('6','8','20URAHMI'),('6','8','2TURACS1'),('6','89','20URACSZ'),('6','89','20URAHM4'),('6','89','20URAHM6'),('6','9','20URAHMT'),('6','9','20URAHMU'),('6','9','20URAHMV'),('6','9','20URAHMW'),('6','98','20UR0205'),('6','98','20URACS8'),('6','98','20URAHM0'),('6','98','20URAHM1'),('6','98','20URAHM2'),('6','98','20URAHM3'),('6','98','20URAHMC'),('6','98','2TURAHM3'),('7','110','20CHACSF'),('7','119','20CHACSG'),('7','18','2TUGACS0'),('7','18','2TUGACS1'),('7','18','2TUGACS3'),('7','4','20CHACS0'),('7','4','20CHAGY1'),('7','4','20CHAGZ0'),('7','4','20CHAGZ1'),('7','4','20CHAHM1'),('7','44','20CHACS6'),('7','44','20CHACS7'),('7','44','20CHACS8'),('7','44','20CHACS9'),('7','44','20SAAGZ2'),('7','44','20SAAGZ3'),('7','44','20UGAHM9'),('7','5','20CHAHM9'),('7','5','20CHAHMA'),('7','6','20CHAHM5'),('7','6','20CHAHM7'),('8','108','20UGACSО'),('8','120','20UGAHM8'),('8','14','20UG0305'),('8','14','20UGACS4'),('8','14','20UGAGZ2'),('8','14','20UGAGZ4'),('8','14','20UGAGZ5'),('8','14','20UGAGZ6'),('8','14','20UGAHMB'),('8','14','20UGAHMF'),('8','15','20UGAHMY'),('8','15','20UGAHMZ'),('8','15','2TUGACS7'),('8','16','20UGAHMJ'),('8','16','20UGAHMK'),('8','16','20UGAHML'),('8','38','20UGAIN0'),('8','38','20UGAIN1'),('8','38','20UGAIN2'),('8','38','20UGAIN3'),('8','38','20UGAIN4'),('8','50','20UGACS0'),('8','50','20UGAGY1'),('8','50','20UGAGZ0'),('8','50','20UGAGZ1'),('8','50','20UGAHMA'),('8','56','20SA0212'),('8','56','20SAACSK'),('8','56','20SAACSL'),('8','56','20SAACSN'),('8','56','20SAACSO'),('8','56','20SAAHMD'),('8','56','20SAAHMF'),('8','56','20SAAHMG'),('8','75','20UG0208'),('8','75','20UGACS6'),('8','75','20UGAGZ8'),('8','75','20UGAHM2'),('8','75','20UGAHM3'),('8','75','20UGAHM6'),('8','76','20UG0209'),('8','76','20UG0295'),('8','76','20UGACS5'),('8','76','20UGACSI'),('8','76','20UGACSL'),('8','76','20UGACSN'),('8','76','20UGAHM0'),('8','76','20UGAHM1'),('8','92','20UG0260'),('8','92','20UGACS7'),('8','92','20UGACS8'),('8','92','20UGACSJ'),('8','92','20UGACSK'),('8','92','20UGACSM'),('8','93','20UGAHMG'),('8','94','20SAACS9'),('8','96','20UG0352'),('8','96','20UGAHMN'),('8','96','20UGAHMO'),('8','96','20UGAHMP'),('8','96','20UGAHMQ'),('9','121','2TCTAIN1'),('9','122','20CTAHM5'),('9','122','20SZACSD'),('9','122','20SZACSN'),('9','122','20SZACSO'),('9','122','20SZACSP'),('9','123','20CTAGZ1'),('9','123','20HOACS5'),('9','126','20HOACS3'),('9','19','20CTAINE'),('9','20','20CTAHM3'),('9','20','20CTAHM9'),('9','20','20CTAIN5'),('9','20','20NGACSJ'),('9','21','2TSZACS4'),('9','21','2TSZACS5'),('9','22','20URACSI'),('9','22','20URACSJ'),('9','22','20URACSK'),('9','22','2TURACS0'),('9','23','20URACSR'),('9','23','20URACSY'),('9','24','20TAACS4'),('9','24','20TAACS5'),('9','24','20TAACS6'),('9','24','20TAACS8'),('9','24','20TAACS9'),('9','25','20TAAHMC'),('9','25','2TTAACS0'),('9','26','20UGACS9'),('9','26','20UGACSA'),('9','26','20UGACSB'),('9','26','20UGACSE'),('9','26','20UGACSF'),('9','26','20UGACSH'),('9','26','20UGACSV'),('9','26','20UGACSX'),('9','27','20CHACS2'),('9','27','20CTACS0'),('9','27','20HOACS0'),('9','27','20NGACS1'),('9','27','20SAACS1'),('9','27','20TAACS1'),('9','27','20UGACS1'),('9','27','20URACS2'),('9','28','20NGAHMK'),('9','28','2TNGACS0'),('9','29','20URACSS'),('9','30','20UGACSC'),('9','30','20UGACSD'),('9','30','20UGACSG'),('9','30','20UGACSW'),('9','31','20SAACSH'),('9','31','20SAACSI'),('9','31','20SAACSJ'),('9','31','20SAACSP'),('9','31','20SAACSQ'),('9','32','20SAAHMP'),('9','32','2TSAACS0'),('9','33','20UGAHMH'),('9','33','20UGAHMU'),('9','34','20URAHMK'),('9','34','20URAHML'),('9','34','20URAHMP'),('9','35','20CTAGY1'),('9','35','20CTAGZ0'),('9','35','20CTAGZC'),('9','35','20CTAHM4'),('9','35','20CTAHM6'),('9','35','20CTAHMA'),('9','35','20CTAHMH'),('9','35','20CTAHML'),('9','35','20CTAHMM'),('9','35','20CTAHMS'),('9','36','20CTAINJ'),('9','36','2TCTACS0'),('9','37','20URACSU'),('9','37','20URACSV'),('9','37','20URAIN3'),('9','97','20SZAGZ6')